import os
import logging
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Determine environment
ENV = os.getenv("ENV", "development")  # Default to development if not specified

# Bot configuration and MySQL config based on environment
if ENV == "production":
    # Use production credentials
    BOT_TOKEN = os.getenv("PROD_BOT_TOKEN")
    CHANNEL_ID = os.getenv("PROD_CHANNEL_ID")
    DATABASE_FILE = "gigsta_prod.db"
    GEMINI_API_KEY = os.getenv("PROD_GEMINI_API_KEY")
    MIXPANEL_TOKEN = os.getenv("PROD_MIXPANEL_TOKEN")
    ROLLBAR_TOKEN = os.getenv("PROD_ROLLBAR_TOKEN")
    MYSQL_DATABASE = "gigsta_prod"
else:
    # Use development credentials
    BOT_TOKEN = os.getenv("DEV_BOT_TOKEN")
    CHANNEL_ID = os.getenv("DEV_CHANNEL_ID")
    DATABASE_FILE = "gigsta_dev.db"
    GEMINI_API_KEY = os.getenv("DEV_GEMINI_API_KEY")
    MIXPANEL_TOKEN = os.getenv("DEV_MIXPANEL_TOKEN")
    ROLLBAR_TOKEN = os.getenv("DEV_ROLLBAR_TOKEN")
    MYSQL_DATABASE = "gigsta_dev"

MYSQL_CONFIG = {
    "host": os.getenv("MYSQLHOST"),
    "port": int(os.getenv("MYSQLPORT")),
    "user": os.getenv("MYSQLUSER"),
    "password": os.getenv("MYSQLPASSWORD"),
    "database": MYSQL_DATABASE,
}

def get_mysql_config():
    """Get the appropriate MySQL config for the current environment"""
    return MYSQL_CONFIG


# Feature flags
FEATURES = {
    "development": {
        "test_mode": True,
        "debug_logging": True,
        "short_intervals": True,  # For testing with shorter intervals
    },
    "production": {
        "test_mode": False,
        "debug_logging": False,
        "short_intervals": False,
    }
}

def is_feature_enabled(feature_name):
    """Check if a feature is enabled in the current environment"""
    return FEATURES.get(ENV, {}).get(feature_name, False)

def get_database_file():
    """Get the appropriate database file for the current environment"""
    return DATABASE_FILE
