services:
  # Define the Telegram bot service
  - type: web
    name: gigsta-bot
    env: docker
    dockerfilePath: ./Dockerfile
    dockerContext: .
    plan: free
    healthCheckPath: /
    disk:
      name: bot-data
      mountPath: /app/data
      sizeGB: 1
    envVars:
      - key: BOT_TOKEN
        sync: false # This means you'll set this manually in the Render dashboard
      - key: GEMINI_API_KEY
        sync: false
      - key: MIXPANEL_TOKEN
        sync: false
      - key: ROLLBAR_TOKEN
        sync: false
      - key: CHANNEL_ID
        sync: false
      - key: ENV
        value: production
    autoDeploy: true # Automatic deployments on push to the connected repository
    scaling:
      minInstances: 1
      maxInstances: 1
    buildCommand: null # Uses the commands in Dockerfile
    startCommand: null # Uses the command in Dockerfile
