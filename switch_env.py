#!/usr/bin/env python3
"""
Environment Switcher for Gigsta Job Notifier Bot
This script allows you to easily switch between development and production environments
by modifying the ENV variable in your .env file.
"""

import os
import sys
import re
from dotenv import load_dotenv, set_key

def switch_environment(env_name):
    """Switch the environment in the .env file"""
    if env_name not in ["development", "production"]:
        print(f"Error: Invalid environment '{env_name}'. Use 'development' or 'production'.")
        return False
    
    # Check if .env exists
    if not os.path.exists(".env"):
        print("Error: .env file not found.")
        return False
    
    # Load current .env file
    load_dotenv()
    
    # Update the ENV variable
    set_key(".env", "ENV", env_name)
    
    print(f"Switched to {env_name.upper()} environment")
    return True

def show_current_environment():
    """Show the current environment setting"""
    # Check if .env exists
    if not os.path.exists(".env"):
        print("Error: .env file not found.")
        return False
    
    # Load current .env file
    load_dotenv()
    env = os.getenv("ENV", "development")
    
    print(f"Current environment: {env.upper()}")
    
    # Show database file that will be used
    if env == "production":
        db_file = "gigsta_prod.db"
    else:
        db_file = "gigsta_dev.db"
    
    print(f"Using database: {db_file}")
    
    # Check if the database file exists
    if os.path.exists(db_file):
        print(f"Database status: EXISTS")
    else:
        print(f"Database status: NOT FOUND (will be created when the bot runs)")
    
    # Show token status
    dev_token = os.getenv("DEV_BOT_TOKEN")
    prod_token = os.getenv("PROD_BOT_TOKEN")
    
    if env == "development":
        if dev_token:
            print("Development token: CONFIGURED")
        else:
            print("Development token: NOT CONFIGURED")
    else:
        if prod_token:
            print("Production token: CONFIGURED")
        else:
            print("Production token: NOT CONFIGURED")

if __name__ == "__main__":
    if len(sys.argv) < 2:
        show_current_environment()
        print("\nUsage:")
        print("  python switch_env.py [development|production]  - Switch environment")
        print("  python switch_env.py status                   - Show current environment")
    elif sys.argv[1].lower() in ["development", "dev", "d"]:
        switch_environment("development")
    elif sys.argv[1].lower() in ["production", "prod", "p"]:
        switch_environment("production")
    elif sys.argv[1].lower() in ["status", "s", "current"]:
        show_current_environment()
    else:
        print(f"Unknown command: {sys.argv[1]}")
        print("Use 'development', 'production', or 'status'")
