/**
 * Main Application Controller for AI Resume Validator
 * Orchestrates all functionality and manages application state
 */

class AIResumeValidatorApp {
    constructor() {
        this.state = {
            resumeFile: null,
            jobText: '',
            jobImage: null,
            jsonData: null,
            currentJobInputMode: 'text', // 'text' or 'image'
            validationResults: null,
            isValidating: false
        };
        
        this.elements = {};
        this.eventListeners = [];
    }

    /**
     * Initializes the application
     */
    async init() {
        try {
            console.log('Initializing AI Resume Validator...');
            
            // Wait for DOM to be ready
            if (document.readyState === 'loading') {
                await new Promise(resolve => document.addEventListener('DOMContentLoaded', resolve));
            }
            
            // Get DOM elements
            this.initializeElements();
            
            // Set up event listeners
            this.setupEventListeners();
            
            // Initialize components
            this.initializeComponents();
            
            // Check API configuration
            this.checkApiConfiguration();
            
            console.log('AI Resume Validator initialized successfully');
            
        } catch (error) {
            console.error('Failed to initialize application:', error);
            ErrorHandler.handle(error, 'Application Initialization', true);
        }
    }

    /**
     * Initializes DOM element references
     */
    initializeElements() {
        const elementIds = [
            'resumeFile', 'resumeUploadArea', 'resumeUploadPlaceholder', 'resumePreview',
            'resumeFileName', 'resumeFileSize', 'resumeFileType', 'removeResumeFile',
            'textInputBtn', 'imageInputBtn', 'jobTextMode', 'jobImageMode',
            'jobText', 'jobTextCount', 'clearJobText', 
            'jobImage', 'jobImageUploadArea', 'jobImageUploadPlaceholder', 'jobImagePreview',
            'jobImageDisplay', 'jobImageName', 'removeJobImage',
            'jsonData', 'jsonStatus', 'jsonCharCount', 'formatJsonBtn', 'validateJsonBtn', 'clearJsonBtn',
            'validateBtn', 'clearAllBtn', 'loadingSpinner',
            'validationRequirements', 'req-resume', 'req-job', 'req-json',
            'resultsSection', 'resultsContainer',
            'configModal', 'closeConfigModal', 'apiKeyInput', 'saveConfig', 'cancelConfig'
        ];

        elementIds.forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                this.elements[id] = element;
            } else {
                console.warn(`Element with ID '${id}' not found`);
            }
        });
    }

    /**
     * Sets up all event listeners
     */
    setupEventListeners() {
        // Resume file upload
        if (this.elements.resumeFile) {
            this.addEventListener(this.elements.resumeFile, 'change', this.handleResumeFileChange.bind(this));
        }

        // Job input mode toggle
        if (this.elements.textInputBtn) {
            this.addEventListener(this.elements.textInputBtn, 'click', () => this.switchJobInputMode('text'));
        }
        if (this.elements.imageInputBtn) {
            this.addEventListener(this.elements.imageInputBtn, 'click', () => this.switchJobInputMode('image'));
        }

        // Job text input
        if (this.elements.jobText) {
            const debouncedJobTextChange = UIUtils.debounce(this.handleJobTextChange.bind(this), 500);
            this.addEventListener(this.elements.jobText, 'input', debouncedJobTextChange);
            this.addEventListener(this.elements.jobText, 'paste', debouncedJobTextChange);
        }

        // Job text controls
        if (this.elements.clearJobText) {
            this.addEventListener(this.elements.clearJobText, 'click', this.clearJobText.bind(this));
        }

        // Job image upload
        if (this.elements.jobImage) {
            this.addEventListener(this.elements.jobImage, 'change', this.handleJobImageChange.bind(this));
        }

        // JSON data input
        if (this.elements.jsonData) {
            const debouncedJsonChange = UIUtils.debounce(this.handleJsonDataChange.bind(this), 500);
            this.addEventListener(this.elements.jsonData, 'input', debouncedJsonChange);
            this.addEventListener(this.elements.jsonData, 'paste', debouncedJsonChange);
        }

        // JSON controls
        if (this.elements.formatJsonBtn) {
            this.addEventListener(this.elements.formatJsonBtn, 'click', this.formatJson.bind(this));
        }
        if (this.elements.validateJsonBtn) {
            this.addEventListener(this.elements.validateJsonBtn, 'click', this.validateJsonStructure.bind(this));
        }
        if (this.elements.clearJsonBtn) {
            this.addEventListener(this.elements.clearJsonBtn, 'click', this.clearJsonData.bind(this));
        }

        // Main actions
        if (this.elements.validateBtn) {
            this.addEventListener(this.elements.validateBtn, 'click', this.performValidation.bind(this));
        }
        if (this.elements.clearAllBtn) {
            this.addEventListener(this.elements.clearAllBtn, 'click', this.clearAllData.bind(this));
        }

        // Configuration modal
        if (this.elements.closeConfigModal) {
            this.addEventListener(this.elements.closeConfigModal, 'click', this.closeConfigModal.bind(this));
        }
        if (this.elements.saveConfig) {
            this.addEventListener(this.elements.saveConfig, 'click', this.saveApiConfiguration.bind(this));
        }
        if (this.elements.cancelConfig) {
            this.addEventListener(this.elements.cancelConfig, 'click', this.closeConfigModal.bind(this));
        }

        // Global keyboard shortcuts
        this.addEventListener(document, 'keydown', this.handleKeyboardShortcuts.bind(this));

        // Window beforeunload (prevent accidental data loss)
        this.addEventListener(window, 'beforeunload', this.handleBeforeUnload.bind(this));
    }

    /**
     * Helper to add event listener and track for cleanup
     * @param {Element} element - Target element
     * @param {string} event - Event name
     * @param {Function} handler - Event handler
     */
    addEventListener(element, event, handler) {
        element.addEventListener(event, handler);
        this.eventListeners.push({ element, event, handler });
    }

    /**
     * Initializes components
     */
    initializeComponents() {
        // Setup drag and drop for file uploads
        if (this.elements.resumeUploadArea) {
            fileHandler.setupDragAndDrop(this.elements.resumeUploadArea, this.handleResumeFileDrop.bind(this));
        }
        
        if (this.elements.jobImageUploadArea) {
            fileHandler.setupDragAndDrop(this.elements.jobImageUploadArea, this.handleJobImageDrop.bind(this));
        }

        // Initialize tooltips or help text
        this.initializeHelpers();

        // Check validation requirements
        this.checkValidationRequirements();
    }

    /**
     * Initializes helper tooltips and information
     */
    initializeHelpers() {
        // Add any tooltips or contextual help here
        // This could include format examples, validation tips, etc.
    }

    /**
     * Checks API configuration and prompts if needed
     */
    checkApiConfiguration() {
        if (!resumeValidator.hasApiKey()) {
            setTimeout(() => {
                if (confirm('API key not configured. Would you like to set it up now?')) {
                    this.openConfigModal();
                }
            }, 1000);
        }
    }

    /**
     * Handles resume file selection
     * @param {Event} event - File input change event
     */
    async handleResumeFileChange(event) {
        const file = event.target.files[0];
        if (file) {
            await this.processResumeFile(file);
        }
    }

    /**
     * Handles resume file drop
     * @param {File} file - Dropped file
     */
    async handleResumeFileDrop(file) {
        this.elements.resumeFile.files = new DataTransfer().files;
        await this.processResumeFile(file);
    }

    /**
     * Processes resume file
     * @param {File} file - Resume file
     */
    async processResumeFile(file) {
        try {
            const processedFile = await fileHandler.processResumeFile(file);
            this.state.resumeFile = processedFile;
            
            // Update UI
            this.updateResumePreview(processedFile);
            this.checkValidationRequirements();
            
            NotificationManager.show(`Resume file "${file.name}" processed successfully`, 'success');
            
        } catch (error) {
            this.clearResumeFile();
            // Error already handled in fileHandler
        }
    }

    /**
     * Updates resume file preview
     * @param {Object} processedFile - Processed file data
     */
    updateResumePreview(processedFile) {
        const preview = fileHandler.createFilePreview(processedFile, 'resume');
        
        if (this.elements.resumePreview) {
            this.elements.resumePreview.innerHTML = preview.html;
            this.elements.resumePreview.style.display = 'block';
        }
        
        if (this.elements.resumeUploadPlaceholder) {
            this.elements.resumeUploadPlaceholder.style.display = 'none';
        }
    }

    /**
     * Clears resume file
     */
    clearResumeFile() {
        this.state.resumeFile = null;
        fileHandler.removeFile('resume');
        this.checkValidationRequirements();
    }

    /**
     * Switches job input mode
     * @param {string} mode - 'text' or 'image'
     */
    switchJobInputMode(mode) {
        this.state.currentJobInputMode = mode;
        
        // Update button states
        if (this.elements.textInputBtn) {
            this.elements.textInputBtn.classList.toggle('active', mode === 'text');
        }
        if (this.elements.imageInputBtn) {
            this.elements.imageInputBtn.classList.toggle('active', mode === 'image');
        }
        
        // Show/hide input modes
        if (this.elements.jobTextMode) {
            this.elements.jobTextMode.style.display = mode === 'text' ? 'block' : 'none';
        }
        if (this.elements.jobImageMode) {
            this.elements.jobImageMode.style.display = mode === 'image' ? 'block' : 'none';
        }
        
        // Clear other mode data
        if (mode === 'text') {
            this.clearJobImage();
        } else {
            this.clearJobText();
        }
        
        this.checkValidationRequirements();
    }

    /**
     * Handles job text input changes
     * @param {Event} event - Input event
     */
    handleJobTextChange(event) {
        this.state.jobText = event.target.value.trim();
        
        // Update character count
        if (this.elements.jobTextCount) {
            this.elements.jobTextCount.textContent = `${event.target.value.length} characters`;
        }
        
        this.checkValidationRequirements();
    }

    /**
     * Clears job text
     */
    clearJobText() {
        this.state.jobText = '';
        if (this.elements.jobText) {
            this.elements.jobText.value = '';
        }
        if (this.elements.jobTextCount) {
            this.elements.jobTextCount.textContent = '0 characters';
        }
        this.checkValidationRequirements();
    }

    /**
     * Handles job image selection
     * @param {Event} event - File input change event
     */
    async handleJobImageChange(event) {
        const file = event.target.files[0];
        if (file) {
            await this.processJobImage(file);
        }
    }

    /**
     * Handles job image drop
     * @param {File} file - Dropped file
     */
    async handleJobImageDrop(file) {
        await this.processJobImage(file);
    }

    /**
     * Processes job image file
     * @param {File} file - Image file
     */
    async processJobImage(file) {
        try {
            const processedFile = await fileHandler.processJobImage(file);
            this.state.jobImage = processedFile;
            
            // Update UI
            this.updateJobImagePreview(processedFile);
            this.checkValidationRequirements();
            
            NotificationManager.show(`Job image "${file.name}" processed successfully`, 'success');
            
        } catch (error) {
            this.clearJobImage();
            // Error already handled in fileHandler
        }
    }

    /**
     * Updates job image preview
     * @param {Object} processedFile - Processed file data
     */
    updateJobImagePreview(processedFile) {
        const previewHtml = fileHandler.createImagePreview(processedFile);
        
        if (this.elements.jobImagePreview) {
            this.elements.jobImagePreview.innerHTML = previewHtml;
            this.elements.jobImagePreview.style.display = 'block';
        }
        
        if (this.elements.jobImageUploadPlaceholder) {
            this.elements.jobImageUploadPlaceholder.style.display = 'none';
        }
    }

    /**
     * Clears job image
     */
    clearJobImage() {
        this.state.jobImage = null;
        fileHandler.removeFile('jobImage');
        this.checkValidationRequirements();
    }

    /**
     * Handles JSON data input changes
     * @param {Event} event - Input event
     */
    handleJsonDataChange(event) {
        const jsonText = event.target.value.trim();
        
        // Update character count
        if (this.elements.jsonCharCount) {
            this.elements.jsonCharCount.textContent = `${event.target.value.length} characters`;
        }
        
        // Validate JSON
        if (jsonText) {
            const validation = JSONUtils.validate(jsonText);
            this.state.jsonData = validation.isValid ? validation.data : null;
            this.updateJsonStatus(validation.isValid, validation.error);
        } else {
            this.state.jsonData = null;
            this.updateJsonStatus(false, 'No JSON data');
        }
        
        this.checkValidationRequirements();
    }

    /**
     * Updates JSON validation status
     * @param {boolean} isValid - Whether JSON is valid
     * @param {string} message - Status message
     */
    updateJsonStatus(isValid, message) {
        if (!this.elements.jsonStatus) return;
        
        this.elements.jsonStatus.textContent = isValid ? 'Valid JSON' : message || 'Invalid JSON';
        this.elements.jsonStatus.className = `json-status ${isValid ? 'valid' : 'invalid'}`;
    }

    /**
     * Formats JSON data
     */
    formatJson() {
        if (!this.elements.jsonData) return;
        
        try {
            const formatted = JSONUtils.format(this.elements.jsonData.value);
            this.elements.jsonData.value = formatted;
            NotificationManager.show('JSON formatted successfully', 'success');
            
            // Trigger change event
            this.elements.jsonData.dispatchEvent(new Event('input'));
        } catch (error) {
            NotificationManager.show('Failed to format JSON: ' + error.message, 'error');
        }
    }

    /**
     * Validates JSON structure
     */
    validateJsonStructure() {
        if (!this.state.jsonData) {
            NotificationManager.show('No JSON data to validate', 'warning');
            return;
        }
        
        const structureValidation = JSONUtils.validateResumeStructure(this.state.jsonData);
        
        if (structureValidation.isValid) {
            NotificationManager.show('JSON structure is valid', 'success');
        } else {
            const errorMessage = `JSON structure issues:\n${structureValidation.errors.join('\n')}`;
            NotificationManager.show(errorMessage, 'error');
        }
    }

    /**
     * Clears JSON data
     */
    clearJsonData() {
        this.state.jsonData = null;
        if (this.elements.jsonData) {
            this.elements.jsonData.value = '';
        }
        if (this.elements.jsonCharCount) {
            this.elements.jsonCharCount.textContent = '0 characters';
        }
        this.updateJsonStatus(false, 'No JSON data');
        this.checkValidationRequirements();
    }

    /**
     * Checks validation requirements and updates UI
     */
    checkValidationRequirements() {
        const requirements = {
            resume: !!this.state.resumeFile,
            job: !!(this.state.jobText || this.state.jobImage),
            json: !!this.state.jsonData
        };
        
        // Update requirement indicators
        Object.entries(requirements).forEach(([key, satisfied]) => {
            const element = this.elements[`req-${key}`];
            if (element) {
                element.classList.toggle('completed', satisfied);
            }
        });
        
        // Update validate button
        const allRequirementsMet = Object.values(requirements).every(Boolean);
        if (this.elements.validateBtn) {
            this.elements.validateBtn.disabled = !allRequirementsMet || this.state.isValidating;
        }
        
        return { requirements, allRequirementsMet };
    }

    /**
     * Performs the main validation
     */
    async performValidation() {
        try {
            // Check requirements
            const { allRequirementsMet } = this.checkValidationRequirements();
            if (!allRequirementsMet) {
                NotificationManager.show('Please complete all requirements before validation', 'warning');
                return;
            }
            
            // Check API key
            if (!resumeValidator.hasApiKey()) {
                this.openConfigModal();
                return;
            }
            
            this.state.isValidating = true;
            this.updateValidationUI(true);
            
            // Prepare data
            const resumeData = this.state.resumeFile;
            const jobData = {
                text: this.state.currentJobInputMode === 'text' ? this.state.jobText : null,
                image: this.state.currentJobInputMode === 'image' ? this.state.jobImage : null
            };
            const jsonData = this.state.jsonData;
            
            // Set validation start time
            resumeValidator.validationStartTime = Date.now();
            
            // Perform validation
            const results = await resumeValidator.validateAlignment(resumeData, jobData, jsonData);
            
            // Format results for display
            const formattedResults = resumeValidator.formatResultsForDisplay(results);
            this.state.validationResults = formattedResults;
            
            // Display results
            this.displayValidationResults(formattedResults);
            
        } catch (error) {
            console.error('Validation failed:', error);
            NotificationManager.show('Validation failed: ' + error.message, 'error');
            
        } finally {
            this.state.isValidating = false;
            this.updateValidationUI(false);
        }
    }

    /**
     * Updates UI during validation
     * @param {boolean} isValidating - Whether validation is in progress
     */
    updateValidationUI(isValidating) {
        if (this.elements.validateBtn) {
            if (isValidating) {
                UIUtils.showLoading(this.elements.validateBtn, 'Validating...');
            } else {
                UIUtils.hideLoading(this.elements.validateBtn, 'Validate Alignment');
            }
        }
    }

    /**
     * Displays validation results
     * @param {Object} results - Formatted validation results
     */
    displayValidationResults(results) {
        if (!this.elements.resultsContainer) return;
        
        // Generate results HTML
        const resultsHtml = this.generateResultsHTML(results);
        
        // Update results container
        this.elements.resultsContainer.innerHTML = resultsHtml;
        
        // Show results section
        if (this.elements.resultsSection) {
            this.elements.resultsSection.style.display = 'block';
            
            // Scroll to results
            setTimeout(() => {
                UIUtils.scrollToElement(this.elements.resultsSection, 20);
            }, 100);
        }
    }

    /**
     * Generates HTML for validation results
     * @param {Object} results - Validation results
     * @returns {string} - Results HTML
     */
    generateResultsHTML(results) {
        const { display, metadata, statistics } = results;
        
        return `
            <div class="result-summary">
                <div class="metric-card">
                    <div class="metric-value" style="color: ${display.scoreColor}">${display.formattedScore}</div>
                    <div class="metric-label">Alignment Score</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" style="color: ${display.statusClass === 'success' ? '#10b981' : '#f59e0b'}">${display.statusText}</div>
                    <div class="metric-label">Validation Status</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">${statistics.totalIssues}</div>
                    <div class="metric-label">Issues Found</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">${statistics.totalSuggestions}</div>
                    <div class="metric-label">Suggestions</div>
                </div>
            </div>
            
            ${display.categoryScores.length > 0 ? `
                <div class="category-scores">
                    <h3>Category Scores</h3>
                    <div class="score-grid">
                        ${display.categoryScores.map(category => `
                            <div class="score-item">
                                <div class="score-name">${category.name}</div>
                                <div class="score-value" style="color: ${category.color}">${category.score}/100</div>
                                <div class="score-label">${category.label}</div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            ` : ''}
            
            <div class="result-summary-text">
                <h3>Summary</h3>
                <p>${results.summary}</p>
            </div>
            
            ${results.issues.length > 0 ? `
                <div class="issues-section">
                    <h3>Issues (${results.issues.length})</h3>
                    <div class="issues-list">
                        ${results.issues.map(issue => `
                            <div class="issue-item issue-${issue.severity}">
                                <div class="issue-header">
                                    <span class="issue-severity severity-${issue.severity}">${issue.severity}</span>
                                    <span class="issue-category">${issue.category}</span>
                                </div>
                                <h4>${issue.title}</h4>
                                <p>${issue.description}</p>
                                ${issue.location ? `<small><strong>Location:</strong> ${issue.location}</small>` : ''}
                                ${issue.impact ? `<small><strong>Impact:</strong> ${issue.impact}</small>` : ''}
                            </div>
                        `).join('')}
                    </div>
                </div>
            ` : ''}
            
            ${results.suggestions.length > 0 ? `
                <div class="suggestions-section">
                    <h3>Suggestions (${results.suggestions.length})</h3>
                    <div class="suggestions-list">
                        ${results.suggestions.map(suggestion => `
                            <div class="suggestion-item">
                                <div class="suggestion-header">
                                    <span class="suggestion-priority priority-${suggestion.priority}">${suggestion.priority}</span>
                                    <span class="suggestion-category">${suggestion.category}</span>
                                </div>
                                <h4>${suggestion.title}</h4>
                                <p>${suggestion.description}</p>
                                ${suggestion.expectedImpact ? `<small><strong>Expected Impact:</strong> ${suggestion.expectedImpact}</small>` : ''}
                            </div>
                        `).join('')}
                    </div>
                </div>
            ` : ''}
            
            ${results.strengths && results.strengths.length > 0 ? `
                <div class="strengths-section">
                    <h3>Strengths</h3>
                    <ul class="strengths-list">
                        ${results.strengths.map(strength => `<li>${strength}</li>`).join('')}
                    </ul>
                </div>
            ` : ''}
            
            <div class="metadata-section">
                <details>
                    <summary>Validation Details</summary>
                    <div class="metadata-grid">
                        <div><strong>Validated At:</strong> ${DateUtils.format(metadata.validatedAt)}</div>
                        <div><strong>Processing Time:</strong> ${metadata.processingTime}ms</div>
                        <div><strong>Resume File:</strong> ${metadata.resumeFileName}</div>
                        <div><strong>Job Input Type:</strong> ${metadata.jobInputType}</div>
                        <div><strong>JSON Size:</strong> ${metadata.jsonCharacterCount} characters</div>
                    </div>
                </details>
            </div>
        `;
    }

    /**
     * Clears all data
     */
    clearAllData() {
        if (confirm('Are you sure you want to clear all data? This action cannot be undone.')) {
            // Clear state
            this.state.resumeFile = null;
            this.state.jobText = '';
            this.state.jobImage = null;
            this.state.jsonData = null;
            this.state.validationResults = null;
            
            // Clear file handler
            fileHandler.clearAll();
            
            // Clear UI
            this.clearJobText();
            this.clearJsonData();
            
            // Hide results
            if (this.elements.resultsSection) {
                this.elements.resultsSection.style.display = 'none';
            }
            
            // Reset to text mode
            this.switchJobInputMode('text');
            
            this.checkValidationRequirements();
            
            NotificationManager.show('All data cleared', 'info');
        }
    }

    /**
     * Opens configuration modal
     */
    openConfigModal() {
        if (this.elements.configModal) {
            this.elements.configModal.classList.add('active');
            this.elements.configModal.style.display = 'flex';
        }
        
        // Focus on API key input
        if (this.elements.apiKeyInput) {
            setTimeout(() => this.elements.apiKeyInput.focus(), 100);
        }
    }

    /**
     * Closes configuration modal
     */
    closeConfigModal() {
        if (this.elements.configModal) {
            this.elements.configModal.classList.remove('active');
            setTimeout(() => {
                this.elements.configModal.style.display = 'none';
            }, 300);
        }
    }

    /**
     * Saves API configuration
     */
    saveApiConfiguration() {
        const apiKey = this.elements.apiKeyInput?.value?.trim();
        
        if (!apiKey) {
            NotificationManager.show('Please enter a valid API key', 'warning');
            return;
        }
        
        try {
            resumeValidator.setApiKey(apiKey);
            this.closeConfigModal();
            NotificationManager.show('API key saved successfully', 'success');
            
            // Clear the input for security
            if (this.elements.apiKeyInput) {
                this.elements.apiKeyInput.value = '';
            }
            
        } catch (error) {
            NotificationManager.show('Failed to save API key: ' + error.message, 'error');
        }
    }

    /**
     * Handles keyboard shortcuts
     * @param {KeyboardEvent} event - Keyboard event
     */
    handleKeyboardShortcuts(event) {
        // Ctrl/Cmd + Enter: Validate
        if ((event.ctrlKey || event.metaKey) && event.key === 'Enter') {
            event.preventDefault();
            if (!this.state.isValidating) {
                this.performValidation();
            }
        }
        
        // Escape: Close modal
        if (event.key === 'Escape') {
            this.closeConfigModal();
        }
        
        // Ctrl/Cmd + K: Clear all
        if ((event.ctrlKey || event.metaKey) && event.key === 'k') {
            event.preventDefault();
            this.clearAllData();
        }
    }

    /**
     * Handles before page unload
     * @param {BeforeUnloadEvent} event - Before unload event
     */
    handleBeforeUnload(event) {
        if (this.state.resumeFile || this.state.jobText || this.state.jsonData) {
            event.preventDefault();
            event.returnValue = 'You have unsaved data. Are you sure you want to leave?';
            return event.returnValue;
        }
    }

    /**
     * Cleanup method
     */
    destroy() {
        // Remove all event listeners
        this.eventListeners.forEach(({ element, event, handler }) => {
            element.removeEventListener(event, handler);
        });
        
        this.eventListeners = [];
    }
}

// Initialize application when DOM is ready
let app;

if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initApp);
} else {
    initApp();
}

function initApp() {
    app = new AIResumeValidatorApp();
    app.init();
    
    // Make app globally available for debugging
    window.app = app;
}

// Handle page unload
window.addEventListener('beforeunload', () => {
    if (app) {
        app.destroy();
    }
});