/**
 * Utility Functions for AI Resume Validator
 * Contains common helper functions used throughout the application
 */

// Configuration Constants
const CONFIG = {
    MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
    SUPPORTED_RESUME_FORMATS: {
        'application/pdf': '.pdf',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document': '.docx',
        'image/png': '.png',
        'image/jpeg': '.jpg,.jpeg',
        'image/jpg': '.jpg'
    },
    SUPPORTED_JOB_FORMATS: {
        'image/png': '.png',
        'image/jpeg': '.jpg,.jpeg',
        'image/jpg': '.jpg'
    },
    API_ENDPOINT: 'https://generativelanguage.googleapis.com/v1beta/models/',
    MODEL_NAME: 'gemini-2.5-pro',
    STORAGE_KEYS: {
        API_KEY: 'ai_resume_validator_api_key',
        SETTINGS: 'ai_resume_validator_settings'
    }
};

// JSON Utilities
const JSONUtils = {
    /**
     * Validates if a string is valid JSON
     * @param {string} jsonString - The JSON string to validate
     * @returns {Object} - { isValid: boolean, data?: object, error?: string }
     */
    validate(jsonString) {
        if (!jsonString || typeof jsonString !== 'string') {
            return { isValid: false, error: 'No JSON data provided' };
        }

        try {
            const data = JSON.parse(jsonString.trim());
            return { isValid: true, data };
        } catch (error) {
            return { isValid: false, error: error.message };
        }
    },

    /**
     * Formats JSON string with proper indentation
     * @param {string} jsonString - The JSON string to format
     * @returns {string} - Formatted JSON string
     */
    format(jsonString) {
        try {
            const data = JSON.parse(jsonString);
            return JSON.stringify(data, null, 2);
        } catch (error) {
            throw new Error('Invalid JSON: ' + error.message);
        }
    },

    /**
     * Validates resume JSON structure
     * @param {Object} data - The parsed JSON data
     * @returns {Object} - { isValid: boolean, errors: string[] }
     */
    validateResumeStructure(data) {
        const errors = [];
        
        if (!data || typeof data !== 'object') {
            return { isValid: false, errors: ['Invalid data structure'] };
        }

        // Check if data is wrapped in structuredData
        let resumeData = data;
        if (data.structuredData && typeof data.structuredData === 'object') {
            resumeData = data.structuredData;
        }

        // Check required fields
        const requiredFields = ['personalInfo', 'experiences', 'education', 'skills'];
        requiredFields.forEach(field => {
            if (!resumeData[field]) {
                errors.push(`Missing required field: ${field}`);
            }
        });

        // Validate personalInfo structure
        if (resumeData.personalInfo) {
            const requiredPersonalFields = ['fullName', 'email'];
            requiredPersonalFields.forEach(field => {
                if (!resumeData.personalInfo[field]) {
                    errors.push(`Missing personalInfo.${field}`);
                }
            });
        }

        // Validate arrays
        ['experiences', 'education'].forEach(field => {
            if (resumeData[field] && !Array.isArray(resumeData[field])) {
                errors.push(`${field} must be an array`);
            }
        });

        return { isValid: errors.length === 0, errors };
    }
};

// File Utilities
const FileUtils = {
    /**
     * Validates file type and size
     * @param {File} file - The file to validate
     * @param {string} type - 'resume' or 'job'
     * @returns {Object} - { isValid: boolean, error?: string }
     */
    validate(file, type) {
        if (!file) {
            return { isValid: false, error: 'No file provided' };
        }

        const supportedFormats = type === 'resume' 
            ? CONFIG.SUPPORTED_RESUME_FORMATS 
            : CONFIG.SUPPORTED_JOB_FORMATS;

        if (!supportedFormats[file.type]) {
            const allowedTypes = Object.values(supportedFormats).join(', ');
            return { 
                isValid: false, 
                error: `Unsupported file type. Allowed: ${allowedTypes}` 
            };
        }

        if (file.size > CONFIG.MAX_FILE_SIZE) {
            return { 
                isValid: false, 
                error: `File too large. Maximum size: ${this.formatFileSize(CONFIG.MAX_FILE_SIZE)}` 
            };
        }

        return { isValid: true };
    },

    /**
     * Converts file to base64 string
     * @param {File} file - The file to convert
     * @returns {Promise<string>} - Base64 string
     */
    async toBase64(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = () => {
                // Remove data:mime/type;base64, prefix
                const base64 = reader.result.split(',')[1];
                resolve(base64);
            };
            reader.onerror = reject;
            reader.readAsDataURL(file);
        });
    },

    /**
     * Formats file size in human-readable format
     * @param {number} bytes - File size in bytes
     * @returns {string} - Formatted file size
     */
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },

    /**
     * Gets file extension from filename
     * @param {string} filename - The filename
     * @returns {string} - File extension
     */
    getExtension(filename) {
        return filename.slice((filename.lastIndexOf(".") - 1 >>> 0) + 2);
    },

    /**
     * Gets appropriate icon for file type
     * @param {string} mimeType - File MIME type
     * @returns {string} - Icon character
     */
    getFileIcon(mimeType) {
        const iconMap = {
            'application/pdf': '📄',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document': '📝',
            'image/png': '🖼️',
            'image/jpeg': '🖼️',
            'image/jpg': '🖼️',
            'text/plain': '📝'
        };
        
        return iconMap[mimeType] || '📄';
    }
};

// UI Utilities
const UIUtils = {
    /**
     * Shows loading state on an element
     * @param {HTMLElement} element - The element to show loading on
     * @param {string} loadingText - Optional loading text
     */
    showLoading(element, loadingText = 'Loading...') {
        if (!element) return;
        
        element.disabled = true;
        element.classList.add('loading');
        
        const spinner = element.querySelector('.loading-spinner');
        if (spinner) {
            spinner.style.display = 'inline-block';
        }
        
        const btnText = element.querySelector('.btn-text');
        if (btnText) {
            btnText.textContent = loadingText;
        }
    },

    /**
     * Hides loading state on an element
     * @param {HTMLElement} element - The element to hide loading on
     * @param {string} originalText - Original button text
     */
    hideLoading(element, originalText = 'Validate Alignment') {
        if (!element) return;
        
        element.disabled = false;
        element.classList.remove('loading');
        
        const spinner = element.querySelector('.loading-spinner');
        if (spinner) {
            spinner.style.display = 'none';
        }
        
        const btnText = element.querySelector('.btn-text');
        if (btnText) {
            btnText.textContent = originalText;
        }
    },

    /**
     * Shows/hides an element with transition
     * @param {HTMLElement} element - The element to toggle
     * @param {boolean} show - Whether to show or hide
     */
    toggleElement(element, show) {
        if (!element) return;
        
        if (show) {
            element.style.display = 'block';
            // Force reflow
            element.offsetHeight;
            element.classList.add('show');
        } else {
            element.classList.remove('show');
            setTimeout(() => {
                element.style.display = 'none';
            }, 300);
        }
    },

    /**
     * Scrolls to an element smoothly
     * @param {HTMLElement} element - The element to scroll to
     * @param {number} offset - Optional offset from top
     */
    scrollToElement(element, offset = 0) {
        if (!element) return;
        
        const elementPosition = element.getBoundingClientRect().top;
        const offsetPosition = elementPosition + window.pageYOffset - offset;

        window.scrollTo({
            top: offsetPosition,
            behavior: "smooth"
        });
    },

    /**
     * Debounces a function call
     * @param {Function} func - The function to debounce
     * @param {number} wait - The wait time in milliseconds
     * @returns {Function} - Debounced function
     */
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
};

// Notification System
const NotificationManager = {
    container: null,

    /**
     * Initializes the notification system
     */
    init() {
        this.container = document.getElementById('notificationContainer');
        if (!this.container) {
            this.container = document.createElement('div');
            this.container.id = 'notificationContainer';
            this.container.className = 'notification-container';
            document.body.appendChild(this.container);
        }
    },

    /**
     * Shows a notification
     * @param {string} message - The notification message
     * @param {string} type - Notification type: 'success', 'error', 'warning', 'info'
     * @param {number} duration - Duration in milliseconds (0 for persistent)
     */
    show(message, type = 'info', duration = 5000) {
        if (!this.container) this.init();

        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <div class="notification-message">${message}</div>
                <button class="notification-close">&times;</button>
            </div>
        `;

        // Add close functionality
        const closeBtn = notification.querySelector('.notification-close');
        closeBtn.addEventListener('click', () => {
            this.remove(notification);
        });

        // Add to container
        this.container.appendChild(notification);

        // Trigger animation
        setTimeout(() => {
            notification.classList.add('show');
        }, 100);

        // Auto-remove if duration is set
        if (duration > 0) {
            setTimeout(() => {
                this.remove(notification);
            }, duration);
        }

        return notification;
    },

    /**
     * Removes a notification
     * @param {HTMLElement} notification - The notification element to remove
     */
    remove(notification) {
        if (!notification) return;

        notification.classList.remove('show');
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    },

    /**
     * Clears all notifications
     */
    clear() {
        if (!this.container) return;
        this.container.innerHTML = '';
    }
};

// Local Storage Utilities
const StorageUtils = {
    /**
     * Gets data from localStorage
     * @param {string} key - Storage key
     * @param {*} defaultValue - Default value if key doesn't exist
     * @returns {*} - Stored value or default
     */
    get(key, defaultValue = null) {
        try {
            const item = localStorage.getItem(key);
            return item ? JSON.parse(item) : defaultValue;
        } catch (error) {
            console.error('Error reading from localStorage:', error);
            return defaultValue;
        }
    },

    /**
     * Sets data in localStorage
     * @param {string} key - Storage key
     * @param {*} value - Value to store
     * @returns {boolean} - Success status
     */
    set(key, value) {
        try {
            localStorage.setItem(key, JSON.stringify(value));
            return true;
        } catch (error) {
            console.error('Error writing to localStorage:', error);
            return false;
        }
    },

    /**
     * Removes data from localStorage
     * @param {string} key - Storage key
     */
    remove(key) {
        try {
            localStorage.removeItem(key);
        } catch (error) {
            console.error('Error removing from localStorage:', error);
        }
    },

    /**
     * Clears all app data from localStorage
     */
    clear() {
        Object.values(CONFIG.STORAGE_KEYS).forEach(key => {
            this.remove(key);
        });
    }
};

// Date/Time Utilities
const DateUtils = {
    /**
     * Formats a date for display
     * @param {Date|string} date - The date to format
     * @param {Object} options - Formatting options
     * @returns {string} - Formatted date string
     */
    format(date, options = {}) {
        const defaultOptions = {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        };

        const formatOptions = { ...defaultOptions, ...options };
        const dateObj = typeof date === 'string' ? new Date(date) : date;
        
        return dateObj.toLocaleDateString('en-US', formatOptions);
    },

    /**
     * Gets a relative time string (e.g., "2 minutes ago")
     * @param {Date|string} date - The date to compare
     * @returns {string} - Relative time string
     */
    getRelativeTime(date) {
        const now = new Date();
        const dateObj = typeof date === 'string' ? new Date(date) : date;
        const diffMs = now - dateObj;
        
        const diffSeconds = Math.floor(diffMs / 1000);
        const diffMinutes = Math.floor(diffSeconds / 60);
        const diffHours = Math.floor(diffMinutes / 60);
        const diffDays = Math.floor(diffHours / 24);

        if (diffSeconds < 60) return 'just now';
        if (diffMinutes < 60) return `${diffMinutes} minute${diffMinutes > 1 ? 's' : ''} ago`;
        if (diffHours < 24) return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
        if (diffDays < 30) return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`;
        
        return this.format(dateObj, { year: 'numeric', month: 'short', day: 'numeric' });
    }
};

// Error Handling Utilities
const ErrorHandler = {
    /**
     * Handles and displays errors
     * @param {Error|string} error - The error to handle
     * @param {string} context - Context where the error occurred
     * @param {boolean} showNotification - Whether to show user notification
     */
    handle(error, context = 'Unknown', showNotification = true) {
        const errorMessage = typeof error === 'string' ? error : error.message;
        const errorDetails = {
            message: errorMessage,
            context: context,
            timestamp: new Date().toISOString(),
            stack: error.stack || 'No stack trace available'
        };

        // Log error to console
        console.error(`[${context}] Error:`, errorDetails);

        // Show user-friendly notification
        if (showNotification) {
            const userMessage = this.getUserFriendlyMessage(errorMessage, context);
            NotificationManager.show(userMessage, 'error');
        }

        return errorDetails;
    },

    /**
     * Converts technical error messages to user-friendly ones
     * @param {string} errorMessage - Technical error message
     * @param {string} context - Error context
     * @returns {string} - User-friendly error message
     */
    getUserFriendlyMessage(errorMessage, context) {
        const messageMap = {
            'Failed to fetch': 'Network error. Please check your internet connection.',
            'API key': 'Invalid or missing API key. Please check your configuration.',
            'file too large': 'File is too large. Please use a smaller file.',
            'unsupported': 'File format not supported. Please use a supported file type.',
            'invalid json': 'Invalid JSON format. Please check your data.',
            'quota': 'API quota exceeded. Please try again later.',
            'rate limit': 'Too many requests. Please wait a moment and try again.'
        };

        for (const [key, message] of Object.entries(messageMap)) {
            if (errorMessage.toLowerCase().includes(key)) {
                return message;
            }
        }

        return `An error occurred${context !== 'Unknown' ? ` in ${context}` : ''}. Please try again.`;
    }
};

// Export utilities for global use
window.CONFIG = CONFIG;
window.JSONUtils = JSONUtils;
window.FileUtils = FileUtils;
window.UIUtils = UIUtils;
window.NotificationManager = NotificationManager;
window.StorageUtils = StorageUtils;
window.DateUtils = DateUtils;
window.ErrorHandler = ErrorHandler;

// Initialize notification system when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        NotificationManager.init();
    });
} else {
    NotificationManager.init();
}