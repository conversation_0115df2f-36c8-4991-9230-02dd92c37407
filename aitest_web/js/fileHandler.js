/**
 * File Handler for AI Resume Validator
 * Handles file uploads, processing, and conversion for validation
 */

class FileHandler {
    constructor() {
        this.supportedResumeFormats = CONFIG.SUPPORTED_RESUME_FORMATS;
        this.supportedJobFormats = CONFIG.SUPPORTED_JOB_FORMATS;
        this.maxFileSize = CONFIG.MAX_FILE_SIZE;
        
        // Store processed files
        this.processedFiles = {
            resume: null,
            jobImage: null
        };
    }

    /**
     * Processes a resume file (PDF, DOCX, or image)
     * @param {File} file - The resume file to process
     * @returns {Promise<Object>} - Processed file data
     */
    async processResumeFile(file) {
        try {
            // Validate file
            const validation = FileUtils.validate(file, 'resume');
            if (!validation.isValid) {
                throw new Error(validation.error);
            }

            // Process based on file type
            let processedFile;
            
            if (file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') {
                // DOCX file - extract text
                processedFile = await this.processDocxFile(file);
            } else if (file.type === 'application/pdf') {
                // PDF file - convert to base64
                processedFile = await this.processPdfFile(file);
            } else if (['image/png', 'image/jpeg', 'image/jpg'].includes(file.type)) {
                // Image file - convert to base64
                processedFile = await this.processImageFile(file);
            } else {
                throw new Error('Unsupported file format');
            }

            // Store processed file
            this.processedFiles.resume = processedFile;
            
            return processedFile;
        } catch (error) {
            ErrorHandler.handle(error, 'Resume File Processing');
            throw error;
        }
    }

    /**
     * Processes a job posting image
     * @param {File} file - The job image file to process
     * @returns {Promise<Object>} - Processed file data
     */
    async processJobImage(file) {
        try {
            // Validate file
            const validation = FileUtils.validate(file, 'job');
            if (!validation.isValid) {
                throw new Error(validation.error);
            }

            // Process image file
            const processedFile = await this.processImageFile(file);
            
            // Store processed file
            this.processedFiles.jobImage = processedFile;
            
            return processedFile;
        } catch (error) {
            ErrorHandler.handle(error, 'Job Image Processing');
            throw error;
        }
    }

    /**
     * Processes a DOCX file by extracting text
     * @param {File} file - The DOCX file
     * @returns {Promise<Object>} - Processed file data with extracted text
     */
    async processDocxFile(file) {
        try {
            if (typeof mammoth === 'undefined') {
                throw new Error('DOCX processing library not loaded');
            }

            // Convert file to ArrayBuffer
            const arrayBuffer = await file.arrayBuffer();
            
            // Extract text using mammoth
            const result = await mammoth.extractRawText({ arrayBuffer });
            
            if (!result.value || result.value.trim().length === 0) {
                throw new Error('No text could be extracted from the DOCX file');
            }

            return {
                fileName: file.name,
                fileSize: file.size,
                mimeType: file.type,
                extractedText: result.value,
                processedAt: new Date().toISOString(),
                type: 'docx'
            };
        } catch (error) {
            throw new Error(`DOCX processing failed: ${error.message}`);
        }
    }

    /**
     * Processes a PDF file by converting to base64
     * @param {File} file - The PDF file
     * @returns {Promise<Object>} - Processed file data with base64 content
     */
    async processPdfFile(file) {
        try {
            const base64Data = await FileUtils.toBase64(file);
            
            return {
                fileName: file.name,
                fileSize: file.size,
                mimeType: file.type,
                buffer: base64Data,
                processedAt: new Date().toISOString(),
                type: 'pdf'
            };
        } catch (error) {
            throw new Error(`PDF processing failed: ${error.message}`);
        }
    }

    /**
     * Processes an image file by converting to base64
     * @param {File} file - The image file
     * @returns {Promise<Object>} - Processed file data with base64 content
     */
    async processImageFile(file) {
        try {
            const base64Data = await FileUtils.toBase64(file);
            
            // Validate image dimensions (optional - for very large images)
            const imageInfo = await this.getImageInfo(file);
            
            return {
                fileName: file.name,
                fileSize: file.size,
                mimeType: file.type,
                buffer: base64Data,
                dimensions: imageInfo.dimensions,
                processedAt: new Date().toISOString(),
                type: 'image'
            };
        } catch (error) {
            throw new Error(`Image processing failed: ${error.message}`);
        }
    }

    /**
     * Gets image information including dimensions
     * @param {File} file - The image file
     * @returns {Promise<Object>} - Image information
     */
    async getImageInfo(file) {
        return new Promise((resolve, reject) => {
            const img = new Image();
            const url = URL.createObjectURL(file);
            
            img.onload = () => {
                URL.revokeObjectURL(url);
                resolve({
                    dimensions: {
                        width: img.naturalWidth,
                        height: img.naturalHeight
                    }
                });
            };
            
            img.onerror = () => {
                URL.revokeObjectURL(url);
                reject(new Error('Failed to load image'));
            };
            
            img.src = url;
        });
    }

    /**
     * Creates file preview HTML
     * @param {Object} processedFile - The processed file data
     * @param {string} type - 'resume' or 'job'
     * @returns {Object} - Preview HTML and data
     */
    createFilePreview(processedFile, type) {
        const icon = FileUtils.getFileIcon(processedFile.mimeType);
        const formattedSize = FileUtils.formatFileSize(processedFile.fileSize);
        
        if (processedFile.type === 'image') {
            return {
                html: `
                    <div class="file-info">
                        <div class="file-icon">${icon}</div>
                        <div class="file-details">
                            <div class="file-name">${processedFile.fileName}</div>
                            <div class="file-size">${formattedSize}</div>
                            <div class="file-type">${processedFile.mimeType}</div>
                            ${processedFile.dimensions ? 
                                `<div class="file-dimensions">${processedFile.dimensions.width} × ${processedFile.dimensions.height}</div>` 
                                : ''
                            }
                        </div>
                        <button class="remove-file" onclick="fileHandler.removeFile('${type}')">&times;</button>
                    </div>
                `,
                data: processedFile
            };
        }
        
        return {
            html: `
                <div class="file-info">
                    <div class="file-icon">${icon}</div>
                    <div class="file-details">
                        <div class="file-name">${processedFile.fileName}</div>
                        <div class="file-size">${formattedSize}</div>
                        <div class="file-type">${processedFile.mimeType}</div>
                        ${processedFile.type === 'docx' ? 
                            `<div class="file-status">Text extracted: ${processedFile.extractedText.length} characters</div>` 
                            : ''
                        }
                    </div>
                    <button class="remove-file" onclick="fileHandler.removeFile('${type}')">&times;</button>
                </div>
            `,
            data: processedFile
        };
    }

    /**
     * Creates image preview for job posting images
     * @param {Object} processedFile - The processed image file data
     * @returns {string} - Preview HTML
     */
    createImagePreview(processedFile) {
        const imageUrl = `data:${processedFile.mimeType};base64,${processedFile.buffer}`;
        
        return `
            <img id="jobImageDisplay" src="${imageUrl}" alt="Job posting preview">
            <div class="image-info">
                <span id="jobImageName">${processedFile.fileName}</span>
                <button class="remove-file" onclick="fileHandler.removeFile('jobImage')">&times;</button>
            </div>
        `;
    }

    /**
     * Removes a processed file
     * @param {string} type - File type: 'resume' or 'jobImage'
     */
    removeFile(type) {
        this.processedFiles[type] = null;
        
        // Clear UI
        if (type === 'resume') {
            this.clearResumePreview();
        } else if (type === 'jobImage') {
            this.clearJobImagePreview();
        }
        
        // Trigger validation check
        if (window.resumeValidator) {
            window.resumeValidator.checkValidationRequirements();
        }
    }

    /**
     * Clears resume file preview
     */
    clearResumePreview() {
        const previewElement = document.getElementById('resumePreview');
        const placeholderElement = document.getElementById('resumeUploadPlaceholder');
        const fileInput = document.getElementById('resumeFile');
        
        if (previewElement) {
            previewElement.style.display = 'none';
            previewElement.innerHTML = '';
        }
        
        if (placeholderElement) {
            placeholderElement.style.display = 'flex';
        }
        
        if (fileInput) {
            fileInput.value = '';
        }
    }

    /**
     * Clears job image preview
     */
    clearJobImagePreview() {
        const previewElement = document.getElementById('jobImagePreview');
        const placeholderElement = document.getElementById('jobImageUploadPlaceholder');
        const fileInput = document.getElementById('jobImage');
        
        if (previewElement) {
            previewElement.style.display = 'none';
            previewElement.innerHTML = '';
        }
        
        if (placeholderElement) {
            placeholderElement.style.display = 'flex';
        }
        
        if (fileInput) {
            fileInput.value = '';
        }
    }

    /**
     * Gets processed file data for validation
     * @returns {Object} - All processed files
     */
    getProcessedFiles() {
        return {
            resume: this.processedFiles.resume,
            jobImage: this.processedFiles.jobImage
        };
    }

    /**
     * Prepares file data for API call
     * @param {string} type - File type: 'resume' or 'jobImage'
     * @returns {Object|null} - API-ready file data
     */
    prepareForAPI(type) {
        const file = this.processedFiles[type];
        if (!file) return null;

        if (file.type === 'docx') {
            // For DOCX, return extracted text
            return {
                type: 'text',
                content: file.extractedText,
                fileName: file.fileName,
                mimeType: file.mimeType
            };
        } else {
            // For images and PDFs, return base64 data
            return {
                type: 'file',
                content: file.buffer,
                fileName: file.fileName,
                mimeType: file.mimeType,
                dimensions: file.dimensions
            };
        }
    }

    /**
     * Validates if files are ready for processing
     * @returns {Object} - Validation result
     */
    validateForProcessing() {
        const errors = [];
        
        if (!this.processedFiles.resume) {
            errors.push('Resume file is required');
        }
        
        // Job information is validated separately (text OR image)
        
        return {
            isValid: errors.length === 0,
            errors
        };
    }

    /**
     * Clears all processed files
     */
    clearAll() {
        this.processedFiles.resume = null;
        this.processedFiles.jobImage = null;
        
        this.clearResumePreview();
        this.clearJobImagePreview();
    }

    /**
     * Gets file processing statistics
     * @returns {Object} - Processing stats
     */
    getStats() {
        const resumeFile = this.processedFiles.resume;
        const jobImage = this.processedFiles.jobImage;
        
        return {
            filesProcessed: [resumeFile, jobImage].filter(f => f !== null).length,
            totalSize: [resumeFile, jobImage]
                .filter(f => f !== null)
                .reduce((sum, file) => sum + file.fileSize, 0),
            resumeReady: resumeFile !== null,
            jobImageReady: jobImage !== null,
            lastProcessed: Math.max(
                resumeFile ? new Date(resumeFile.processedAt).getTime() : 0,
                jobImage ? new Date(jobImage.processedAt).getTime() : 0
            )
        };
    }

    /**
     * Sets up drag and drop functionality for file upload areas
     * @param {HTMLElement} uploadArea - The upload area element
     * @param {Function} onDrop - Callback for file drop
     */
    setupDragAndDrop(uploadArea, onDrop) {
        if (!uploadArea) return;

        // Prevent default drag behaviors
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            uploadArea.addEventListener(eventName, (e) => {
                e.preventDefault();
                e.stopPropagation();
            }, false);
        });

        // Highlight drop area when item is dragged over it
        ['dragenter', 'dragover'].forEach(eventName => {
            uploadArea.addEventListener(eventName, () => {
                uploadArea.classList.add('dragover');
            }, false);
        });

        ['dragleave', 'drop'].forEach(eventName => {
            uploadArea.addEventListener(eventName, () => {
                uploadArea.classList.remove('dragover');
            }, false);
        });

        // Handle dropped files
        uploadArea.addEventListener('drop', (e) => {
            const files = e.dataTransfer.files;
            if (files.length > 0 && onDrop) {
                onDrop(files[0]);
            }
        }, false);
    }
}

// Create global instance
window.fileHandler = new FileHandler();