/**
 * Validator for AI Resume Validator
 * Handles validation logic using Gemini 2.5 Flash API
 */

class ResumeValidator {
    constructor() {
        this.apiKey = null;
        this.model = CONFIG.MODEL_NAME;
        this.apiEndpoint = CONFIG.API_ENDPOINT;
        
        // Load API key from storage
        this.loadApiKey();
        
        // Validation prompt template
        this.validationPrompt = this.createValidationPrompt();
    }

    /**
     * Loads API key from localStorage
     */
    loadApiKey() {
        this.apiKey = StorageUtils.get(CONFIG.STORAGE_KEYS.API_KEY);
    }

    /**
     * Sets and saves API key
     * @param {string} apiKey - The Gemini API key
     */
    setApiKey(apiKey) {
        this.apiKey = apiKey;
        StorageUtils.set(CONFIG.STORAGE_KEYS.API_KEY, apiKey);
    }

    /**
     * Creates the validation prompt template
     * @returns {string} - The prompt template
     */
    createValidationPrompt() {
        return `You are an expert resume validator specializing in career transition analysis. You must analyze the alignment between:
1. Original resume content (provided as file or text)
2. Job requirements (provided as text or image)
3. Generated JSON resume data (provided as structured data)

Your analysis must evaluate both standard alignment AND career transition optimization:

## CAREER TRANSITION & RELEVANCE (40% weight)
- Verify if the JSON properly reframes experience for the target role
- Check if transferable skills are effectively highlighted
- Assess if industry-specific terminology has been translated appropriately
- Validate that irrelevant experiences are minimized/excluded
- Check if the professional summary addresses career transition confidently
- Verify keyword mapping from job description to resume content
- Assess if content prioritization follows job relevance over chronology

## INFORMATION ACCURACY & INTEGRITY (30% weight)
- Verify all personal information matches exactly (no embellishments)
- Check that reframed content maintains factual accuracy
- Validate education details remain unchanged
- Confirm skills are accurately represented without exaggeration
- Ensure achievements use real metrics from original resume
- Check data consistency across all sections

## CONTENT OPTIMIZATION & CONSTRAINTS (20% weight)
- CRITICAL: Verify total JSON character count is under 4000 characters
- Check if content follows strict limits:
  * Professional summary: 40-50 words max
  * Each responsibility point: 12-18 words max
  * Max 3 responsibility points per position
  * Only most relevant positions included
- Verify Indonesian language usage for CV content (except technical terms)
- Assess if quantification with numbers/percentages is utilized

## STRATEGIC POSITIONING (10% weight)
- Evaluate if career change is positioned as strategic growth
- Check if gaps or transitions are addressed proactively
- Verify side projects/freelance work elevated when more relevant
- Assess if the narrative shows logical career progression
- Check if industry-bridging language is used effectively

## SCORING CRITERIA
- 90-100: Excellent transition strategy, highly relevant, within constraints
- 80-89: Good reframing, mostly relevant, minor optimization needed
- 70-79: Acceptable transition approach, some relevance gaps
- 60-69: Poor transition strategy, significant relevance issues
- Below 60: Failed career transition optimization, major problems

## CHARACTER COUNT VALIDATION
You MUST count the total characters in the JSON and verify it's under 4000.
If over 4000 characters, automatically deduct 20 points from the score.

Provide your analysis in the following JSON format:
{
  "alignmentScore": number (0-100),
  "isValid": boolean,
  "summary": "Brief overall assessment focusing on career transition effectiveness and relevance (2-3 sentences)",
  "characterCount": number (total characters in generated JSON),
  "isUnderCharLimit": boolean (true if under 4000),
  "categoryScores": {
    "careerTransitionRelevance": number (0-100),
    "accuracyIntegrity": number (0-100),
    "contentOptimization": number (0-100),
    "strategicPositioning": number (0-100)
  },
  "issues": [
    {
      "severity": "high|medium|low",
      "category": "transition|relevance|accuracy|optimization|character-limit",
      "title": "Brief issue title",
      "description": "Detailed description focusing on career transition impact",
      "location": "specific field or section where issue occurs",
      "impact": "how this affects career transition success"
    }
  ],
  "suggestions": [
    {
      "priority": "high|medium|low",
      "category": "transition|relevance|accuracy|optimization",
      "title": "Improvement suggestion title",
      "description": "Actionable recommendation for better career transition",
      "expectedImpact": "how this improves job match and transition narrative"
    }
  ],
  "strengths": [
    "What the JSON does well for career transition and relevance"
  ],
  "transitionAnalysis": {
    "isCareerTransition": boolean,
    "transitionEffectiveness": number (0-100),
    "transferableSkillsIdentified": string[],
    "reframingQuality": "excellent|good|fair|poor",
    "irrelevantContentRemoved": boolean,
    "industryTerminologyAdapted": boolean
  },
  "keyFindings": {
    "totalCharacterCount": number,
    "exceedsCharLimit": boolean,
    "careerTransitionGaps": string[],
    "missingTransferableSkills": string[],
    "wellReframedSections": string[],
    "jobMatchPercentage": number,
    "languageCompliance": "full|partial|none"
  }
}`;
    }

    /**
     * Validates alignment between resume, job info, and JSON data
     * @param {Object} resumeData - Processed resume file data
     * @param {Object} jobData - Job information (text or image)
     * @param {Object} jsonData - Parsed JSON resume data
     * @returns {Promise<Object>} - Validation results
     */
    async validateAlignment(resumeData, jobData, jsonData) {
        try {
            // Check API key
            if (!this.apiKey) {
                throw new Error('API key is required. Please configure your Gemini API key.');
            }

            // Validate inputs
            this.validateInputs(resumeData, jobData, jsonData);

            // Prepare API request
            const requestPayload = this.buildApiRequest(resumeData, jobData, jsonData);
            
            // Call Gemini API
            NotificationManager.show('Analyzing alignment with AI...', 'info', 0);
            const apiResponse = await this.callGeminiAPI(requestPayload);
            console.log('API response:', JSON.stringify(apiResponse, null, 2));
            
            // Parse and validate response
            const validationResults = this.parseValidationResponse(apiResponse);
            
            // Post-process results
            const enrichedResults = this.enrichValidationResults(validationResults, resumeData, jobData, jsonData);
            
            NotificationManager.clear();
            NotificationManager.show('Validation completed successfully!', 'success');
            
            return enrichedResults;
            
        } catch (error) {
            NotificationManager.clear();
            ErrorHandler.handle(error, 'Resume Validation');
            throw error;
        }
    }

    /**
     * Validates input data before processing
     * @param {Object} resumeData - Resume data
     * @param {Object} jobData - Job data  
     * @param {Object} jsonData - JSON data
     */
    validateInputs(resumeData, jobData, jsonData) {
        if (!resumeData) {
            throw new Error('Resume data is required');
        }

        if (!jobData || (!jobData.text && !jobData.image)) {
            throw new Error('Job information (text or image) is required');
        }

        if (!jsonData) {
            throw new Error('JSON resume data is required');
        }

        // Validate JSON structure
        const structureValidation = JSONUtils.validateResumeStructure(jsonData);
        if (!structureValidation.isValid) {
            throw new Error(`Invalid JSON structure: ${structureValidation.errors.join(', ')}`);
        }
    }

    /**
     * Builds the API request payload
     * @param {Object} resumeData - Resume data
     * @param {Object} jobData - Job data
     * @param {Object} jsonData - JSON data
     * @returns {Object} - API request payload
     */
    buildApiRequest(resumeData, jobData, jsonData) {
        const parts = [];

        // Add validation prompt
        parts.push({ text: this.validationPrompt });

        // Add JSON data to validate
        parts.push({ 
            text: `JSON Resume Data to Validate:\n${JSON.stringify(jsonData, null, 2)}` 
        });

        // Add resume data
        if (resumeData.extractedText) {
            // DOCX with extracted text
            parts.push({ 
                text: `Original Resume Content:\n${resumeData.extractedText}` 
            });
        } else if (resumeData.buffer) {
            // PDF or image file
            parts.push({ text: 'Original Resume Content (file):' });
            parts.push({
                inlineData: {
                    data: resumeData.buffer,
                    mimeType: resumeData.mimeType
                }
            });
        }

        // Add job information
        if (jobData.text) {
            parts.push({ 
                text: `Job Description:\n${jobData.text}` 
            });
        } else if (jobData.image) {
            parts.push({ text: 'Job Posting Image:' });
            parts.push({
                inlineData: {
                    data: jobData.image.buffer,
                    mimeType: jobData.image.mimeType
                }
            });
        }

        return {
            contents: [{ parts }],
            generationConfig: {
                temperature: 0.2,
                topK: 40,
                topP: 0.95,
                responseMimeType: "application/json"
            }
        };
    }

    /**
     * Calls the Gemini API
     * @param {Object} payload - Request payload
     * @returns {Promise<Object>} - API response
     */
    async callGeminiAPI(payload) {
        const url = `${this.apiEndpoint}${this.model}:generateContent`;
        
        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'x-goog-api-key': this.apiKey
            },
            body: JSON.stringify(payload)
        });

        if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            
            if (response.status === 401) {
                throw new Error('Invalid API key. Please check your Gemini API key configuration.');
            } else if (response.status === 429) {
                throw new Error('API quota exceeded. Please try again later.');
            } else if (response.status >= 500) {
                throw new Error('API service temporarily unavailable. Please try again.');
            } else {
                throw new Error(
                    errorData.error?.message || 
                    `API request failed with status ${response.status}`
                );
            }
        }

        return await response.json();
    }

    /**
     * Parses the validation response from Gemini API
     * @param {Object} apiResponse - Raw API response
     * @returns {Object} - Parsed validation results
     */
    parseValidationResponse(apiResponse) {
        try {
            if (!apiResponse.candidates || apiResponse.candidates.length === 0) {
                throw new Error('No response generated by AI model');
            }

            const responseText = apiResponse.candidates[0].content.parts[0].text;
            
            if (!responseText) {
                throw new Error('Empty response from AI model');
            }

            // Parse JSON response
            let parsedResponse;
            try {
                parsedResponse = JSON.parse(responseText);
            } catch (parseError) {
                // Try to extract JSON from response text
                const jsonMatch = responseText.match(/\{[\s\S]*\}/);
                if (jsonMatch) {
                    parsedResponse = JSON.parse(jsonMatch[0]);
                } else {
                    throw new Error('Could not parse AI response as JSON');
                }
            }

            // Validate response structure
            this.validateResponseStructure(parsedResponse);
            
            return parsedResponse;
            
        } catch (error) {
            console.error('API Response parsing error:', error);
            console.error('Raw response:', apiResponse);
            throw new Error(`Failed to parse validation results: ${error.message}`);
        }
    }

    /**
     * Validates the structure of the AI response
     * @param {Object} response - Parsed AI response
     */
    validateResponseStructure(response) {
        const requiredFields = ['alignmentScore', 'isValid', 'summary', 'issues', 'suggestions'];
        const missingFields = requiredFields.filter(field => !(field in response));
        
        if (missingFields.length > 0) {
            throw new Error(`Invalid response structure. Missing fields: ${missingFields.join(', ')}`);
        }

        if (typeof response.alignmentScore !== 'number' || 
            response.alignmentScore < 0 || 
            response.alignmentScore > 100) {
            throw new Error('Invalid alignment score');
        }

        if (typeof response.isValid !== 'boolean') {
            throw new Error('Invalid validation status');
        }

        if (!Array.isArray(response.issues) || !Array.isArray(response.suggestions)) {
            throw new Error('Issues and suggestions must be arrays');
        }
    }

    /**
     * Enriches validation results with additional metadata
     * @param {Object} results - Raw validation results
     * @param {Object} resumeData - Original resume data
     * @param {Object} jobData - Job data
     * @param {Object} jsonData - JSON data
     * @returns {Object} - Enriched results
     */
    enrichValidationResults(results, resumeData, jobData, jsonData) {
        return {
            ...results,
            metadata: {
                validatedAt: new Date().toISOString(),
                resumeFileName: resumeData.fileName,
                resumeFileSize: resumeData.fileSize,
                jsonCharacterCount: JSON.stringify(jsonData).length,
                jobInputType: jobData.text ? 'text' : 'image',
                processingTime: Date.now() - this.validationStartTime
            },
            statistics: {
                totalIssues: results.issues.length,
                highSeverityIssues: results.issues.filter(i => i.severity === 'high').length,
                mediumSeverityIssues: results.issues.filter(i => i.severity === 'medium').length,
                lowSeverityIssues: results.issues.filter(i => i.severity === 'low').length,
                totalSuggestions: results.suggestions.length,
                highPrioritySuggestions: results.suggestions.filter(s => s.priority === 'high').length
            }
        };
    }

    /**
     * Formats validation results for display
     * @param {Object} results - Validation results
     * @returns {Object} - Formatted display data
     */
    formatResultsForDisplay(results) {
        const getScoreColor = (score) => {
            if (score >= 90) return '#10b981'; // green
            if (score >= 80) return '#3b82f6'; // blue
            if (score >= 70) return '#f59e0b'; // yellow
            if (score >= 60) return '#f97316'; // orange
            return '#ef4444'; // red
        };

        const getScoreLabel = (score) => {
            if (score >= 90) return 'Excellent';
            if (score >= 80) return 'Good';
            if (score >= 70) return 'Acceptable';
            if (score >= 60) return 'Poor';
            return 'Needs Major Work';
        };

        return {
            ...results,
            display: {
                scoreColor: getScoreColor(results.alignmentScore),
                scoreLabel: getScoreLabel(results.alignmentScore),
                formattedScore: `${results.alignmentScore}/100`,
                statusText: results.isValid ? 'Validation Passed' : 'Issues Found',
                statusClass: results.isValid ? 'success' : 'warning',
                categoryScores: results.categoryScores ? Object.entries(results.categoryScores).map(([key, value]) => {
                    const nameMap = {
                        'careerTransitionRelevance': 'Career Transition & Relevance',
                        'accuracyIntegrity': 'Accuracy & Integrity',
                        'contentOptimization': 'Content Optimization',
                        'strategicPositioning': 'Strategic Positioning'
                    };
                    return {
                        name: nameMap[key] || key.charAt(0).toUpperCase() + key.slice(1),
                        score: value,
                        color: getScoreColor(value),
                        label: getScoreLabel(value)
                    };
                }) : []
            }
        };
    }

    /**
     * Checks if API key is configured
     * @returns {boolean} - Whether API key is available
     */
    hasApiKey() {
        return !!this.apiKey;
    }

    /**
     * Gets API configuration status
     * @returns {Object} - Configuration status
     */
    getApiStatus() {
        return {
            hasApiKey: this.hasApiKey(),
            model: this.model,
            endpoint: this.apiEndpoint
        };
    }

    /**
     * Tests API connection
     * @returns {Promise<boolean>} - Whether API is accessible
     */
    async testApiConnection() {
        if (!this.apiKey) {
            throw new Error('API key not configured');
        }

        try {
            const testPayload = {
                contents: [{ parts: [{ text: 'Test connection' }] }],
                generationConfig: {
                    maxOutputTokens: 10
                }
            };

            const response = await this.callGeminiAPI(testPayload);
            return !!response.candidates;
        } catch (error) {
            throw new Error(`API connection test failed: ${error.message}`);
        }
    }
}

// Create global instance
window.resumeValidator = new ResumeValidator();