# AI Resume Validator - Architecture Diagrams

## Component Interaction Diagram

```mermaid
graph TB
    subgraph "User Interface Layer"
        UI[index.html]
        CSS[styles.css]
        UI --> CSS
    end
    
    subgraph "Application Logic Layer"
        APP[app.js<br/>Main Controller]
        FH[fileHandler.js<br/>File Processing]
        VAL[validator.js<br/>Validation Logic]
        UTIL[utils.js<br/>Utilities]
        
        APP --> FH
        APP --> VAL
        APP --> UTIL
        FH --> UTIL
        VAL --> UTIL
    end
    
    subgraph "External Services"
        GEMINI[Gemini 2.5 Flash API]
        MAMMOTH[mammoth.js CDN]
    end
    
    UI --> APP
    FH --> MAMMOTH
    VAL --> GEMINI
```

## Validation Process Flow

```mermaid
sequenceDiagram
    participant User
    participant UI
    participant FileHandler
    participant Validator
    participant GeminiAPI
    
    User->>UI: Upload Resume File
    User->>UI: Input Job Info (Text/Image)
    User->>UI: Paste JSON Data
    User->>UI: Click Validate
    
    UI->>FileHandler: Process Resume File
    activate FileHandler
    alt is DOCX
        FileHandler->>FileHandler: Extract Text with Mammoth
    else is Image/PDF
        FileHandler->>FileHandler: Convert to Base64
    end
    FileHandler-->>UI: Processed File Data
    deactivate FileHandler
    
    UI->>Validator: Validate Alignment
    activate Validator
    Validator->>Validator: Prepare Prompt
    Validator->>GeminiAPI: Send Validation Request
    GeminiAPI-->>Validator: Validation Response
    Validator->>Validator: Parse Response
    Validator-->>UI: Validation Results
    deactivate Validator
    
    UI->>User: Display Results
```

## File Processing Flow

```mermaid
flowchart LR
    subgraph Input
        RF[Resume File]
        JT[Job Text]
        JI[Job Image]
        JD[JSON Data]
    end
    
    subgraph Processing
        FP[File Processor]
        TE[Text Extractor]
        B64[Base64 Encoder]
        JV[JSON Validator]
    end
    
    subgraph Output
        PD[Processed Data]
        VR[Validation Ready]
    end
    
    RF --> FP
    FP -->|DOCX| TE
    FP -->|PDF/Image| B64
    
    JT --> PD
    JI --> B64
    JD --> JV
    
    TE --> PD
    B64 --> PD
    JV --> PD
    
    PD --> VR
```

## State Management

```mermaid
stateDiagram-v2
    [*] --> Idle
    
    Idle --> FilesUploaded: User uploads files
    FilesUploaded --> DataEntered: User enters JSON
    DataEntered --> ReadyToValidate: All inputs provided
    
    ReadyToValidate --> Validating: User clicks validate
    Validating --> ProcessingFiles: Files being processed
    ProcessingFiles --> CallingAPI: Calling Gemini API
    CallingAPI --> ParsingResults: Receiving response
    
    ParsingResults --> Success: Validation complete
    ParsingResults --> Error: Validation failed
    
    Success --> DisplayingResults
    Error --> DisplayingError
    
    DisplayingResults --> Idle: User clears
    DisplayingError --> Idle: User clears
```

## Error Handling Flow

```mermaid
graph TD
    A[User Action] --> B{Input Validation}
    B -->|Valid| C[Process Input]
    B -->|Invalid| D[Show Input Error]
    
    C --> E{File Processing}
    E -->|Success| F[Prepare API Call]
    E -->|Failure| G[Show File Error]
    
    F --> H{API Call}
    H -->|Success| I[Parse Response]
    H -->|Failure| J[Show API Error]
    
    I --> K{Response Valid?}
    K -->|Yes| L[Display Results]
    K -->|No| M[Show Parse Error]
    
    D --> N[Enable Retry]
    G --> N
    J --> N
    M --> N
```

## Component Responsibilities Matrix

| Component | Primary Responsibility | Dependencies | Output |
|-----------|----------------------|--------------|---------|
| **index.html** | UI Structure & Layout | styles.css, app.js | User Interface |
| **app.js** | Application Orchestration | All JS modules | Coordinated Flow |
| **fileHandler.js** | File Processing & Conversion | utils.js, mammoth.js | Processed Files |
| **validator.js** | API Integration & Validation | utils.js | Validation Results |
| **utils.js** | Common Utilities | None | Helper Functions |

## API Integration Architecture

```mermaid
graph LR
    subgraph Browser
        V[validator.js]
        C[Config]
    end
    
    subgraph "API Request"
        P[Prompt Builder]
        R[Request Formatter]
        H[Headers + Auth]
    end
    
    subgraph "Gemini API"
        E[Endpoint]
        M[Model: gemini-2.5-flash]
    end
    
    subgraph "Response"
        RP[Response Parser]
        ER[Error Handler]
        RES[Results Formatter]
    end
    
    V --> C
    C --> P
    P --> R
    R --> H
    H --> E
    E --> M
    M --> RP
    RP --> ER
    ER --> RES
    RES --> V
```

## Data Schema Overview

```mermaid
classDiagram
    class ResumeFile {
        +String name
        +String mimeType
        +Number size
        +String base64Data
        +String extractedText
    }
    
    class JobInfo {
        +String type
        +String textContent
        +Object imageData
    }
    
    class JSONData {
        +Object personalInfo
        +String professionalSummary
        +Array experiences
        +Array education
        +Object skills
        +Object metadata
    }
    
    class ValidationRequest {
        +ResumeFile resume
        +JobInfo job
        +JSONData jsonData
        +String apiKey
    }
    
    class ValidationResult {
        +Boolean isValid
        +Number alignmentScore
        +Array issues
        +Array suggestions
        +Object details
    }
    
    ValidationRequest "1" --> "1" ResumeFile
    ValidationRequest "1" --> "1" JobInfo
    ValidationRequest "1" --> "1" JSONData
    ValidationRequest "1" --> "1" ValidationResult : produces
```

## Deployment Architecture

```mermaid
graph TD
    subgraph "Static Hosting"
        WS[Web Server]
        HTML[index.html]
        CSS[CSS Files]
        JS[JS Files]
    end
    
    subgraph "Client Browser"
        WA[Web App]
        LS[Local Storage]
        FC[File Cache]
    end
    
    subgraph "External APIs"
        GA[Gemini API]
        CDN[mammoth.js CDN]
    end
    
    WS --> HTML
    WS --> CSS
    WS --> JS
    
    HTML --> WA
    CSS --> WA
    JS --> WA
    
    WA --> LS
    WA --> FC
    WA --> GA
    WA --> CDN