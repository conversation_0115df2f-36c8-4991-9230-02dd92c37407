# AI Resume Validator Testing Website

A self-contained web application for testing and validating AI-generated resume JSON data against source resume files and job information using the Gemini 2.5 Flash API.

## Overview

This testing tool validates whether AI-generated job-specific resume data (in JSON format) properly aligns with:
- The original resume file (PDF, DOCX, PNG, JPG, or JPEG)
- Job information (text description or image)
- Expected data structure and formatting

## Architecture Documentation

- **[ARCHITECTURE.md](./ARCHITECTURE.md)** - Complete system architecture, component breakdown, and technical decisions
- **[ARCHITECTURE_DIAGRAMS.md](./ARCHITECTURE_DIAGRAMS.md)** - Visual diagrams including data flow, state management, and component interactions
- **[IMPLEMENTATION_GUIDE.md](./IMPLEMENTATION_GUIDE.md)** - Step-by-step implementation guide with code examples

## Features

- **Multiple Input Formats**: Supports PDF, DOCX, and image formats for resume files
- **Flexible Job Input**: Accept job descriptions as text or image uploads
- **Real-time Validation**: Uses Gemini 2.5 Flash API for intelligent alignment checking
- **Detailed Feedback**: Provides alignment scores, specific issues, and improvement suggestions
- **Self-contained**: No backend required - runs entirely in the browser

## Quick Start

1. Clone or download this folder
2. Add your Gemini API key to `js/app.js`
3. Open `index.html` in a modern web browser
4. Start validating!

## Project Structure

```
aitest_web/
├── index.html              # Main application
├── css/
│   └── styles.css         # Application styling
├── js/
│   ├── app.js             # Main controller
│   ├── fileHandler.js     # File processing
│   ├── validator.js       # Validation logic
│   └── utils.js           # Utilities
└── assets/                # Icons and images
```

## Key Components

### File Handler
- Processes resume files (PDF, DOCX, images)
- Extracts text from DOCX using mammoth.js
- Converts files to base64 for API consumption

### Validator
- Integrates with Gemini 2.5 Flash API
- Analyzes alignment between resume, job, and JSON data
- Returns structured validation results

### UI Controller
- Manages user interactions
- Orchestrates the validation flow
- Displays results in a user-friendly format

## Validation Checks

The validator performs comprehensive checks including:

1. **Information Accuracy**: Verifies all data matches the source resume
2. **Job Relevance**: Ensures JSON emphasizes relevant experience
3. **Data Integrity**: Identifies missing or embellished information
4. **Optimization Quality**: Evaluates keyword usage and tailoring

## Technical Stack

- **Frontend**: Vanilla JavaScript (ES6+)
- **File Processing**: mammoth.js for DOCX extraction
- **AI Integration**: Google Gemini 2.5 Flash API
- **Styling**: Modern CSS with Grid and Flexbox
- **No Build Process**: Direct browser execution

## Security Considerations

- All processing happens client-side
- No data is stored or transmitted to third parties
- API key can be user-provided for enhanced security
- File size limits prevent abuse

## Browser Requirements

- Modern browsers (Chrome, Firefox, Safari, Edge)
- JavaScript enabled
- File API support
- Fetch API support

## Future Enhancements

- Batch validation for multiple resumes
- Export validation reports
- Template-specific testing
- Performance metrics
- Validation history

## Development

For development, use a local server:
```bash
python -m http.server 8000
# Visit http://localhost:8000
```

## License

This testing tool is part of the Gigsta Project and follows the same licensing terms.