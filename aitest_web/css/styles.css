/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Colors */
    --primary-color: #2563eb;
    --primary-hover: #1d4ed8;
    --secondary-color: #6b7280;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    --info-color: #3b82f6;
    
    /* Backgrounds */
    --bg-primary: #ffffff;
    --bg-secondary: #f9fafb;
    --bg-tertiary: #f3f4f6;
    --bg-hover: #f8fafc;
    
    /* Text Colors */
    --text-primary: #111827;
    --text-secondary: #4b5563;
    --text-muted: #9ca3af;
    --text-inverse: #ffffff;
    
    /* Borders */
    --border-color: #e5e7eb;
    --border-hover: #d1d5db;
    --border-focus: #3b82f6;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    
    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    
    /* Border Radius */
    --radius-sm: 0.25rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    
    /* Typography */
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    
    /* Transitions */
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--bg-secondary);
    font-size: var(--font-size-base);
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: var(--spacing-xl);
    min-height: 100vh;
}

/* Header */
.header {
    text-align: center;
    margin-bottom: var(--spacing-2xl);
    padding: var(--spacing-xl) 0;
    background: linear-gradient(135deg, var(--primary-color), #1e40af);
    color: var(--text-inverse);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
}

.header h1 {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    margin-bottom: var(--spacing-sm);
}

.subtitle {
    font-size: var(--font-size-lg);
    opacity: 0.9;
    max-width: 600px;
    margin: 0 auto;
}

/* Main Content */
.main-content {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-2xl);
}

/* Input Section */
.input-section {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-xl);
}

.json-group {
    grid-column: 1 / -1;
}

/* Input Groups */
.input-group {
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
    box-shadow: var(--shadow);
    transition: box-shadow var(--transition-normal);
}

.input-group:hover {
    box-shadow: var(--shadow-md);
}

.section-title {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    font-size: var(--font-size-xl);
    font-weight: 600;
    margin-bottom: var(--spacing-lg);
    color: var(--text-primary);
}

.step-number {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2rem;
    height: 2rem;
    background: var(--primary-color);
    color: var(--text-inverse);
    border-radius: 50%;
    font-size: var(--font-size-sm);
    font-weight: 700;
}

/* File Upload Areas */
.file-upload-area {
    position: relative;
    border: 2px dashed var(--border-color);
    border-radius: var(--radius-lg);
    transition: all var(--transition-normal);
    background: var(--bg-secondary);
}

.file-upload-area:hover,
.file-upload-area.dragover {
    border-color: var(--primary-color);
    background-color: var(--bg-hover);
}

.file-input {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
    z-index: 2;
}

.upload-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-2xl);
    text-align: center;
}

.upload-icon {
    font-size: 3rem;
    margin-bottom: var(--spacing-md);
}

.upload-text p {
    margin-bottom: var(--spacing-sm);
    color: var(--text-secondary);
}

.upload-link {
    color: var(--primary-color);
    text-decoration: underline;
    cursor: pointer;
}

.upload-link:hover {
    color: var(--primary-hover);
}

.upload-text small {
    color: var(--text-muted);
    font-size: var(--font-size-xs);
}

/* File Preview */
.file-preview,
.image-preview {
    padding: var(--spacing-lg);
    background: var(--bg-secondary);
    border-radius: var(--radius-lg);
    margin-top: var(--spacing-md);
}

.file-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.file-icon {
    font-size: 2rem;
    color: var(--primary-color);
}

.file-details {
    flex: 1;
}

.file-name {
    font-weight: 600;
    color: var(--text-primary);
}

.file-size,
.file-type {
    font-size: var(--font-size-sm);
    color: var(--text-muted);
}

.remove-file {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--text-muted);
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--radius-sm);
    transition: all var(--transition-fast);
}

.remove-file:hover {
    background: var(--error-color);
    color: var(--text-inverse);
}

/* Job Input */
.job-input-container {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.input-toggle {
    display: flex;
    gap: var(--spacing-sm);
    padding: var(--spacing-xs);
    background: var(--bg-secondary);
    border-radius: var(--radius-lg);
}

.toggle-btn {
    flex: 1;
    padding: var(--spacing-md);
    border: none;
    background: transparent;
    color: var(--text-secondary);
    border-radius: var(--radius-md);
    cursor: pointer;
    font-size: var(--font-size-sm);
    font-weight: 500;
    transition: all var(--transition-fast);
}

.toggle-btn.active,
.toggle-btn:hover {
    background: var(--primary-color);
    color: var(--text-inverse);
}

.job-textarea {
    width: 100%;
    min-height: 200px;
    padding: var(--spacing-md);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    font-family: inherit;
    font-size: var(--font-size-base);
    line-height: 1.6;
    resize: vertical;
    transition: border-color var(--transition-fast);
}

.job-textarea:focus {
    outline: none;
    border-color: var(--border-focus);
    box-shadow: 0 0 0 3px rgb(59 130 246 / 0.1);
}

.textarea-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: var(--spacing-sm);
}

.char-count {
    font-size: var(--font-size-sm);
    color: var(--text-muted);
}

/* Image Preview */
.image-preview img {
    max-width: 100%;
    height: auto;
    border-radius: var(--radius-md);
    margin-bottom: var(--spacing-md);
    box-shadow: var(--shadow);
}

.image-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

/* JSON Input */
.json-input-container {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.json-textarea {
    width: 100%;
    min-height: 300px;
    padding: var(--spacing-md);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
    font-size: var(--font-size-sm);
    line-height: 1.5;
    resize: vertical;
    transition: border-color var(--transition-fast);
    background: var(--bg-secondary);
}

.json-textarea:focus {
    outline: none;
    border-color: var(--border-focus);
    box-shadow: 0 0 0 3px rgb(59 130 246 / 0.1);
    background: var(--bg-primary);
}

.json-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: var(--spacing-md);
}

.json-info {
    display: flex;
    gap: var(--spacing-md);
    align-items: center;
}

.json-status {
    font-size: var(--font-size-sm);
    font-weight: 500;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    background: var(--bg-tertiary);
    color: var(--text-secondary);
}

.json-status.valid {
    background: var(--success-color);
    color: var(--text-inverse);
}

.json-status.invalid {
    background: var(--error-color);
    color: var(--text-inverse);
}

.json-actions {
    display: flex;
    gap: var(--spacing-sm);
}

/* Buttons */
.action-btn,
.clear-btn {
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--border-color);
    background: var(--bg-primary);
    color: var(--text-secondary);
    border-radius: var(--radius-md);
    cursor: pointer;
    font-size: var(--font-size-sm);
    font-weight: 500;
    transition: all var(--transition-fast);
}

.action-btn:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.clear-btn:hover {
    background: var(--error-color);
    border-color: var(--error-color);
    color: var(--text-inverse);
}

/* Action Section */
.action-section {
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
    box-shadow: var(--shadow);
    text-align: center;
}

.action-container {
    display: flex;
    justify-content: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
    flex-wrap: wrap;
}

.primary-btn,
.secondary-btn {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-xl);
    border: none;
    border-radius: var(--radius-md);
    cursor: pointer;
    font-size: var(--font-size-base);
    font-weight: 600;
    transition: all var(--transition-normal);
    text-decoration: none;
    position: relative;
    overflow: hidden;
}

.primary-btn {
    background: var(--primary-color);
    color: var(--text-inverse);
    box-shadow: var(--shadow-md);
}

.primary-btn:hover:not(:disabled) {
    background: var(--primary-hover);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.primary-btn:disabled {
    background: var(--text-muted);
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.secondary-btn {
    background: var(--bg-secondary);
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
}

.secondary-btn:hover {
    background: var(--bg-tertiary);
    border-color: var(--border-hover);
}

.btn-icon {
    font-size: var(--font-size-lg);
}

/* Loading Spinner */
.loading-spinner {
    width: 20px;
    height: 20px;
    border: 2px solid transparent;
    border-top: 2px solid var(--text-inverse);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-left: var(--spacing-sm);
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Validation Requirements */
.validation-requirements {
    max-width: 400px;
    margin: 0 auto;
}

.requirements-title {
    font-weight: 600;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-md);
}

.requirements-list {
    list-style: none;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.requirement {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.req-icon {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-xs);
}

.requirement.completed .req-icon {
    background: var(--success-color);
    color: var(--text-inverse);
}

.requirement.completed .req-icon:before {
    content: "✓";
}

/* Results Section */
.results-section {
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
    box-shadow: var(--shadow);
}

.results-container {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

/* Results Components */
.result-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.metric-card {
    background: var(--bg-secondary);
    padding: var(--spacing-lg);
    border-radius: var(--radius-md);
    text-align: center;
}

.metric-value {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--primary-color);
}

.metric-label {
    font-size: var(--font-size-sm);
    color: var(--text-muted);
    margin-top: var(--spacing-xs);
}

.issues-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.issue-item {
    padding: var(--spacing-lg);
    border-radius: var(--radius-md);
    border-left: 4px solid;
}

.issue-high {
    border-color: var(--error-color);
    background: rgb(239 68 68 / 0.05);
}

.issue-medium {
    border-color: var(--warning-color);
    background: rgb(245 158 11 / 0.05);
}

.issue-low {
    border-color: var(--info-color);
    background: rgb(59 130 246 / 0.05);
}

.issue-header {
    display: flex;
    justify-content: between;
    align-items: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-sm);
}

.issue-severity {
    font-size: var(--font-size-xs);
    font-weight: 600;
    text-transform: uppercase;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
}

.severity-high {
    background: var(--error-color);
    color: var(--text-inverse);
}

.severity-medium {
    background: var(--warning-color);
    color: var(--text-inverse);
}

.severity-low {
    background: var(--info-color);
    color: var(--text-inverse);
}

.issue-category {
    font-size: var(--font-size-sm);
    color: var(--text-muted);
    text-transform: capitalize;
}

/* Modal */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
}

.modal.active {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow: hidden;
    transform: scale(0.9);
    transition: transform var(--transition-normal);
}

.modal.active .modal-content {
    transform: scale(1);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
}

.modal-header h3 {
    margin: 0;
    font-size: var(--font-size-lg);
    font-weight: 600;
}

.modal-close {
    background: none;
    border: none;
    font-size: var(--font-size-xl);
    cursor: pointer;
    padding: var(--spacing-xs);
    color: var(--text-muted);
    border-radius: var(--radius-sm);
    transition: all var(--transition-fast);
}

.modal-close:hover {
    background: var(--bg-secondary);
    color: var(--text-primary);
}

.modal-body {
    padding: var(--spacing-lg);
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: var(--spacing-sm);
    padding: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
}

.config-field {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.config-field label {
    font-weight: 600;
    color: var(--text-primary);
}

.config-field input {
    padding: var(--spacing-md);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    font-size: var(--font-size-base);
    transition: border-color var(--transition-fast);
}

.config-field input:focus {
    outline: none;
    border-color: var(--border-focus);
    box-shadow: 0 0 0 3px rgb(59 130 246 / 0.1);
}

.config-field small {
    color: var(--text-muted);
    font-size: var(--font-size-sm);
}

/* Notifications */
.notification-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1100;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.notification {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    padding: var(--spacing-md);
    box-shadow: var(--shadow-lg);
    max-width: 350px;
    transform: translateX(100%);
    transition: transform var(--transition-normal);
}

.notification.show {
    transform: translateX(0);
}

.notification.success {
    border-left: 4px solid var(--success-color);
}

.notification.error {
    border-left: 4px solid var(--error-color);
}

.notification.warning {
    border-left: 4px solid var(--warning-color);
}

.notification.info {
    border-left: 4px solid var(--info-color);
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: var(--spacing-md);
    }
    
    .input-section {
        grid-template-columns: 1fr;
    }
    
    .action-container {
        flex-direction: column;
        align-items: center;
    }
    
    .primary-btn,
    .secondary-btn {
        width: 100%;
        max-width: 300px;
    }
    
    .result-summary {
        grid-template-columns: 1fr;
    }
    
    .json-controls {
        flex-direction: column;
        align-items: stretch;
    }
    
    .json-actions {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .header h1 {
        font-size: var(--font-size-2xl);
    }
    
    .subtitle {
        font-size: var(--font-size-base);
    }
    
    .input-group {
        padding: var(--spacing-md);
    }
    
    .upload-placeholder {
        padding: var(--spacing-lg);
    }
    
    .upload-icon {
        font-size: 2rem;
    }
}

/* Print Styles */
@media print {
    .action-section,
    .modal,
    .notification-container {
        display: none;
    }
    
    .container {
        max-width: none;
        padding: 0;
    }
    
    .header {
        background: none;
        color: var(--text-primary);
        box-shadow: none;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    :root {
        /* Backgrounds */
        --bg-primary: #1f2937;
        --bg-secondary: #111827;
        --bg-tertiary: #374151;
        --bg-hover: #1f2937;
        
        /* Text Colors */
        --text-primary: #f9fafb;
        --text-secondary: #d1d5db;
        --text-muted: #9ca3af;
        --text-inverse: #111827;
        
        /* Borders */
        --border-color: #374151;
        --border-hover: #4b5563;
        --border-focus: #60a5fa;
        
        /* Shadows for dark mode */
        --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.3);
        --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.4), 0 1px 2px -1px rgb(0 0 0 / 0.4);
        --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.4), 0 2px 4px -2px rgb(0 0 0 / 0.4);
        --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.5), 0 4px 6px -4px rgb(0 0 0 / 0.5);
    }
    
    .header {
        background: linear-gradient(135deg, #1e40af, #1e3a8a);
        color: var(--text-primary);
    }
    
    /* Form elements */
    .job-textarea,
    .json-textarea {
        background: var(--bg-primary);
        color: var(--text-primary);
        border-color: var(--border-color);
    }
    
    .job-textarea:focus,
    .json-textarea:focus {
        background: var(--bg-secondary);
        border-color: var(--border-focus);
        color: var(--text-primary);
    }
    
    /* Input placeholders */
    .job-textarea::placeholder,
    .json-textarea::placeholder {
        color: var(--text-muted);
    }
    
    /* File upload areas */
    .file-upload-area {
        background: var(--bg-secondary);
        border-color: var(--border-color);
    }
    
    .file-upload-area:hover,
    .file-upload-area.dragover {
        background: var(--bg-tertiary);
    }
    
    /* Upload text */
    .upload-text p {
        color: var(--text-secondary);
    }
    
    .upload-text small {
        color: var(--text-muted);
    }
    
    /* File preview */
    .file-preview,
    .image-preview {
        background: var(--bg-secondary);
    }
    
    .file-name {
        color: var(--text-primary);
    }
    
    /* Toggle buttons */
    .input-toggle {
        background: var(--bg-secondary);
    }
    
    .toggle-btn {
        color: var(--text-secondary);
    }
    
    .toggle-btn.active,
    .toggle-btn:hover {
        background: var(--primary-color);
        color: var(--text-primary);
    }
    
    /* Action buttons */
    .action-btn {
        background: var(--bg-secondary);
        color: var(--text-secondary);
        border-color: var(--border-color);
    }
    
    .action-btn:hover {
        background: var(--bg-tertiary);
        color: var(--text-primary);
    }
    
    .secondary-btn {
        background: var(--bg-secondary);
        color: var(--text-secondary);
        border-color: var(--border-color);
    }
    
    .secondary-btn:hover {
        background: var(--bg-tertiary);
        color: var(--text-primary);
    }
    
    /* Modal */
    .modal {
        background: rgba(0, 0, 0, 0.8);
    }
    
    .modal-content {
        background: var(--bg-primary);
    }
    
    .modal-header {
        border-color: var(--border-color);
    }
    
    .modal-header h3 {
        color: var(--text-primary);
    }
    
    .modal-close {
        color: var(--text-muted);
    }
    
    .modal-close:hover {
        background: var(--bg-secondary);
        color: var(--text-primary);
    }
    
    .modal-footer {
        border-color: var(--border-color);
    }
    
    /* Config fields */
    .config-field label {
        color: var(--text-primary);
    }
    
    .config-field input {
        background: var(--bg-secondary);
        color: var(--text-primary);
        border-color: var(--border-color);
    }
    
    .config-field input:focus {
        background: var(--bg-primary);
        border-color: var(--border-focus);
    }
    
    .config-field input::placeholder {
        color: var(--text-muted);
    }
    
    .config-field small {
        color: var(--text-muted);
    }
    
    /* Notifications */
    .notification {
        background: var(--bg-primary);
        border-color: var(--border-color);
        color: var(--text-primary);
    }
    
    /* Metric cards */
    .metric-card {
        background: var(--bg-secondary);
    }
    
    .metric-label {
        color: var(--text-muted);
    }
    
    /* JSON status */
    .json-status {
        background: var(--bg-tertiary);
        color: var(--text-secondary);
    }
    
    /* Requirements */
    .requirements-title {
        color: var(--text-secondary);
    }
    
    .requirement {
        color: var(--text-secondary);
    }
    
    /* Remove file button */
    .remove-file {
        color: var(--text-muted);
    }
    
    .remove-file:hover {
        background: var(--error-color);
        color: var(--text-primary);
    }
    
    /* Loading spinner */
    .loading-spinner {
        border-top-color: var(--text-primary);
    }
}