# AI Resume Validator - Implementation Guide

## Quick Start

1. **Project Setup**
   ```bash
   cd aitest_web/
   # Open index.html in a modern web browser
   ```

2. **API Key Configuration**
   - Replace `YOUR_API_KEY_HERE` in `js/app.js` with your Gemini API key
   - Or implement a prompt for users to enter their own API key

## Implementation Steps

### Step 1: Create the HTML Structure

```html
<!-- index.html -->
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Resume Validator</title>
    <link rel="stylesheet" href="css/styles.css">
    <script src="https://cdn.jsdelivr.net/npm/mammoth@1.6.0/mammoth.browser.min.js"></script>
</head>
<body>
    <div class="container">
        <header>
            <h1>AI Resume Validator</h1>
            <p>Test AI-generated resume JSON data alignment</p>
        </header>
        
        <main>
            <!-- File upload section -->
            <section class="input-section">
                <div class="resume-upload">
                    <h2>Resume File</h2>
                    <input type="file" id="resumeFile" accept=".pdf,.docx,.png,.jpg,.jpeg">
                    <div id="resumePreview"></div>
                </div>
                
                <div class="job-input">
                    <h2>Job Information</h2>
                    <div class="input-toggle">
                        <button id="textInputBtn">Text Input</button>
                        <button id="imageInputBtn">Image Upload</button>
                    </div>
                    <textarea id="jobText" placeholder="Paste job description here..."></textarea>
                    <input type="file" id="jobImage" accept=".png,.jpg,.jpeg" style="display:none;">
                    <div id="jobPreview"></div>
                </div>
                
                <div class="json-input">
                    <h2>JSON Resume Data</h2>
                    <textarea id="jsonData" placeholder="Paste JSON data here..."></textarea>
                    <div class="json-controls">
                        <button id="formatJson">Format JSON</button>
                        <span id="charCount">0 characters</span>
                    </div>
                </div>
            </section>
            
            <section class="action-section">
                <button id="validateBtn" class="primary-btn">Validate Alignment</button>
                <button id="clearBtn" class="secondary-btn">Clear All</button>
            </section>
            
            <section id="results" class="results-section" style="display:none;">
                <h2>Validation Results</h2>
                <div id="resultsContent"></div>
            </section>
        </main>
    </div>
    
    <script src="js/utils.js"></script>
    <script src="js/fileHandler.js"></script>
    <script src="js/validator.js"></script>
    <script src="js/app.js"></script>
</body>
</html>
```

### Step 2: Implement Core JavaScript Modules

#### Configuration (js/config.js)
```javascript
const CONFIG = {
  GEMINI_API_KEY: 'YOUR_API_KEY_HERE',
  GEMINI_MODEL: 'gemini-2.0-flash-exp',
  MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
  API_ENDPOINT: 'https://generativelanguage.googleapis.com/v1beta/models/',
  SUPPORTED_FORMATS: {
    resume: {
      'application/pdf': '.pdf',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': '.docx',
      'image/png': '.png',
      'image/jpeg': '.jpg,.jpeg'
    },
    job: {
      'image/png': '.png',
      'image/jpeg': '.jpg,.jpeg'
    }
  }
};
```

#### File Handler Implementation
```javascript
// js/fileHandler.js
class FileHandler {
  constructor() {
    this.supportedFormats = CONFIG.SUPPORTED_FORMATS;
  }
  
  async processResumeFile(file) {
    if (!this.validateFile(file, 'resume')) {
      throw new Error('Unsupported file format');
    }
    
    if (file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') {
      return await this.processDocx(file);
    }
    
    return await this.fileToBase64(file);
  }
  
  async processDocx(file) {
    const arrayBuffer = await file.arrayBuffer();
    const result = await mammoth.extractRawText({ arrayBuffer });
    return {
      extractedText: result.value,
      mimeType: file.type,
      fileName: file.name
    };
  }
  
  async fileToBase64(file) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        const base64 = reader.result.split(',')[1];
        resolve({
          buffer: base64,
          mimeType: file.type,
          fileName: file.name
        });
      };
      reader.onerror = reject;
      reader.readAsDataURL(file);
    });
  }
  
  validateFile(file, type) {
    const formats = this.supportedFormats[type];
    return formats && formats[file.type];
  }
}
```

#### Validation Prompt Template
```javascript
// js/validator.js
const VALIDATION_PROMPT_TEMPLATE = `
You are an expert resume validator tasked with analyzing the alignment between:
1. Original resume content (provided as file)
2. Job requirements (provided as text or image)
3. Generated JSON resume data

Your analysis should check for:

1. **Information Accuracy**
   - Verify all personal information matches
   - Check work experience dates and details
   - Validate education information
   - Confirm skills are accurately represented

2. **Job Relevance**
   - Assess if the JSON emphasizes relevant experience
   - Check if key job requirements are addressed
   - Verify professional summary aligns with the position

3. **Data Integrity**
   - Identify any missing critical information
   - Flag any embellishments or inaccuracies
   - Check for proper formatting and structure

4. **Optimization Quality**
   - Evaluate keyword usage from job description
   - Assess the overall tailoring to the position
   - Review the prioritization of information

Provide your response in the following JSON format:
{
  "isValid": boolean,
  "alignmentScore": number (0-100),
  "issues": [
    {
      "severity": "high|medium|low",
      "category": "accuracy|relevance|integrity|optimization",
      "description": "specific issue description",
      "location": "field or section where issue occurs"
    }
  ],
  "suggestions": [
    "actionable improvement suggestion"
  ],
  "summary": "brief overall assessment"
}
`;
```

### Step 3: API Integration Pattern

```javascript
// js/validator.js
class Validator {
  constructor() {
    this.apiKey = CONFIG.GEMINI_API_KEY;
    this.model = CONFIG.GEMINI_MODEL;
  }
  
  async validateAlignment(resumeData, jobData, jsonData) {
    try {
      const prompt = this.buildPrompt(resumeData, jobData, jsonData);
      const response = await this.callGeminiAPI(prompt);
      return this.parseResponse(response);
    } catch (error) {
      console.error('Validation error:', error);
      throw new Error('Failed to validate alignment: ' + error.message);
    }
  }
  
  buildPrompt(resumeData, jobData, jsonData) {
    const parts = [
      { text: VALIDATION_PROMPT_TEMPLATE },
      { text: `JSON Resume Data to Validate:\n${JSON.stringify(jsonData, null, 2)}` }
    ];
    
    // Add resume data
    if (resumeData.extractedText) {
      parts.push({ text: `Resume Content:\n${resumeData.extractedText}` });
    } else {
      parts.push({
        inlineData: {
          data: resumeData.buffer,
          mimeType: resumeData.mimeType
        }
      });
    }
    
    // Add job data
    if (jobData.text) {
      parts.push({ text: `Job Description:\n${jobData.text}` });
    } else if (jobData.image) {
      parts.push({
        inlineData: {
          data: jobData.image.buffer,
          mimeType: jobData.image.mimeType
        }
      });
    }
    
    return parts;
  }
  
  async callGeminiAPI(parts) {
    const response = await fetch(
      `${CONFIG.API_ENDPOINT}${this.model}:generateContent?key=${this.apiKey}`,
      {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          contents: [{ parts }],
          generationConfig: {
            temperature: 0.2,
            topK: 40,
            topP: 0.95,
            maxOutputTokens: 2048,
            responseMimeType: "application/json"
          }
        })
      }
    );
    
    if (!response.ok) {
      throw new Error(`API error: ${response.status}`);
    }
    
    return await response.json();
  }
  
  parseResponse(apiResponse) {
    try {
      const text = apiResponse.candidates[0].content.parts[0].text;
      return JSON.parse(text);
    } catch (error) {
      throw new Error('Failed to parse API response');
    }
  }
}
```

### Step 4: CSS Styling Guidelines

```css
/* css/styles.css */
:root {
  --primary-color: #2563eb;
  --success-color: #10b981;
  --error-color: #ef4444;
  --warning-color: #f59e0b;
  --bg-color: #f9fafb;
  --text-color: #111827;
  --border-color: #e5e7eb;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

.input-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-bottom: 2rem;
}

.json-input {
  grid-column: span 2;
}

textarea {
  width: 100%;
  min-height: 200px;
  padding: 1rem;
  border: 1px solid var(--border-color);
  border-radius: 0.5rem;
  font-family: monospace;
}

.primary-btn {
  background-color: var(--primary-color);
  color: white;
  padding: 0.75rem 2rem;
  border: none;
  border-radius: 0.5rem;
  font-size: 1rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.primary-btn:hover {
  background-color: #1d4ed8;
}

.results-section {
  margin-top: 2rem;
  padding: 2rem;
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.issue-item {
  padding: 1rem;
  margin-bottom: 0.5rem;
  border-left: 4px solid;
  background-color: var(--bg-color);
}

.issue-high { border-color: var(--error-color); }
.issue-medium { border-color: var(--warning-color); }
.issue-low { border-color: var(--success-color); }
```

## Testing Strategy

### 1. Unit Tests
- Test file processing for each format
- Validate JSON parsing and formatting
- Test API response handling

### 2. Integration Tests
- End-to-end validation flow
- Error handling scenarios
- Different file format combinations

### 3. Manual Testing Checklist
- [ ] Upload various resume formats
- [ ] Test with invalid files
- [ ] Try different job input methods
- [ ] Test with malformed JSON
- [ ] Verify error messages
- [ ] Check responsive design
- [ ] Test API failures

## Deployment

### Option 1: Local Development
```bash
# Simply open index.html in a browser
# Or use a local server for better development experience
python -m http.server 8000
# Visit http://localhost:8000
```

### Option 2: Static Hosting
- GitHub Pages
- Netlify
- Vercel
- Any static file server

### Option 3: CDN Deployment
- Upload files to CDN
- No server-side processing needed
- Purely client-side application

## Security Best Practices

1. **API Key Management**
   ```javascript
   // Option 1: User provides key
   const apiKey = prompt('Enter your Gemini API key:');
   
   // Option 2: Environment variable (build time)
   const apiKey = process.env.GEMINI_API_KEY;
   
   // Option 3: Secure key storage service
   const apiKey = await fetchKeyFromSecureStorage();
   ```

2. **Input Validation**
   - Sanitize file names
   - Validate file sizes
   - Check MIME types
   - Validate JSON structure

3. **Error Handling**
   - Never expose API keys in errors
   - Provide user-friendly messages
   - Log errors for debugging

## Performance Optimization

1. **File Processing**
   - Use Web Workers for large files
   - Implement progress indicators
   - Cache processed results

2. **API Calls**
   - Implement request debouncing
   - Show loading states
   - Handle timeouts gracefully

3. **UI Responsiveness**
   - Lazy load large components
   - Optimize image previews
   - Use efficient DOM updates