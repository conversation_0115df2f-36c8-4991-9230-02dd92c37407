# AI Resume Validator Testing Website Architecture

## Overview
This document outlines the architecture for a self-contained testing website designed to validate AI-generated resume JSON data against source resume files and job information using the Gemini 2.5 Flash API.

## Project Structure

```
aitest_web/
├── index.html              # Main HTML file with UI structure
├── css/
│   └── styles.css         # Styling for the application
├── js/
│   ├── app.js             # Main application logic
│   ├── fileHandler.js     # File upload and processing
│   ├── validator.js       # Validation logic using Gemini API
│   └── utils.js           # Utility functions
├── assets/
│   └── icons/             # UI icons if needed
└── README.md              # Setup and usage instructions
```

## Component Architecture

### 1. UI Components (index.html)

#### Header Section
- Application title and description
- Clear instructions for users

#### Input Section
- **Resume Upload Component**
  - File input accepting: PDF, DOCX, PNG, JPG, JPEG
  - File preview area showing uploaded file name and size
  - Clear button to remove uploaded file

- **Job Information Component**
  - Toggle between text input and image upload
  - Text area for job description (expandable)
  - Image upload for job posting screenshots
  - Preview for uploaded job image

- **JSON Data Component**
  - Large text area for pasting JSON resume data
  - JSON syntax validation indicator
  - Format/prettify button
  - Character count display

#### Action Section
- Validate button (primary action)
- Clear all button (secondary action)

#### Results Section
- Validation status (success/failure)
- Detailed validation report
- Alignment score
- Specific feedback on mismatches
- Suggestions for improvements

### 2. JavaScript Modules

#### app.js - Main Application Controller
```javascript
class ResumeValidator {
  constructor() {
    this.fileHandler = new FileHandler();
    this.validator = new Validator();
    this.initializeEventListeners();
  }
  
  validateData() {
    // Orchestrate the validation process
  }
  
  displayResults(results) {
    // Update UI with validation results
  }
}
```

#### fileHandler.js - File Processing Module
```javascript
class FileHandler {
  constructor() {
    this.supportedFormats = {
      resume: ['application/pdf', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'image/png', 'image/jpeg', 'image/jpg'],
      job: ['image/png', 'image/jpeg', 'image/jpg']
    };
  }
  
  async processFile(file, type) {
    // Convert file to base64
    // Extract text from DOCX if needed
    // Validate file format
  }
  
  async extractTextFromDocx(buffer) {
    // Use mammoth.js for DOCX text extraction
  }
}
```

#### validator.js - Validation Logic
```javascript
class Validator {
  constructor() {
    this.apiKey = null; // Will be set from user input or config
  }
  
  async validateAlignment(resumeFile, jobInfo, jsonData) {
    // Prepare data for Gemini API
    // Call Gemini 2.5 Flash
    // Parse and return results
  }
  
  preparePrompt(resumeFile, jobInfo, jsonData) {
    // Create validation prompt
  }
  
  parseValidationResponse(response) {
    // Extract validation results
  }
}
```

#### utils.js - Utility Functions
```javascript
// JSON validation and formatting
function validateJSON(jsonString) { }
function formatJSON(jsonString) { }

// File utilities
function fileToBase64(file) { }
function formatFileSize(bytes) { }

// UI utilities
function showLoading() { }
function hideLoading() { }
function showError(message) { }
```

## Data Flow Diagram

```mermaid
graph TD
    A[User Interface] --> B[File Upload Handler]
    A --> C[Job Info Input]
    A --> D[JSON Data Input]
    
    B --> E[File Processor]
    E --> F[Base64 Converter]
    E --> G[DOCX Text Extractor]
    
    C --> H[Text/Image Handler]
    D --> I[JSON Validator]
    
    F --> J[Validation Orchestrator]
    G --> J
    H --> J
    I --> J
    
    J --> K[Gemini API Client]
    K --> L[Prompt Builder]
    L --> M[API Request]
    M --> N[Response Parser]
    
    N --> O[Results Processor]
    O --> P[UI Updater]
    P --> A
```

## Technical Decisions

### 1. Frontend Framework
- **Decision**: Vanilla JavaScript (no framework)
- **Rationale**: 
  - Self-contained requirement
  - No build process needed
  - Easy to run without setup
  - Sufficient for the application's complexity

### 2. File Processing
- **Client-side processing**: All file processing happens in the browser
- **Libraries**:
  - mammoth.js (CDN) for DOCX text extraction
  - Built-in FileReader API for file-to-base64 conversion

### 3. API Integration
- **Gemini API**: Direct browser-to-API calls
- **API Key Management**: 
  - Option 1: User provides their own API key
  - Option 2: Hardcoded key (not recommended for production)
  - Option 3: Environment variable loaded at runtime

### 4. Validation Logic
The validator will check:
1. **Data Completeness**: All required fields present in JSON
2. **Information Alignment**: Resume content matches JSON data
3. **Job Relevance**: JSON data is tailored to job requirements
4. **Format Compliance**: JSON follows the expected schema

### 5. Error Handling
- File format validation before processing
- API error handling with user-friendly messages
- JSON parsing error handling
- Network error recovery

## Configuration Considerations

### 1. API Configuration
```javascript
const CONFIG = {
  GEMINI_API_KEY: 'YOUR_API_KEY_HERE', // Replace in deployment
  GEMINI_MODEL: 'gemini-2.5-flash',
  MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
  SUPPORTED_FORMATS: {
    resume: ['pdf', 'docx', 'png', 'jpg', 'jpeg'],
    job: ['png', 'jpg', 'jpeg', 'txt']
  }
};
```

### 2. Validation Prompt Template
```javascript
const VALIDATION_PROMPT = `
You are an expert resume validator. Analyze the alignment between:
1. Original resume content
2. Job requirements
3. Generated JSON resume data

Check for:
- Information accuracy
- Relevance to job requirements
- Missing critical information
- Over-embellishment
- Format compliance

Provide a detailed validation report.
`;
```

## Security Considerations

1. **API Key Protection**
   - Never commit API keys to version control
   - Consider using environment variables
   - Implement rate limiting

2. **File Upload Security**
   - Validate file types before processing
   - Implement file size limits
   - Sanitize file names

3. **Data Privacy**
   - All processing happens client-side
   - No data storage on servers
   - Clear sensitive data after use

## Development Guidelines

1. **Code Organization**
   - Modular structure with clear separation of concerns
   - Well-documented functions
   - Consistent naming conventions

2. **Error Messages**
   - User-friendly error descriptions
   - Actionable error messages
   - Clear validation feedback

3. **Performance**
   - Efficient file processing
   - Debounced API calls
   - Loading indicators for long operations

## Testing Considerations

1. **Test Cases**
   - Various file formats
   - Large files
   - Invalid JSON
   - Network errors
   - API failures

2. **Browser Compatibility**
   - Modern browsers (Chrome, Firefox, Safari, Edge)
   - File API support required
   - ES6+ JavaScript features

## Future Enhancements

1. **Batch Processing**: Validate multiple resumes at once
2. **Export Results**: Save validation reports
3. **Template Testing**: Test against different resume templates
4. **History**: Keep validation history in localStorage
5. **Advanced Analytics**: Detailed scoring metrics