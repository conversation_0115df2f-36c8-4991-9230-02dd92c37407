<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Resume Validator - Testing Tool</title>
    <meta name="description" content="Test and validate AI-generated resume JSON data against source files and job information">
    <link rel="stylesheet" href="css/styles.css">
    <!-- Mammoth.js for DOCX processing -->
    <script src="https://cdn.jsdelivr.net/npm/mammoth@1.6.0/mammoth.browser.min.js"></script>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>AI Resume Validator</h1>
            <p class="subtitle">Test AI-generated resume JSON data alignment with source files and job requirements</p>
        </header>
        
        <main class="main-content">
            <!-- Input Section -->
            <section class="input-section">
                <!-- Resume Upload -->
                <div class="input-group resume-group">
                    <h2 class="section-title">
                        <span class="step-number">1</span>
                        Upload Resume File
                    </h2>
                    <div class="file-upload-area" id="resumeUploadArea">
                        <input type="file" id="resumeFile" accept=".pdf,.docx,.png,.jpg,.jpeg" class="file-input">
                        <div class="upload-placeholder" id="resumeUploadPlaceholder">
                            <div class="upload-icon">📄</div>
                            <div class="upload-text">
                                <p><strong>Drop your resume file here</strong></p>
                                <p>or <span class="upload-link">browse to select</span></p>
                                <small>Supports PDF, DOCX, PNG, JPG, JPEG (max 10MB)</small>
                            </div>
                        </div>
                        <div class="file-preview" id="resumePreview" style="display: none;">
                            <div class="file-info">
                                <div class="file-icon">📄</div>
                                <div class="file-details">
                                    <div class="file-name" id="resumeFileName"></div>
                                    <div class="file-size" id="resumeFileSize"></div>
                                    <div class="file-type" id="resumeFileType"></div>
                                </div>
                                <button class="remove-file" id="removeResumeFile">&times;</button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Job Information -->
                <div class="input-group job-group">
                    <h2 class="section-title">
                        <span class="step-number">2</span>
                        Job Information
                    </h2>
                    <div class="job-input-container">
                        <div class="input-toggle">
                            <button class="toggle-btn active" id="textInputBtn" data-mode="text">
                                📝 Text Input
                            </button>
                            <button class="toggle-btn" id="imageInputBtn" data-mode="image">
                                🖼️ Image Upload
                            </button>
                        </div>
                        
                        <div class="job-input-content">
                            <!-- Text Input Mode -->
                            <div class="input-mode" id="jobTextMode">
                                <textarea 
                                    id="jobText" 
                                    class="job-textarea" 
                                    placeholder="Paste the job description here...&#10;&#10;Include details like:&#10;- Job title and requirements&#10;- Required skills and experience&#10;- Responsibilities&#10;- Qualifications"
                                    rows="8"
                                ></textarea>
                                <div class="textarea-footer">
                                    <span class="char-count" id="jobTextCount">0 characters</span>
                                    <button class="clear-btn" id="clearJobText">Clear</button>
                                </div>
                            </div>
                            
                            <!-- Image Input Mode -->
                            <div class="input-mode" id="jobImageMode" style="display: none;">
                                <div class="file-upload-area" id="jobImageUploadArea">
                                    <input type="file" id="jobImage" accept=".png,.jpg,.jpeg" class="file-input">
                                    <div class="upload-placeholder" id="jobImageUploadPlaceholder">
                                        <div class="upload-icon">🖼️</div>
                                        <div class="upload-text">
                                            <p><strong>Drop job posting image here</strong></p>
                                            <p>or <span class="upload-link">browse to select</span></p>
                                            <small>Supports PNG, JPG, JPEG (max 10MB)</small>
                                        </div>
                                    </div>
                                    <div class="image-preview" id="jobImagePreview" style="display: none;">
                                        <img id="jobImageDisplay" src="" alt="Job posting preview">
                                        <div class="image-info">
                                            <span id="jobImageName"></span>
                                            <button class="remove-file" id="removeJobImage">&times;</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- JSON Data Input -->
                <div class="input-group json-group">
                    <h2 class="section-title">
                        <span class="step-number">3</span>
                        JSON Resume Data
                    </h2>
                    <div class="json-input-container">
                        <textarea 
                            id="jsonData" 
                            class="json-textarea" 
                            placeholder='Paste the AI-generated JSON resume data here...&#10;&#10;Expected format:&#10;{&#10;  "personalInfo": {&#10;    "fullName": "John Doe",&#10;    "email": "<EMAIL>",&#10;    ...&#10;  },&#10;  "experiences": [...],&#10;  "education": [...],&#10;  ...&#10;}'
                            rows="12"
                        ></textarea>
                        <div class="json-controls">
                            <div class="json-info">
                                <span class="json-status" id="jsonStatus">No JSON data</span>
                                <span class="char-count" id="jsonCharCount">0 characters</span>
                            </div>
                            <div class="json-actions">
                                <button class="action-btn" id="formatJsonBtn">Format JSON</button>
                                <button class="action-btn" id="validateJsonBtn">Validate JSON</button>
                                <button class="clear-btn" id="clearJsonBtn">Clear</button>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            
            <!-- Action Section -->
            <section class="action-section">
                <div class="action-container">
                    <button class="primary-btn" id="validateBtn" disabled>
                        <span class="btn-icon">🔍</span>
                        <span class="btn-text">Validate Alignment</span>
                        <div class="loading-spinner" id="loadingSpinner" style="display: none;"></div>
                    </button>
                    <button class="secondary-btn" id="clearAllBtn">
                        <span class="btn-icon">🗑️</span>
                        <span class="btn-text">Clear All</span>
                    </button>
                </div>
                <div class="validation-requirements" id="validationRequirements">
                    <p class="requirements-title">Required for validation:</p>
                    <ul class="requirements-list">
                        <li class="requirement" id="req-resume">
                            <span class="req-icon">⭕</span>
                            <span class="req-text">Resume file uploaded</span>
                        </li>
                        <li class="requirement" id="req-job">
                            <span class="req-icon">⭕</span>
                            <span class="req-text">Job information provided</span>
                        </li>
                        <li class="requirement" id="req-json">
                            <span class="req-icon">⭕</span>
                            <span class="req-text">Valid JSON data entered</span>
                        </li>
                    </ul>
                </div>
            </section>
            
            <!-- Results Section -->
            <section class="results-section" id="resultsSection" style="display: none;">
                <h2 class="section-title">
                    <span class="step-number">4</span>
                    Validation Results
                </h2>
                <div class="results-container" id="resultsContainer">
                    <!-- Results will be populated by JavaScript -->
                </div>
            </section>
        </main>
        
        <!-- Configuration Modal -->
        <div class="modal" id="configModal" style="display: none;">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>API Configuration</h3>
                    <button class="modal-close" id="closeConfigModal">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="config-field">
                        <label for="apiKeyInput">Gemini API Key:</label>
                        <input type="password" id="apiKeyInput" placeholder="Enter your Gemini API key">
                        <small>Your API key is stored locally and never sent to any server except Google's Gemini API.</small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="secondary-btn" id="cancelConfig">Cancel</button>
                    <button class="primary-btn" id="saveConfig">Save Configuration</button>
                </div>
            </div>
        </div>
        
        <!-- Notification Container -->
        <div class="notification-container" id="notificationContainer"></div>
    </div>
    
    <!-- JavaScript Files -->
    <script src="js/utils.js"></script>
    <script src="js/fileHandler.js"></script>
    <script src="js/validator.js"></script>
    <script src="js/app.js"></script>
</body>
</html>