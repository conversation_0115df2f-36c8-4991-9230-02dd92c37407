"""
AI-based contact information extractor using Google's Gemini 2.0 Flash Lite model.

This module extracts contact information (emails, URLs, and social media handles) from job posts using
Google's Generative AI capabilities.
"""

import os
import logging
import json
import typing
from typing import List, Dict, Tuple, Optional, Any
import google.generativeai as genai
import sys
import os
import re

# Add parent directory to path to allow imports from parent directory
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config import GEMINI_API_KEY

# Configure logging
logger = logging.getLogger(__name__)

# API Key management
def get_api_key() -> Optional[str]:
    """Get the Gemini API key from environment variables."""
    return GEMINI_API_KEY

def configure_genai() -> bool:
    """Configure the Generative AI API with the API key."""
    api_key = get_api_key()
    if not api_key:
        logger.warning("GEMINI_API_KEY not found in configuration")
        return False
    
    genai.configure(api_key=api_key)
    return True

# Enhanced response schema for structured output including social media
contact_schema = {
    "type": "object",
    "properties": {
        "emails": {
            "type": "array",
            "description": "List of email addresses found in the text",
            "items": {
                "type": "string",
                "description": "A valid email address"
            }
        },
        "urls": {
            "type": "array",
            "description": "List of URLs or websites found in the text",
            "items": {
                "type": "string",
                "description": "A valid URL or website"
            }
        },
        "social_media": {
            "type": "array",
            "description": "List of social media handles or accounts found in the text",
            "items": {
                "type": "object",
                "properties": {
                    "platform": {
                        "type": "string",
                        "description": "The social media platform name (e.g., Instagram, LinkedIn, Twitter, Facebook, WhatsApp)"
                    },
                    "handle": {
                        "type": "string",
                        "description": "The social media handle or account identifier"
                    }
                }
            }
        }
    },
    "required": ["emails", "urls", "social_media"]
}

def normalize_indonesian_phone(number):
    """
    Normalize Indonesian phone numbers to international format.
    
    Args:
        number (str): Phone number in any format
    
    Returns:
        str: Normalized phone number starting with 62 (Indonesian country code)
    """
    # Remove all non-digit characters
    clean = ''.join(c for c in number if c.isdigit())
    
    # Handle different formats
    if clean.startswith('62'):
        # Already in international format
        return clean
    elif clean.startswith('0'):
        # Convert 08xxx to 628xxx
        return '62' + clean[1:]
    # Handle international format with + prefix that was stripped
    elif number.strip().startswith('+62'):
        return clean
    # Handle international format with 00 prefix
    elif clean.startswith('0062'):
        return clean[2:]
    elif len(clean) >= 9 and not clean.startswith('62'):
        # Assume it's missing country code (common in Indonesia)
        return '62' + clean
    else:
        return clean

def extract_international_code(number):
    """
    Extract the international country code from a phone number.
    
    Args:
        number (str): Phone number possibly with international prefix
        
    Returns:
        tuple: (country_code, number_without_code) or (None, original_clean_number)
    """
    # Clean the number first
    clean = ''.join(c for c in number if c.isdigit())
    
    # If it starts with international format
    if number.strip().startswith('+'):
        # Common country codes (1-3 digits)
        for i in range(1, 4):
            if len(clean) > i:
                country_code = clean[:i]
                if country_code in ('1', '7', '20', '27', '30', '31', '32', '33', '34', '36', 
                                   '39', '40', '41', '43', '44', '45', '46', '47', '48', '49', 
                                   '51', '52', '53', '54', '55', '56', '57', '58', '60', '61', 
                                   '62', '63', '64', '65', '66', '81', '82', '84', '86', '90', 
                                   '91', '92', '93', '94', '95', '98', '212', '213', '216', 
                                   '218', '220', '221', '222', '223', '224', '225', '226', '227', 
                                   '228', '229', '230', '231', '232', '233', '234', '235', '236', 
                                   '237', '238', '239', '240', '241', '242', '243', '244', '245', 
                                   '246', '247', '248', '249', '250', '251', '252', '253', '254', 
                                   '255', '256', '257', '258', '260', '261', '262', '263', '264', 
                                   '265', '266', '267', '268', '269', '297', '298', '299', '350', 
                                   '351', '352', '353', '354', '355', '356', '357', '358', '359', 
                                   '370', '371', '372', '373', '374', '375', '376', '377', '378', 
                                   '380', '381', '382', '383', '385', '386', '387', '389', '420', 
                                   '421', '423', '500', '501', '502', '503', '504', '505', '506', 
                                   '507', '508', '509', '590', '591', '592', '593', '595', '597', 
                                   '598', '599', '670', '672', '673', '674', '675', '676', '677', 
                                   '678', '679', '680', '681', '682', '683', '685', '686', '687', 
                                   '688', '689', '690', '691', '692', '850', '852', '853', '855', 
                                   '856', '870', '880', '886', '960', '961', '962', '963', '964', 
                                   '965', '966', '967', '968', '970', '971', '972', '973', '974', 
                                   '975', '976', '977', '992', '993', '994', '995', '996', '998'):
                    return country_code, clean[i:]
    
    # If it starts with 00 (international calling prefix)
    if clean.startswith('00'):
        # Remove the 00 prefix and try again with the rest
        return extract_international_code('+' + clean[2:])
        
    # Couldn't determine international code
    return None, clean

def is_valid_mobile_number(number):
    """
    Check if a number appears to be a valid mobile number for any country.
    This is a more relaxed check than is_valid_indonesian_mobile.
    
    Args:
        number (str): Phone number to check
    
    Returns:
        bool: True if the number appears to be a valid mobile number
    """
    # Get cleaned number
    clean = ''.join(c for c in number if c.isdigit())
    
    # Check for international format indicators
    is_international = number.strip().startswith('+') or clean.startswith('00')
    
    # Extract country code and remaining number
    country_code, remaining = extract_international_code(number)
    
    # If we identified a country code
    if country_code:
        # Different countries have different valid lengths
        if country_code == '1':  # North America
            return len(remaining) == 10
        elif country_code in ('44', '33', '49'):  # UK, France, Germany
            return len(remaining) >= 9 and len(remaining) <= 12
        elif country_code in ('62', '60', '65'):  # Indonesia, Malaysia, Singapore
            return len(remaining) >= 8 and len(remaining) <= 12
        else:
            # General rule for other countries - at least 7 digits after country code
            return len(remaining) >= 7
    
    # If no country code identified but appears international
    if is_international:
        # Use a general rule - full number should be at least 10 digits
        return len(clean) >= 10
    
    # No international indicators, just check if it's a reasonable length
    return len(clean) >= 8

def is_valid_indonesian_mobile(number):
    """
    Check if a number appears to be a valid Indonesian mobile number.
    Indonesian mobile numbers typically start with 08 (local format)
    or 628 (international format).
    
    Args:
        number (str): Phone number to check
    
    Returns:
        bool: True if the number appears to be a valid Indonesian mobile number
    """
    # First normalize the number
    normalized = normalize_indonesian_phone(number)
    
    # Check if it meets basic requirements for Indonesian mobile numbers
    # Must be at least 10 digits (62 + 8 digits)
    if len(normalized) < 10:
        return False
    
    # Must start with 62 and then 8 (Indonesian mobile format)
    if not normalized.startswith('628'):
        return False
        
    return True

def extract_whatsapp_number_from_url(url):
    """
    Extract WhatsApp number from various WhatsApp URL formats.
    
    Args:
        url (str): WhatsApp URL in various formats
        
    Returns:
        str or None: Extracted phone number or None if not found
    """
    patterns = [
        # Standard wa.me format - with and without + sign
        r'wa\.me/(\+?\d+)',
        # api.whatsapp.com format - with and without + sign
        r'api\.whatsapp\.com/send\?phone=(\+?\d+)',
        # Full WhatsApp URL format - with and without + sign
        r'whatsapp\.com/send\?phone=(\+?\d+)'
    ]
    
    for pattern in patterns:
        match = re.search(pattern, url)
        if match:
            number = match.group(1)
            # Strip the plus sign if present
            if number.startswith('+'):
                number = number[1:]
            return number
    
    return None

def extract_contact_info(text: str) -> Tuple[List[str], List[str]]:
    """
    Extract contact information from text using Gemini AI.
    
    Args:
        text (str): The job post text to extract contact information from
        
    Returns:
        tuple: (emails, urls) - Lists of extracted emails and URLs
    """
    if not text:
        return [], []
    
    # Try AI extraction
    try:
        if not configure_genai():
            logger.warning("Failed to configure GenAI, API key may be missing")
            return [], []
        
        # Set up the model (Gemini 2.0 Flash Lite)
        model = genai.GenerativeModel('gemini-2.0-flash-lite')
        
        # Create structured prompt with explicit JSON format instruction
        prompt = f"""
        Extract ALL contact information from the following job post.
        Find all ways someone could apply for the job or contact the employer, including:
        
        1. Email addresses (e.g., <EMAIL>, <EMAIL>)
        2. URLs/websites (e.g., example.com/jobs, apply.company.com)
        3. Social media handles and accounts:
           - Instagram accounts (e.g., @company_name)
           - LinkedIn profiles (e.g., linkedin.com/in/recruiter, linkedin.com/company/example)
           - Twitter/X handles (e.g., @company_jobs)
           - Facebook pages
           - WhatsApp numbers
           - Other social platforms
        
        For social media, include both direct handles (like @company_name) and URLs (like twitter.com/company_name).
        
        Return your response as a valid JSON with the following structure:
        {{
            "emails": ["<EMAIL>", "<EMAIL>", ...],
            "urls": ["https://example.com", "https://company.com/apply", ...],
            "social_media": [
                {{"platform": "Instagram", "handle": "@example_company"}},
                {{"platform": "LinkedIn", "handle": "linkedin.com/company/example"}},
                {{"platform": "WhatsApp", "handle": "***********"}}
            ]
        }}
        
        Be THOROUGH and make sure to find ALL contact information in the text, even if it's:
        - Written in another language (like Indonesian)
        - Written in special formats (like email [at] domain [dot] com)
        - Mentioned within paragraphs rather than separately
        - Indicated indirectly (e.g., "visit our website at")
        
        If none are found in a category, use an empty array.
        
        Job Post:
        {text}
        """
        
        # Generate response
        response = model.generate_content(
            prompt,
            generation_config={"temperature": 0}
        )
        
        # Process and validate response
        if hasattr(response, 'text'):
            try:
                # Try to parse the response as JSON
                response_text = response.text.strip()
                
                # Extract JSON from the response if it's wrapped in markdown code blocks
                if "```json" in response_text:
                    json_text = response_text.split("```json")[1].split("```")[0].strip()
                elif "```" in response_text:
                    json_text = response_text.split("```")[1].strip()
                else:
                    json_text = response_text
                
                # Parse the JSON response
                structured_response = json.loads(json_text)
                
                # Extract emails and URLs
                emails = structured_response.get("emails", [])
                urls = structured_response.get("urls", [])
                social_media = structured_response.get("social_media", [])
                
                # Process social media handles
                social_urls = []
                for sm in social_media:
                    handle = sm.get("handle", "")
                    platform = sm.get("platform", "").lower()
                    
                    # Convert social media handles to URLs when possible
                    if handle:
                        # If it's already a URL-like format, add it directly
                        if handle.startswith("http") or handle.startswith("www") or ".com" in handle or ".net" in handle or ".org" in handle:
                            social_urls.append(handle)
                        # Otherwise, format based on the platform
                        elif handle.startswith("@"):
                            # Strip the @ for URL construction
                            username = handle[1:]
                            if platform == "instagram":
                                social_urls.append(f"instagram.com/{username}")
                            elif platform == "twitter" or platform == "x":
                                social_urls.append(f"twitter.com/{username}")
                            elif platform == "facebook":
                                social_urls.append(f"facebook.com/{username}")
                            elif platform == "telegram" or platform == "line":
                                # For messaging platforms, keep the handle as is
                                social_urls.append(handle)
                            else:
                                # For other platforms, keep the handle itself
                                social_urls.append(handle)
                        elif "whatsapp" in platform or platform == "wa":
                            # Format WhatsApp numbers as URLs if they're not already
                            if not handle.startswith("https://wa.me/"):
                                # Normalize WhatsApp number
                                normalized = normalize_indonesian_phone(handle)
                                if len(normalized) >= 10 and is_valid_indonesian_mobile(handle):
                                    social_urls.append(f"https://wa.me/{normalized}")
                            else:
                                social_urls.append(handle)
                        # Handle messaging platform URLs
                        elif "t.me/" in handle or "line.me/" in handle:
                            social_urls.append(handle)
                
                # Process WhatsApp numbers from text
                whatsapp_numbers = set()
                whatsapp_pattern = re.compile(r'(?:wa\.me/|whatsapp\.com/)(\d+)')
                phone_patterns = ["08", "+62", "628", "62-8", "wa:", "wa ", "whatsapp", "WA", "WhatsApp"]
                
                # First, normalize any existing WhatsApp URLs
                normalized_urls = []
                for url in urls:
                    # Check if it's a WhatsApp URL
                    if "wa.me" in url or "whatsapp.com" in url or "api.whatsapp" in url:
                        # Try to extract the number
                        extracted_number = extract_whatsapp_number_from_url(url)
                        if extracted_number:
                            # For Indonesian numbers, normalize them
                            if extracted_number.startswith('0') or extracted_number.startswith('62'):
                                normalized = normalize_indonesian_phone(extracted_number)
                                if normalized not in whatsapp_numbers and is_valid_indonesian_mobile(normalized):
                                    whatsapp_numbers.add(normalized)
                                    normalized_urls.append(f"https://wa.me/{normalized}")
                                    continue
                            # For other international numbers, keep the URL if it's a valid mobile
                            elif is_valid_mobile_number(extracted_number):
                                normalized_urls.append(url)
                                continue
                    
                    # Special cases for WhatsApp message links that don't contain phone numbers
                    if "wa.me/message/" in url or "business.whatsapp.com" in url:
                        normalized_urls.append(url)
                        continue
                        
                    # Not a WhatsApp URL or couldn't extract number, keep as is
                    normalized_urls.append(url)
                
                # Replace URLs with normalized versions
                urls = normalized_urls
                
                # Same for social URLs - normalize WhatsApp URLs
                normalized_social_urls = []
                for url in social_urls:
                    # Check if it's a WhatsApp URL
                    if "wa.me" in url or "whatsapp.com" in url or "api.whatsapp" in url:
                        # Try to extract the number
                        extracted_number = extract_whatsapp_number_from_url(url)
                        if extracted_number:
                            # For Indonesian numbers, normalize them
                            if extracted_number.startswith('0') or extracted_number.startswith('62'):
                                normalized = normalize_indonesian_phone(extracted_number)
                                if normalized not in whatsapp_numbers and is_valid_indonesian_mobile(normalized):
                                    whatsapp_numbers.add(normalized)
                                    normalized_social_urls.append(f"https://wa.me/{normalized}")
                                    continue
                            # For other international numbers, keep the URL if it's a valid mobile
                            elif is_valid_mobile_number(extracted_number):
                                normalized_social_urls.append(url)
                                continue
                    
                    # Special cases for WhatsApp message links that don't contain phone numbers
                    if "wa.me/message/" in url or "business.whatsapp.com" in url:
                        normalized_social_urls.append(url)
                        continue
                        
                    # Not a WhatsApp URL or couldn't extract number, keep as is
                    normalized_social_urls.append(url)
                
                # Extract WhatsApp numbers directly from text
                for line in text.split('\n'):
                    line = line.lower()
                    if any(pattern in line for pattern in phone_patterns):
                        # Look for digits with common Indonesian mobile number patterns
                        phone_matches = re.findall(r'(?:\+62|62|0)(?:8\d{1,}[-\.\s]?(?:\d{1,}[-\.\s]?){1,})', line)
                        
                        for match in phone_matches:
                            normalized = normalize_indonesian_phone(match)
                            if normalized not in whatsapp_numbers and is_valid_indonesian_mobile(match):
                                whatsapp_numbers.add(normalized)
                                normalized_social_urls.append(f"https://wa.me/{normalized}")
                        
                        # Also look for international numbers with WhatsApp mention
                        if "whatsapp" in line or "wa:" in line or "wa " in line or "whatsapp" in line:
                            # Look for international patterns with + sign (common format)
                            intl_matches = re.findall(r'(?:\+[1-9]\d{1,2}[-\.\s]?)(?:\d{1,}[-\.\s]?){5,}', line)
                            for match in intl_matches:
                                # Extract country code and number
                                country_code, remaining = extract_international_code(match)
                                if country_code:
                                    # Clean up the number and combine with country code
                                    clean_number = country_code + ''.join(c for c in remaining if c.isdigit())
                                    # Add if it's valid and not already added
                                    if clean_number not in whatsapp_numbers and is_valid_mobile_number(match):
                                        whatsapp_numbers.add(clean_number)
                                        normalized_social_urls.append(f"https://wa.me/{clean_number}")
                            
                            # Look for 00 international calling format
                            intl_00_matches = re.findall(r'(?:00[1-9]\d{1,2}[-\.\s]?)(?:\d{1,}[-\.\s]?){5,}', line)
                            for match in intl_00_matches:
                                # Remove the 00 prefix and extract country code
                                clean_match = '+' + match[2:]
                                country_code, remaining = extract_international_code(clean_match)
                                if country_code:
                                    # Clean up the number and combine with country code
                                    clean_number = country_code + ''.join(c for c in remaining if c.isdigit())
                                    # Add if it's valid and not already added
                                    if clean_number not in whatsapp_numbers and is_valid_mobile_number(clean_match):
                                        whatsapp_numbers.add(clean_number)
                                        normalized_social_urls.append(f"https://wa.me/{clean_number}")
                    
                    # Also look for international numbers with country codes regardless of WhatsApp mention
                    # This is useful for job posts where phone numbers are listed and some are implicitly WhatsApp
                    intl_patterns = re.findall(r'(?:\+[1-9]\d{1,2}[-\.\s]?)(?:\d{1,}[-\.\s]?){5,}', line.lower())
                    for match in intl_patterns:
                        country_code, remaining = extract_international_code(match)
                        # Focus on country codes where WhatsApp is common
                        if country_code in ('60', '62', '65', '63', '66', '91', '971', '966', '52', '55', '33'):
                            clean_number = country_code + ''.join(c for c in remaining if c.isdigit())
                            if clean_number not in whatsapp_numbers and is_valid_mobile_number(match):
                                # The number is very likely to be a WhatsApp number in these countries
                                # especially in job application contexts
                                whatsapp_numbers.add(clean_number)
                                normalized_social_urls.append(f"https://wa.me/{clean_number}")
                # Add normalized social URLs to main URLs list
                for url in normalized_social_urls:
                    if url not in urls:
                        urls.append(url)
                
                # Log extracted information
                logger.info(f"AI extracted {len(emails)} emails, {len(urls)} URLs, and {len(social_media)} social media handles")
                
                return emails, urls
                
            except (json.JSONDecodeError, KeyError, AttributeError) as e:
                logger.error(f"Error processing Gemini response: {e}")
                return [], []
        
        # Couldn't process the response
        logger.warning("Invalid AI response format")
        return [], []
        
    except Exception as e:
        logger.error(f"Error in AI contact extraction: {e}")
        return [], []
