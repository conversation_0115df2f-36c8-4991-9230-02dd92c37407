import asyncio
import json
import logging
from job_scraper import JobScraper

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

async def main():
    # Create an instance of the JobScraper
    scraper = JobScraper()
    
    # Scrape jobs from JobStreet
    print("Scraping jobs from JobStreet...")
    jobstreet_jobs = await scraper.scrape_jobstreet()
    
    # Print the number of jobs found
    print(f"Found {len(jobstreet_jobs)} jobs on JobStreet")
    
    # Save the results to a JSON file
    output_file = "jobstreet_jobs.json"
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(jobstreet_jobs, f, ensure_ascii=False, indent=2)
    
    print(f"Results saved to {output_file}")
    
    # Print the first 3 jobs as a preview
    if jobstreet_jobs:
        print("\nPreview of the first 5 jobs:")
        for i, job in enumerate(jobstreet_jobs[:5]):
            print(f"\nJob {i+1}:")
            for key, value in job.items():
                print(f"  {key}: {value}")

if __name__ == "__main__":
    asyncio.run(main())
