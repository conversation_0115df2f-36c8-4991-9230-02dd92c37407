import logging
import pytz
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import Application, CommandHandler, CallbackQueryHandler, ContextTypes, MessageHandler, filters
import asyncio
import aiohttp
import validators
from bs4 import BeautifulSoup
from datetime import datetime, time as datetime_time, timedelta
import json
import sys
import os
import random

# Add parent directory to path to allow imports from parent directory
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config import BOT_TOKEN, CHANNEL_ID, ENV, is_feature_enabled
from job_scraper import JobScraper
from gigsta_database import JobDatabase
from job.contact_validator import extract_contact_info as rule_based_extract
from job.ai_contact_extractor import extract_contact_info

# Configure logging
logging_level = logging.DEBUG if is_feature_enabled("debug_logging") else logging.INFO
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging_level
)
logger = logging.getLogger(__name__)

# Log environment information
logger.info(f"Starting bot in {ENV} environment")

# Bot configuration
TELEGRAM_TOKEN = BOT_TOKEN

class JobBot:
    def __init__(self):
        self.scraper = JobScraper()
        self.db = JobDatabase()
        self.subscriptions = self.load_subscriptions()
        # State tracking for conversation flows
        self.user_states = {}
        # Track active commands for each user
        self.active_commands = {}
        
    def get_channel_link(self):
        """Get a formatted channel link based on CHANNEL_ID"""
        try:
            if CHANNEL_ID.startswith('@'):
                return f"{CHANNEL_ID}"
            else:
                # For numeric IDs, we can create a t.me link
                return f"https://t.me/c/{CHANNEL_ID.replace('-100', '')}"
        except:
            # Fallback if there's any issue with the channel ID
            return "Gigsta Jobs"
        
    def load_subscriptions(self):
        return self.db.load_subscriptions()

    def save_subscriptions(self):
        # This method is no longer needed as we save directly to the database
        # when adding or removing subscriptions
        pass

    async def start(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        # Cancel any previous commands
        await self.cancel_previous_commands(update, context, "start")
        
        # Get channel link using the helper method
        channel_link = self.get_channel_link()
        
        # Format the channel info text
        channel_info = f"Channel: {channel_link}"
            
        welcome_message = (
            f"Hai! Selamat datang di Gigsta Bot!\n\n"
            f"Saya bisa membantu Anda:\n"
            f"1. Berlangganan notifikasi lowongan berdasarkan kata kunci 🔔\n"
            f"2. Posting lowongan kerja Anda ke channel kami dan ke para subscribers 📝\n"
            f"3. Mengelola lowongan yang sudah Anda posting 🗂️\n\n"
            f"{channel_info}\n\n"
            f"Perintah yang tersedia:\n"
            f"/subscribe - Berlangganan notifikasi lowongan berdasarkan kata kunci\n"
            f"/mysubscriptions - Lihat langganan kata kunci Anda\n"
            f"/unsubscribe - Berhenti berlangganan kata kunci\n"
            f"/post - Posting lowongan kerja baru\n"
            f"/myjobs - Lihat dan kelola lowongan yang telah Anda posting"
        )
        await update.message.reply_text(welcome_message)

    async def subscribe(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        # Cancel any previous commands
        await self.cancel_previous_commands(update, context, "subscribe")
        
        user_id = str(update.effective_user.id)
        await update.message.reply_text(
            "Silakan ketik kata kunci untuk lowongan yang ingin Anda dapatkan notifikasinya.\n"
            "Contoh: 'Javascript', 'Marketing', 'Remote'"
        )
        context.user_data['awaiting_keywords'] = True

    async def handle_subscription(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        user_id = str(update.effective_user.id)
        
        # Check if user is in a conversation flow
        if user_id in self.user_states:
            await self.handle_job_posting_flow(update, context)
            return
            
        if context.user_data.get('awaiting_keywords'):
            keywords = update.message.text.lower()
            
            # Save to database
            success = self.db.save_subscription(user_id, keywords)
            
            if success:
                # Update local cache
                if user_id not in self.subscriptions:
                    self.subscriptions[user_id] = []
                
                self.subscriptions[user_id].append(keywords)
                
                await update.message.reply_text(
                    f"Anda akan menerima notifikasi untuk lowongan dengan kata kunci: \"{keywords}\"\n\n"
                    f"⚠️ PENTING: Pastikan Anda telah mengaktifkan notifikasi untuk bot ini di pengaturan Telegram, "
                    f"karena semua pemberitahuan lowongan baru akan dikirim melalui bot ini."
                )
            else:
                await update.message.reply_text(
                    "Maaf, ada masalah saat menyimpan kata kunci Anda. Silakan coba lagi nanti."
                )
            
            context.user_data['awaiting_keywords'] = False

    async def unsubscribe(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        # Cancel any previous commands
        await self.cancel_previous_commands(update, context, "unsubscribe")
        
        user_id = str(update.effective_user.id)
        
        # Get subscriptions from database
        user_subscriptions = self.db.get_user_subscriptions(user_id)
        
        if user_subscriptions:
            keyboard = []
            for keywords in user_subscriptions:
                keyboard.append([InlineKeyboardButton(keywords, callback_data=f"unsub_{keywords}")])
            
            reply_markup = InlineKeyboardMarkup(keyboard)
            await update.message.reply_text(
                "Pilih kata kunci yang ingin Anda hapus dari notifikasi:",
                reply_markup=reply_markup
            )
        else:
            await update.message.reply_text(
                "Anda belum berlangganan notifikasi untuk kata kunci apapun.\n"
                "Gunakan perintah /subscribe untuk berlangganan notifikasi lowongan."
            )

    async def my_subscriptions(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Display a user's current subscriptions"""
        # Cancel any previous commands
        await self.cancel_previous_commands(update, context, "my_subscriptions")
        
        user_id = str(update.effective_user.id)
        
        # Get subscriptions from database
        user_subscriptions = self.db.get_user_subscriptions(user_id)
        
        if user_subscriptions:
            subscription_list = ", ".join([f'"{sub}"' for sub in user_subscriptions])
            await update.message.reply_text(
                f"Anda saat ini berlangganan notifikasi untuk kata kunci berikut:\n\n"
                f"{subscription_list}\n\n"
                f"Gunakan /unsubscribe untuk berhenti berlangganan kata kunci."
            )
        else:
            await update.message.reply_text(
                "Anda belum berlangganan notifikasi untuk kata kunci apapun.\n"
                "Gunakan perintah /subscribe untuk berlangganan notifikasi lowongan."
            )

    async def handle_callback(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle callback queries from inline buttons"""
        query = update.callback_query
        user_id = str(query.from_user.id)
        
        # Handle unsubscribe buttons
        if query.data.startswith("unsub_"):
            await query.answer()
            keywords = query.data[6:]
            
            # Delete from database
            success = self.db.delete_subscription(user_id, keywords)
            
            if success:
                # Update local cache
                if user_id in self.subscriptions and keywords in self.subscriptions[user_id]:
                    self.subscriptions[user_id].remove(keywords)
                
                # Check if user still has other subscriptions
                remaining_subscriptions = self.db.get_user_subscriptions(user_id)
                if remaining_subscriptions:
                    subscription_list = ", ".join([f'"{sub}"' for sub in remaining_subscriptions])
                    await query.edit_message_text(
                        f"Anda tidak akan lagi menerima notifikasi untuk: \"{keywords}\"\n\n"
                        f"Anda masih berlangganan notifikasi untuk kata kunci: {subscription_list}"
                    )
                else:
                    await query.edit_message_text(
                        f"Anda tidak akan lagi menerima notifikasi untuk: \"{keywords}\"\n\n"
                        f"Anda tidak memiliki langganan kata kunci yang aktif. Gunakan /subscribe untuk berlangganan lagi."
                    )
            else:
                await query.edit_message_text("Maaf, ada masalah saat menghapus kata kunci Anda. Silakan coba lagi nanti.")
        
        # Handle contact type selection for job posting
        elif query.data.startswith("contact_type_"):
            contact_type = query.data.replace("contact_type_", "")
            
            # Update user state
            if user_id in self.user_states:
                self.user_states[user_id]["is_email"] = (contact_type == "email")
                self.user_states[user_id]["current_step"] = "contact"
                
                contact_type_text = "email" if contact_type == "email" else "website atau URL"
                await query.answer(f"Jenis kontak dipilih: {contact_type_text}")
                
                await query.edit_message_text(
                    f"Silakan masukkan {contact_type_text} untuk melamar pekerjaan ini:"
                )
            else:
                await query.answer("Silakan mulai lagi dengan /post")
        
        # Handle job posting confirmation
        elif query.data == "post_job_confirm":
            await query.answer()
            await self.finish_job_posting(update, context)
        elif query.data == "post_job_cancel":
            await query.answer()
            await self.cancel_job_posting(update, context)
        
        # Handle job deletion
        elif query.data.startswith("delete_job_"):
            await query.answer()
            job_id = int(query.data.replace("delete_job_", ""))
            
            # Delete the job
            message_id = self.db.delete_posted_job(job_id, user_id)
            
            if message_id:
                # Try to delete the message from the channel
                try:
                    # Convert message_id to integer if it's a string
                    message_id_int = int(message_id) if message_id else None
                    
                    if message_id_int:
                        await context.bot.delete_message(chat_id=CHANNEL_ID, message_id=message_id_int)
                        await query.edit_message_text("Lowongan berhasil dihapus dari channel.")
                    else:
                        await query.edit_message_text("Lowongan dihapus dari database, tetapi tidak ada pesan di channel.")
                except Exception as e:
                    logger.error(f"Error deleting message from channel: {e}")
                    await query.edit_message_text("Lowongan dihapus dari database, tetapi gagal menghapus dari channel.")
            else:
                await query.edit_message_text("Gagal menghapus lowongan. Silakan coba lagi nanti!")

        # Handle URL selection
        elif query.data.startswith("select_url_"):
            await query.answer()
            url_index = int(query.data.replace("select_url_", ""))
            
            # Update user state with the selected URL
            if user_id in self.user_states:
                state = self.user_states[user_id]
                if "pending_urls" in state:
                    selected_url = state["pending_urls"][url_index]
                    state["contact"] = selected_url
                    state["is_email"] = False
                    del state["pending_urls"]
                    
                    # Move to confirmation stage
                    state["current_step"] = "confirmation_stage"
                    
                    # Show confirmation buttons
                    keyboard = [
                        [InlineKeyboardButton("✅ Posting Sekarang", callback_data="post_job_confirm")],
                        [InlineKeyboardButton("❌ Batal", callback_data="post_job_cancel")]
                    ]
                    reply_markup = InlineKeyboardMarkup(keyboard)
                    
                    # Check if we have an image
                    image_info = "✓ Gambar poster diterima" if state["poster_file_id"] else "✗ Tanpa gambar poster"
                    
                    await query.edit_message_text(
                        f"📝 Deskripsi lowongan telah diterima\n"
                        f"📷 {image_info}\n"
                        f"📧 Website kontak: {selected_url}\n\n"
                        f"Apakah Anda ingin memposting lowongan ini sekarang?",
                        reply_markup=reply_markup,
                        parse_mode='Markdown'
                    )
                else:
                    await query.edit_message_text("Silakan mulai lagi dengan /post")
            else:
                await query.answer("Silakan mulai lagi dengan /post")

    async def post_job(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Start the job posting flow with simplified free text input"""
        # Cancel any previous commands
        await self.cancel_previous_commands(update, context, "post_job")
        
        user_id = str(update.effective_user.id)
        
        # Initialize user state for job posting flow
        self.user_states[user_id] = {
            "current_step": "input_stage",
            "content": None,
            "poster_file_id": None,  # Store file_id for uploaded images
            "contact": None,
            "is_email": None
        }
        
        await update.message.reply_text(
            "Mari posting lowongan baru! 📝\n\n"
            "Silakan ketik deskripsi lengkap lowongan Anda. Anda dapat menyertakan:\n"
            "• Posisi yang dibuka (nama atau judul pekerjaan)\n"
            "• Deskripsi tugas dan tanggung jawab\n"
            "• Persyaratan kandidat\n"
            "• Kontak untuk melamar (email atau website)\n\n"
            "Anda juga dapat menambahkan satu gambar (jika ada)."
        )
    
    async def handle_job_posting_flow(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle the job posting conversation flow"""
        user_id = str(update.effective_user.id)
        
        # Get current user state
        if user_id not in self.user_states:
            # If user doesn't have a state, they need to start with /post
            if hasattr(update, 'message') and update.message:
                await update.message.reply_text("Silakan gunakan /post untuk mulai posting lowongan.")
            elif hasattr(update, 'callback_query') and update.callback_query:
                await update.callback_query.answer("Silakan gunakan /post untuk mulai posting lowongan.")
            return
            
        state = self.user_states[user_id]
        current_step = state["current_step"]
        
        # We only process inputs during the input stage
        if current_step != "input_stage":
            return
            
        # Handle photo uploads with captions
        if hasattr(update, 'message') and update.message and update.message.photo:
            # Check if the photo has a caption
            if update.message.caption:
                # Get the largest photo (best quality)
                photo = update.message.photo[-1]
                state["poster_file_id"] = photo.file_id
                
                # Use the caption as content
                content = update.message.caption
                state["content"] = content
                
                # Extract contact info from caption
                await self._process_content_and_move_to_confirmation(update, context, content)
            else:
                # They sent an image without caption
                await update.message.reply_text(
                    "⚠️ Harap sertakan caption yang berisi deskripsi lowongan.\n"
                    "Silakan kirim ulang gambar beserta caption deskripsi."
                )
            return
            
        # Check if they're uploading an image as a file/document instead of as a photo
        if hasattr(update, 'message') and update.message and update.message.document:
            # Check if it's an image file
            mime_type = update.message.document.mime_type
            if mime_type and mime_type.startswith('image/'):
                await update.message.reply_text(
                    "⚠️ Mohon kirim gambar sebagai foto, bukan sebagai file."
                )
            else:
                await update.message.reply_text(
                    "⚠️ Format posting lowongan hanya menerima teks atau foto dengan caption.\n\n"
                    "Mohon kirim deskripsi lowongan sebagai teks biasa atau sebagai caption pada foto jika ingin menyertakan gambar."
                )
            return
            
        # Handle text messages
        if hasattr(update, 'message') and update.message and update.message.text:
            content = update.message.text
            
            # Save job content
            state["content"] = content
            
            # Process content and move to confirmation
            await self._process_content_and_move_to_confirmation(update, context, content)
    
    async def _process_content_and_move_to_confirmation(self, update, context, content):
        """Process job content and move to confirmation stage if valid"""
        user_id = str(update.effective_user.id)
        state = self.user_states[user_id]
        
        # Extract contact information from the text (email or URL) using AI with fallback to rule-based
        try:
            valid_emails, valid_urls = extract_contact_info(content)
            
            # Log which extraction method was used (for debugging)
            if is_feature_enabled("debug_logging"):
                logger.debug(f"Contact extraction found {len(valid_emails)} emails and {len(valid_urls)} URLs")
        except Exception as e:
            # Log the error and fall back to rule-based extraction
            logger.error(f"Error in AI contact extraction: {e}")
            valid_emails, valid_urls = rule_based_extract(content)
        
        # Check if we have valid contact information
        if not valid_emails and not valid_urls:
            # No valid contact information found
            await update.message.reply_text(
                "⚠️ PERHATIAN: Tidak ditemukan informasi kontak (email atau website) dalam deskripsi Anda.\n\n"
                "Informasi kontak diperlukan agar pelamar dapat menghubungi Anda. "
                "Silakan kirim deskripsi baru yang menyertakan alamat email atau website untuk melamar."
            )
            # Keep the user in the input stage to allow them to try again
            state["current_step"] = "input_stage"
            return
            
        # Prioritize email over URL
        if valid_emails:
            # If we have a valid email, use it regardless of URLs
            state["contact"] = valid_emails[0]
            state["is_email"] = True
            await self._move_to_confirmation_stage(update, context)
        elif len(valid_urls) > 1:
            # If no email but multiple URLs, let the user choose which one to use
            # Save the content and URLs in state to use later
            state["pending_urls"] = valid_urls
            
            # Create buttons for each URL
            keyboard = []
            for i, url in enumerate(valid_urls):
                # Trim URL for display if too long
                display_url = url
                if len(display_url) > 30:
                    display_url = display_url[:27] + "..."
                keyboard.append([InlineKeyboardButton(f"{i+1}. {display_url}", callback_data=f"select_url_{i}")])
            
            reply_markup = InlineKeyboardMarkup(keyboard)
            
            await update.message.reply_text(
                "Ditemukan beberapa URL dalam deskripsi Anda. Silakan pilih URL mana yang akan digunakan sebagai kontak untuk melamar pekerjaan:",
                reply_markup=reply_markup
            )
        else:
            # No email but one URL
            state["contact"] = valid_urls[0]
            state["is_email"] = False
            await self._move_to_confirmation_stage(update, context)
    
    async def _move_to_confirmation_stage(self, update, context):
        """Move to confirmation stage after valid input"""
        user_id = str(update.effective_user.id)
        state = self.user_states[user_id]
        
        # Move to confirmation stage
        state["current_step"] = "confirmation_stage"
        
        # Create confirmation buttons
        keyboard = [
            [InlineKeyboardButton("✅ Posting Sekarang", callback_data="post_job_confirm")],
            [InlineKeyboardButton("❌ Batal", callback_data="post_job_cancel")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        # Include contact info in confirmation message
        contact_type = "Email" if state["is_email"] else "Website"
        
        # Check if we have an image
        image_info = "✓ Gambar poster diunggah" if state["poster_file_id"] else "✗ Tanpa gambar poster"
        
        await update.message.reply_text(
            f"📝 Deskripsi lowongan telah diterima\n"
            f"📷 {image_info}\n"
            f"📧 {contact_type} kontak: {state['contact']}\n\n"
            f"Apakah Anda ingin memposting lowongan ini sekarang?",
            reply_markup=reply_markup,
            parse_mode='Markdown'
        )
    
    async def finish_job_posting(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Finalize the job posting process after confirmation"""
        query = update.callback_query
        user_id = str(query.from_user.id)
        
        if user_id not in self.user_states:
            await query.edit_message_text("Sesi posting lowongan telah berakhir. Silakan gunakan /post untuk memulai lagi.")
            return
            
        state = self.user_states[user_id]
        
        # Get the full content
        content = state["content"]
        if not content:
            await query.edit_message_text("Tidak ada konten lowongan yang diberikan. Posting dibatalkan.")
            del self.user_states[user_id]
            return
        
        # Use first line or first 50 chars for database title (for reference only)
        if "\n" in content:
            title = content.split("\n")[0][:100]  # Limit title to 100 chars
        else:
            title = content[:50] + ("..." if len(content) > 50 else "")
        
        # Check if we have contact information
        if not state["contact"]:
            # Try to ask the user for contact
            await query.edit_message_text(
                "Untuk menyelesaikan posting, diperlukan kontak untuk melamar (email atau website).\n"
                "Silakan gunakan /post untuk memulai lagi dan sertakan kontak di deskripsi lowongan."
            )
            del self.user_states[user_id]
            return
        
        # Post the job
        job_id = self.db.post_job(
            user_id=user_id,
            title=title,
            description=content,  # Use full content as description
            poster_file_id=state["poster_file_id"],
            contact=state["contact"],
            is_email=state["is_email"]
        )
        
        if job_id:
            # Post to channel
            await self.post_job_to_channel(context, job_id)
            
            # Notify subscribers
            await self.notify_subscribers(context, job_id)
            
            await query.edit_message_text(
                "🎉 Lowongan kerja Anda berhasil diposting!\n\n"
                f"✅ Telah dibagikan ke channel {self.get_channel_link()}\n"
                "✅ Notifikasi telah dikirim ke pengguna yang berlangganan kata kunci terkait"
            )
        else:
            await query.edit_message_text(
                "Maaf, ada masalah saat posting lowongan Anda. Silakan coba lagi nanti."
            )
        
        # Clear user state
        del self.user_states[user_id]
    
    async def cancel_job_posting(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Cancel the job posting process"""
        query = update.callback_query
        user_id = str(query.from_user.id)
        
        if user_id in self.user_states:
            del self.user_states[user_id]
        
        await query.edit_message_text("Posting lowongan dibatalkan.")
    
    async def my_jobs(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Show the user's posted jobs"""
        # Cancel any previous commands
        await self.cancel_previous_commands(update, context, "my_jobs")
        
        user_id = str(update.effective_user.id)
        
        # Get user's posted jobs
        jobs = self.db.get_user_posted_jobs(user_id)
        
        if not jobs:
            await update.message.reply_text(
                "Anda belum posting lowongan apapun. Gunakan /post untuk membuat lowongan pertama Anda!"
            )
            return
        
        # Create a list of jobs with delete buttons
        for job in jobs:
            keyboard = [[InlineKeyboardButton("🗑️ Hapus", callback_data=f"delete_job_{job['id']}")]]
            reply_markup = InlineKeyboardMarkup(keyboard)
            
            # Escape special Markdown characters
            escaped_description = job['description'].replace('*', '\\*').replace('_', '\\_').replace('`', '\\`').replace('[', '\\[')
            
            job_message = (
                f"📅 Diposting pada: {job['created_at'][:10]}\n\n"
                f"{escaped_description}\n\n"
            )
            
            # Check if the job has an image (poster_file_id)
            if job.get('poster_file_id'):
                # Send the image with the job description as caption
                await update.message.reply_photo(
                    photo=job['poster_file_id'],
                    caption=job_message,
                    reply_markup=reply_markup,
                    parse_mode='Markdown'
                )
            else:
                # No image, just send the text
                await update.message.reply_text(
                    job_message,
                    reply_markup=reply_markup,
                    parse_mode='Markdown'
                )
    
    async def post_job_to_channel(self, context: ContextTypes.DEFAULT_TYPE, job_id: int):
        """Post a job to the channel and update the job with the message ID"""
        job = self.db.get_job_details(job_id)
        if not job:
            logger.error(f"Failed to get job details for job ID {job_id}")
            return
        
        # Escape special Markdown characters in content
        escaped_content = job['description'].replace('*', '\\*').replace('_', '\\_').replace('`', '\\`').replace('[', '\\[')
        
        # Create job message as free text without any title
        job_message = f"{escaped_content}\n\n"
        
        # Set up reply markup for clickable button if we have a URL
        reply_markup = None
        if not job['is_email']:
            button_text = "🔗 Lamar Sekarang"
            url = job['contact'] if job['contact'].startswith(('http://', 'https://')) else f"https://{job['contact']}"
            keyboard = [[InlineKeyboardButton(button_text, url=url)]]
            reply_markup = InlineKeyboardMarkup(keyboard)
        
        # Check if we have a poster file_id
        poster_file_id = job.get('poster_file_id')
        
        if poster_file_id:
            try:
                # Send using file_id
                message = await context.bot.send_photo(
                    chat_id=CHANNEL_ID,
                    photo=poster_file_id,
                    caption=job_message,
                    reply_markup=reply_markup,
                    parse_mode='Markdown'
                )
                
                # Update job with message ID
                self.db.update_job_message_id(job_id, str(message.message_id))
                logger.info(f"Posted job {job_id} to channel with message ID {message.message_id}")
                return
            except Exception as e:
                logger.error(f"Error sending job poster image: {e}")
                # Continue with text-only message if image fails
        
        # Send to channel (text only if no poster or if poster image failed)
        try:
            message = await context.bot.send_message(
                chat_id=CHANNEL_ID,
                text=job_message,
                reply_markup=reply_markup,
                parse_mode='Markdown'
            )
            
            # Update job with message ID
            self.db.update_job_message_id(job_id, str(message.message_id))
            logger.info(f"Posted job {job_id} to channel with message ID {message.message_id}")
        except Exception as e:
            logger.error(f"Error posting job to channel: {e}")
    
    async def notify_subscribers(self, context: ContextTypes.DEFAULT_TYPE, job_id: int):
        """Notify subscribers about a new job posting"""
        job = self.db.get_job_details(job_id)
        if not job:
            logger.error(f"Failed to get job details for job ID {job_id}")
            return
        
        # Get all subscriptions
        all_subscriptions = self.db.load_subscriptions()
        notified_users = set()
        
        for user_id, keywords_list in all_subscriptions.items():
            # Skip if already notified
            if user_id in notified_users:
                continue
            
            # Check if any keywords match the job content
            job_text = job['description'].lower()
            
            for keywords in keywords_list:
                if keywords.lower() in job_text:
                    # Escape special Markdown characters in content
                    escaped_content = job['description'].replace('*', '\\*').replace('_', '\\_').replace('`', '\\`').replace('[', '\\[')
                    
                    # Create notification message as free text
                    notification = (
                        f"🔔 *Lowongan Kerja Baru Sesuai Kata Kunci Anda*\n\n"
                        f"{escaped_content}\n\n"
                    )
                    
                    # Set up reply markup for clickable button if we have a URL
                    reply_markup = None
                    if not job['is_email']:
                        button_text = "🔗 Lamar Sekarang"
                        url = job['contact'] if job['contact'].startswith(('http://', 'https://')) else f"https://{job['contact']}"
                        keyboard = [[InlineKeyboardButton(button_text, url=url)]]
                        reply_markup = InlineKeyboardMarkup(keyboard)
                    
                    # Send notification
                    try:
                        # Check if we have a poster file_id
                        poster_file_id = job.get('poster_file_id')
                        
                        if poster_file_id:
                            try:
                                # Send using file_id
                                await context.bot.send_photo(
                                    chat_id=user_id,
                                    photo=poster_file_id,
                                    caption=notification,
                                    reply_markup=reply_markup,
                                    parse_mode='Markdown'
                                )
                                logger.info(f"Sent job notification with image to user {user_id}")
                                notified_users.add(user_id)
                                break  # Break after first match to avoid duplicate notifications
                            except Exception as e:
                                logger.error(f"Error sending notification with image to user {user_id}: {e}")
                                # Continue with text-only message if image fails
                        
                        # Send text-only notification if no poster or if poster image failed
                        await context.bot.send_message(
                            chat_id=user_id,
                            text=notification,
                            reply_markup=reply_markup,
                            parse_mode='Markdown'
                        )
                        logger.info(f"Sent job notification to user {user_id}")
                        notified_users.add(user_id)
                        break  # Break after first match to avoid duplicate notifications
                    except Exception as e:
                        logger.error(f"Error sending notification to user {user_id}: {e}")
    
    async def send_daily_updates(self, context: ContextTypes.DEFAULT_TYPE):
        logger.info("Starting to collect jobs...")
        # Collect jobs from all sources
        all_jobs = []
        
        # Collect jobs from Glints
        glints_jobs = await self.scraper.scrape_glints()
        if glints_jobs:
            all_jobs.extend(glints_jobs)
            logger.info(f"Collected {len(glints_jobs)} jobs from Glints")
        
        # Collect jobs from Jobstreet
        jobstreet_jobs = await self.scraper.scrape_jobstreet()
        if jobstreet_jobs:
            all_jobs.extend(jobstreet_jobs)
            logger.info(f"Collected {len(jobstreet_jobs)} jobs from Jobstreet")
        
        if not all_jobs:
            logger.info("No jobs found to send")
            return

        # Save jobs to scraped_jobs database and get only new ones
        new_jobs = self.db.save_jobs(all_jobs)
        
        if not new_jobs:
            logger.info("No new jobs to send - all jobs already exist in database")
            return
            
        logger.info(f"Found {len(new_jobs)} new jobs out of {len(all_jobs)} total scraped jobs")

        # Format and send message
        message_header = f"🔥 {len(new_jobs)} Lowongan Kerja Terbaru! 🔥\n\n"
        
        # Create individual job messages
        job_messages = []
        for job in new_jobs:
            job_message = (
                f"💼 {job['title']}\n"
                f"🏢 {job['company']}\n"
                f"📍 {job['location']}\n"
                f"🔗 {job['source']}\n"
                f"Link: {job['url']}\n\n"
            )
            job_messages.append(job_message)
        
        # Group job messages into chunks that fit within Telegram's limit
        # Telegram has a 4096 character limit per message
        messages = []
        current_message = message_header
        
        for job_message in job_messages:
            # Check if adding this job would exceed the limit
            if len(current_message) + len(job_message) > 4000:  # Using 4000 to leave some buffer
                messages.append(current_message)
                current_message = message_header + job_message
            else:
                current_message += job_message
        
        # Add the last message if it's not empty
        if current_message != message_header:
            messages.append(current_message)
        
        # Send all messages
        for msg in messages:
            await context.bot.send_message(
                chat_id=CHANNEL_ID,
                text=msg,
                disable_web_page_preview=True
            )
        
        logger.info(f"Sent {len(new_jobs)} new job listings to channel")

    async def schedule_daily_jobs(self, context: ContextTypes.DEFAULT_TYPE):
        # Clear existing jobs (except this scheduler)
        current_jobs = context.job_queue.jobs()
        for job in current_jobs:
            if job.name != "midnight_scheduler":
                job.schedule_removal()
        
        if is_feature_enabled("short_intervals"):
            # Development mode: Schedule jobs at short intervals for testing
            logger.info("Using short intervals for job scheduling (development mode)")
            
            # Get current time
            now = datetime.now(pytz.timezone('Asia/Jakarta'))
            
            # Schedule 3 jobs within the next 15 minutes
            intervals = [5, 10, 15]  # minutes
            
            for i, minutes in enumerate(intervals):
                job_time = now + timedelta(minutes=minutes)
                random_seconds = random.randint(0, 59)
                job_time = job_time.replace(second=random_seconds)
                
                job_name = f"test_update_{i+1}"
                logger.info(f"Scheduling job {job_name} for {job_time.strftime('%H:%M:%S')}")
                
                context.job_queue.run_once(
                    self.send_daily_updates,
                    when=job_time,
                    name=job_name
                )
        else:
            # Production mode: Schedule jobs at regular times
            logger.info("Using regular intervals for job scheduling (production mode)")
            
            # Random times within specified windows
            morning_hour = random.randint(7, 9)
            afternoon_hour = random.randint(12, 14)
            evening_hour = random.randint(18, 20)
            
            morning_minute = random.randint(0, 59)
            afternoon_minute = random.randint(0, 59)
            evening_minute = random.randint(0, 59)
            
            logger.info(f"Scheduled jobs for today at: {morning_hour}:{morning_minute}, {afternoon_hour}:{afternoon_minute}, {evening_hour}:{evening_minute}")
            
            # Schedule the jobs for today
            context.job_queue.run_daily(
                self.send_daily_updates, 
                time=datetime_time(hour=morning_hour, minute=morning_minute, tzinfo=pytz.timezone('Asia/Jakarta')),
                name="morning_update"
            )
            context.job_queue.run_daily(
                self.send_daily_updates, 
                time=datetime_time(hour=afternoon_hour, minute=afternoon_minute, tzinfo=pytz.timezone('Asia/Jakarta')),
                name="afternoon_update"
            )
            context.job_queue.run_daily(
                self.send_daily_updates, 
                time=datetime_time(hour=evening_hour, minute=evening_minute, tzinfo=pytz.timezone('Asia/Jakarta')),
                name="evening_update"
            )

    async def cancel_previous_commands(self, update: Update, context: ContextTypes.DEFAULT_TYPE, new_command: str):
        """Cancel any previous commands and their steps when a new command is called"""
        user_id = str(update.effective_user.id)
        
        # Check if user has an active command
        if user_id in self.active_commands:
            previous_command = self.active_commands[user_id]
            logger.info(f"Canceling previous command '{previous_command}' for user {user_id}")
            
            # Clear user state if they were in a conversation flow
            if user_id in self.user_states:
                del self.user_states[user_id]
                logger.info(f"Cleared conversation state for user {user_id}")
            
            # Clear any awaiting flags in user_data
            if 'awaiting_keywords' in context.user_data:
                context.user_data['awaiting_keywords'] = False
        
        # Set the new active command
        self.active_commands[user_id] = new_command
        logger.info(f"Set active command to '{new_command}' for user {user_id}")

async def post_init(application: Application) -> None:
    """Set up the bot menu commands that appear in the chat interface"""
    commands = [
        ("start", "Mulai bot dan lihat bantuan"),
        ("subscribe", "Berlangganan kata kunci"),
        ("mysubscriptions", "Lihat langganan kata kunci Anda"),
        ("unsubscribe", "Berhenti berlangganan kata kunci"),
        ("post", "Posting lowongan kerja baru"),
        ("myjobs", "Lihat dan kelola lowongan Anda"),
    ]
    
    await application.bot.set_my_commands(commands)
    logger.info("Bot commands menu has been set up")


def main():
    """Run the bot."""
    logger.info("Starting bot...")
    
    # Create the Application
    application = Application.builder().token(TELEGRAM_TOKEN).post_init(post_init).build()
    
    # Create bot instance
    bot = JobBot()
    
    # Add command handlers
    application.add_handler(CommandHandler("start", bot.start))
    application.add_handler(CommandHandler("subscribe", bot.subscribe))
    application.add_handler(CommandHandler("mysubscriptions", bot.my_subscriptions))
    application.add_handler(CommandHandler("unsubscribe", bot.unsubscribe))
    application.add_handler(CommandHandler("post", bot.post_job))
    application.add_handler(CommandHandler("myjobs", bot.my_jobs))
    
    # Add callback query handler for buttons
    application.add_handler(CallbackQueryHandler(bot.handle_callback))
    
    # Add message handlers
    application.add_handler(MessageHandler(filters.TEXT & ~filters.COMMAND, bot.handle_subscription))
    
    # Job posting related handlers - text, photo, and document uploads are handled by handle_job_posting_flow
    application.add_handler(MessageHandler(filters.PHOTO, bot.handle_job_posting_flow))
    application.add_handler(MessageHandler(filters.Document.ALL, bot.handle_job_posting_flow))
    
    # Schedule daily updates
    job_queue = application.job_queue
    jakarta_tz = pytz.timezone('Asia/Jakarta')
    
    # Schedule the midnight job that will set up each day's random times
    # job_queue.run_daily(
    #     bot.schedule_daily_jobs, 
    #     time=datetime_time(hour=0, minute=1, tzinfo=jakarta_tz),
    #     name="midnight_scheduler"
    # )
    
    # Run the scheduler once at startup to set up today's jobs
    # application.job_queue.run_once(bot.schedule_daily_jobs, when=1)
    
    # Log environment information
    logger.info(f"Bot started in {ENV} environment")
    if ENV == "development":
        logger.info("Development mode active - using shorter job intervals")
    else:
        logger.info("Production mode active - using regular job schedule")
    
    # Start the Bot
    logger.info("Starting polling...")
    application.run_polling(allowed_updates=Update.ALL_TYPES)

if __name__ == '__main__':
    main()
