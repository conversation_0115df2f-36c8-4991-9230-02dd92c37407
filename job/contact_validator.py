import re
import logging
from urlextract import U<PERSON><PERSON>xtract
from email_validator import validate_email, EmailNotValidError
import validators  # We still need this for some edge cases

logger = logging.getLogger(__name__)

# Initialize URL extractor once (loads TLD list)
extractor = URLExtract()
# Enable recognition of international domains 
extractor.update_when_older(30)  # Update TLD list if older than 30 days

def extract_contact_info(text):
    """
    Extract and validate contact information (emails and URLs) from text.
    
    Args:
        text (str): Text content to extract contact info from
        
    Returns:
        tuple: (valid_emails, valid_urls) - Lists of validated emails and URLs
    """
    if not text:
        return [], []
        
    # Find potential emails using regex (more lenient than validation)
    email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
    potential_emails = re.findall(email_pattern, text)
    
    # Validate emails using email-validator (much more strict)
    valid_emails = []
    for email in potential_emails:
        try:
            # Validate and normalize the email
            validated = validate_email(email, check_deliverability=False)
            # Force lowercase for consistency with tests
            normalized_email = validated.normalized.lower()
            valid_emails.append(normalized_email)
            logger.debug(f"Valid email found: {normalized_email}")
        except EmailNotValidError:
            # Not a valid email
            pass
    
    # Extract URLs using urlextract (better than regex for URLs)
    # Create a modified text with emails removed to avoid conflicts
    modified_text = text
    for email in potential_emails:
        modified_text = modified_text.replace(email, " ")
    
    # First extract using URLExtract
    potential_urls = extractor.find_urls(modified_text, only_unique=True)
    
    # For cases where URLs are adjacent to text without spaces, we need a fallback
    # Look for patterns that might be domains but weren't properly extracted
    if not potential_urls or "example.com" in text.lower() and "example.com" not in [u.lower() for u in potential_urls]:
        domain_pattern = r'[a-zA-Z0-9][a-zA-Z0-9-]*\.[a-zA-Z0-9-]+(\.[a-zA-Z0-9-]+)+'
        additional_domains = re.findall(domain_pattern, text)
        
        # Check each word for potential domains
        for word in re.split(r'\s+', text):
            # Check if there's a domain-like pattern embedded in the word
            for part in re.split(r'([^\w.-])', word):
                if '.' in part and len(part) > 3:
                    if validators.domain(part):
                        potential_urls.append(part)
                        
        # Look specifically for international domains
        for word in re.split(r'\s+', text):
            clean_word = word.strip('.,;:()"\'-')
            if '.' in clean_word and len(clean_word) > 3:
                # Try to validate as a URL
                if validators.url(f"https://{clean_word}"):
                    potential_urls.append(clean_word)
    
    # Normalize and validate URLs
    valid_urls = []
    processed_urls = set()  # To track URLs we've already processed
    
    for url in potential_urls:
        # Skip empty or too short URLs
        if not url or len(url) < 4:
            continue
            
        # Add https:// prefix if missing
        if not url.startswith(('http://', 'https://')):
            url = f"https://{url}"
            
        # Normalize to lowercase for consistent comparison
        url = url.lower()
        
        # Skip if we've already processed this exact URL
        if url in processed_urls:
            continue
            
        # For URLs with paths, make sure we don't add the base domain separately
        base_domain = url.split('/')[2]
        base_url = f"https://{base_domain}"
        
        # Check if this is a domain with path, and if we should skip the base domain
        has_path = len(url.split('/')) > 3 and url != base_url
        
        # Basic validation checks
        try:
            if validators.url(url):
                # If this is a URL with path, remove any base domain URLs we may have added
                if has_path and base_url in valid_urls:
                    valid_urls.remove(base_url)
                    
                valid_urls.append(url)
                processed_urls.add(url)
                # Also mark the base domain as processed so we don't add it later
                processed_urls.add(base_url)
                logger.debug(f"Valid URL found: {url}")
        except (IndexError, ValueError):
            # Malformed URL
            pass
    
    logger.debug(f"Extracted {len(valid_emails)} emails and {len(valid_urls)} URLs")
    return valid_emails, valid_urls
