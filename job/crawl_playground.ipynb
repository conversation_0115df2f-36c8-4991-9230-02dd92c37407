{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import os\n", "import asyncio\n", "from crawl4ai import AsyncWebCrawler, BrowserConfig, CrawlerRunConfig, CacheMode, LLMConfig\n", "from crawl4ai.extraction_strategy import LLMExtractionStrategy\n", "from pydantic import BaseModel, Field"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["class JobDetails(BaseModel):\n", "    url: str = Field(..., description=\"URL of the job.\")\n", "    title: str = Field(..., description=\"Title of the job.\")\n", "    description: str = Field(..., description=\"Description of the job.\")\n", "    company: str = Field(..., description=\"Company of the job.\")\n", "    location: str = Field(..., description=\"Location of the job.\")\n", "    source: str = Field(..., description=\"Source of the job.\")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["from crawl4ai.async_crawler_strategy import AsyncPlaywrightCrawlerStrategy\n", "from crawl4ai.browser_manager import BrowserManager\n", "\n", "\n", "async def patched_async_playwright__crawler_strategy_close(self) -> None:\n", "    \"\"\"\n", "    Close the browser and clean up resources.\n", "\n", "    This patch addresses an issue with Playwright instance cleanup where the static instance\n", "    wasn't being properly reset, leading to issues with multiple crawls.\n", "\n", "    Issue: https://github.com/unclecode/crawl4ai/issues/842\n", "\n", "    Returns:\n", "        None\n", "    \"\"\"\n", "    await self.browser_manager.close()\n", "\n", "    # Reset the static Playwright instance\n", "    BrowserManager._playwright_instance = None\n", "\n", "\n", "AsyncPlaywrightCrawlerStrategy.close = patched_async_playwright__crawler_strategy_close"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/var/folders/m3/mg42bl9s1sd0m2f9yh22c7pm0000gn/T/ipykernel_33084/2395742558.py:8: PydanticDeprecatedSince20: The `schema` method is deprecated; use `model_json_schema` instead. Deprecated in Pydantic V2.0 to be removed in V3.0. See Pydantic V2 Migration Guide at https://errors.pydantic.dev/2.11/migration/\n", "  schema=JobDetails.schema(),\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[INIT].... → Crawl4AI 0.5.0.post8\n", "[FETCH]... ↓ https://glints.com/id/opportunities/jobs/java-deve... | Status: True | Time: 9.57s\n", "[SCRAPE].. ◆ https://glints.com/id/opportunities/jobs/java-deve... | Time: 0.02s\n", "[EXTRACT]. ■ Completed for https://glints.com/id/opportunities/jobs/java-deve... | Time: 2.7981242919995566s\n", "[COMPLETE] ● https://glints.com/id/opportunities/jobs/java-deve... | Status: True | Total: 12.40s\n", "[\n", "    {\n", "        \"url\": \"https://glints.com/id/opportunities/jobs/java-developer/0e2000f7-0337-42ac-bf56-1074017c19e0\",\n", "        \"title\": \"Java Developer\",\n", "        \"description\": \"We are looking for a Java Developer with expertise in Jasper Reports to design, develop, and maintain reporting solutions. The ideal candidate will have experience integrating JasperReports into Java applications, optimizing performance, and collaborating with cross-functional teams to deliver high-quality reports.\\n\\n**Key Responsibilities:**\\n* Design and develop Jasper Reports for various business needs.\\n* Integrate JasperReports into Java applications.\\n* Optimize SQL queries for data extraction and reporting.\\n* Troubleshoot and resolve issues related to reports and data accuracy.\\n* Collaborate with stakeholders to understand and implement reporting requirements.\\n\\n**Qualifications:**\\n* 2+ years of Java development experience with JasperReports.\\n* Strong knowledge of JasperReports and related tools.\\n* Proficient in Java SE/EE and SQL.\\n* Experience with JasperSoft Studio is a plus.\\n* Strong problem-solving and communication skills.\",\n", "        \"company\": \"PT Solusindo Digital Holistik\",\n", "        \"location\": \"Jakarta Selatan, Setiabudi\",\n", "        \"source\": \"Glints\",\n", "        \"error\": false\n", "    }\n", "]\n"]}], "source": ["browser_config = BrowserConfig(verbose=True)\n", "run_config = CrawlerRunConfig(\n", "    word_count_threshold=1,\n", "    extraction_strategy=LLMExtractionStrategy(\n", "        # Here you can use any provider that Litellm library supports, for instance: ollama/qwen2\n", "        # provider=\"ollama/qwen2\", api_token=\"no-token\", \n", "        llm_config = LLMConfig(provider=\"gemini/gemini-2.0-flash\", api_token=os.getenv('DEV_GEMINI_API_KEY')), \n", "        schema=JobDetails.schema(),\n", "        extraction_type=\"schema\",\n", "        instruction=\"\"\"From the crawled content, extract all mentioned job title, description, company, location, and source along with the job post url. \n", "        Do not miss any job information in the entire content. Make sure the job description information is completely scraped and properly formatted. Do not include the text line like \"<PERSON><PERSON><PERSON><PERSON> pek<PERSON>...\" in the description. One extracted job JSON format should look like this: \n", "        {\"url\": \"https://glints.com/id/opportunities/jobs/content-creator-and-video-editor/601ef7af-7c07-4f33-8c95-72da993b792a\", \"title\": \"Content Creator & Video editor\", \"description\": \"Job Description\", \"company\": \"PT Aneka Search Indonesia (ASI Asia Pacific)\", \"location\": \"Jl. Green Village No 19A. Kebon Jeruk. Jakarta Barat\", \"source\": \"Glints\"}.\"\"\"\n", "    ),            \n", "    cache_mode=CacheMode.BYPASS,\n", ")\n", "\n", "async with AsyncWebCrawler(config=browser_config) as crawler:\n", "    result = await crawler.arun(\n", "        url='https://glints.com/id/opportunities/jobs/java-developer/0e2000f7-0337-42ac-bf56-1074017c19e0',\n", "        config=run_config\n", "    )\n", "    print(result.extracted_content)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["<coroutine object run at 0x10eff6200>"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.4"}}, "nbformat": 4, "nbformat_minor": 2}