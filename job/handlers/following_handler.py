"""
Job following handler for Gigsta Job Bot functionality.
"""
import logging
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import ContextTypes
from .base_handler import BaseHandler

# Configure logging
logger = logging.getLogger(__name__)

class FollowingHandler(BaseHandler):
    """Handler for job following commands and functionality."""
    
    def __init__(self, db, active_commands=None, followed_keywords=None, analytics=None, rollbar=None):
        """
        Initialize the job following handler.
        
        Args:
            db: The database instance to use
            active_commands: Dictionary to track active commands for each user
            followed_keywords: Dictionary to cache user followed keywords
            analytics: The analytics service for tracking events
        """
        super().__init__(db)
        self.active_commands = active_commands or {}
        self.followed_keywords = followed_keywords or {}
        self.analytics = analytics
        self.rollbar = rollbar
        
    def load_followed_keywords(self):
        """Load all followed keywords from the database."""
        return self.db.load_subscriptions()  # Database method name will be updated separately
        
    async def follow(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """
        Handle the /follow command to follow job keywords.
        
        Args:
            update: The update object from Telegram
            context: The context object from Telegram
        """
        # Cancel any previous commands
        try:
            await self.cancel_previous_commands(update, context, "follow")
            
            user_id = str(update.effective_user.id)
            
            # Track command usage
            if self.analytics:
                self.analytics.track_command(user_id, "follow")
                
            await update.message.reply_text(
                "Silakan ketik kata kunci untuk lowongan yang ingin Anda ikuti dan dapatkan notifikasinya.\n"
                "Contoh: 'Javascript', 'Marketing', 'Remote'"
            )
            context.user_data['awaiting_keywords'] = True
        except Exception as e:
            logger.error(f"Error in follow handler: {e}")
            if self.rollbar:
                self.rollbar.capture_exception(
                    exc_info=(type(e), e, e.__traceback__),
                    request=self.rollbar.telegram_update_to_request(update) if hasattr(self.rollbar, 'telegram_update_to_request') else None,
                    extra_data={'handler': 'follow'}
                )
            await update.message.reply_text("Terjadi kesalahan saat memproses permintaan Anda. Silakan coba lagi nanti.")

    async def handle_following(self, update: Update, context: ContextTypes.DEFAULT_TYPE, user_states=None):
        """
        Handle keyword input from user who wants to follow job postings.
        
        Args:
            update: The update object from Telegram
            context: The context object from Telegram
            user_states: Dictionary to track conversation states for users
        """
        user_id = str(update.effective_user.id)
        
        # Check if user is in a conversation flow
        if user_states and user_id in user_states:
            return False  # Let the job posting handler handle this
            
        if context.user_data.get('awaiting_keywords'):
            keywords = update.message.text
            try:
                # Save to database
                success = self.db.save_subscription(user_id, keywords)
                
                if success:
                    # Update local cache
                    if user_id not in self.followed_keywords:
                        self.followed_keywords[user_id] = []
                        
                    self.followed_keywords[user_id].append(keywords)
                    
                    # Track successful follow
                    if self.analytics and hasattr(update, 'effective_user'):
                        username = update.effective_user.username
                        self.analytics.track_keyword_followed(user_id, keywords, username=username)
                        
                    await update.message.reply_text(
                        f"Anda akan menerima notifikasi untuk lowongan dengan kata kunci: \"{keywords}\"\n\n"
                        f"⚠️ PENTING: Pastikan Anda telah mengaktifkan notifikasi untuk bot ini di pengaturan Telegram, "
                        f"karena semua pemberitahuan lowongan baru akan dikirim melalui bot ini."
                    )
                else:
                    await update.message.reply_text(
                        "Maaf, ada masalah saat menyimpan kata kunci Anda. Silakan coba lagi nanti."
                    )
            except Exception as e:
                logger.error(f"Error in handle_following: {e}")
                if self.rollbar:
                    self.rollbar.capture_exception(
                        exc_info=(type(e), e, e.__traceback__),
                        request=self.rollbar.telegram_update_to_request(update) if hasattr(self.rollbar, 'telegram_update_to_request') else None,
                        extra_data={'handler': 'handle_following', 'user_id': user_id, 'keywords': keywords}
                    )
                await update.message.reply_text("Terjadi kesalahan saat memproses permintaan Anda. Silakan coba lagi nanti.")
            context.user_data['awaiting_keywords'] = False
            return True
            
        return False

    async def unfollow(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """
        Handle the /unfollow command to stop following job keywords.
        
        Args:
            update: The update object from Telegram
            context: The context object from Telegram
        """
        # Cancel any previous commands
        try:
            await self.cancel_previous_commands(update, context, "unfollow")
            
            # Track command usage
            user_id = str(update.effective_user.id)
            username = update.effective_user.username
            if self.analytics:
                self.analytics.track_command(user_id, "unfollow", username=username)
                
            user_id = str(update.effective_user.id)
            
            # Get followed keywords from database
            user_keywords = self.db.get_user_subscriptions(user_id)
            
            # Track followed keywords view event
            if self.analytics and hasattr(update, 'effective_user'):
                username = update.effective_user.username
                self.analytics.track_followed_keywords_viewed(user_id, len(user_keywords) if user_keywords else 0, username=username)
                
            if user_keywords:
                keyboard = []
                for keywords in user_keywords:
                    keyboard.append([InlineKeyboardButton(keywords, callback_data=f"unfollow_{keywords}")])
                    
                reply_markup = InlineKeyboardMarkup(keyboard)
                await update.message.reply_text(
                    "Pilih kata kunci yang ingin Anda berhenti ikuti:",
                    reply_markup=reply_markup
                )
            else:
                await update.message.reply_text(
                    "Anda belum mengikuti notifikasi untuk kata kunci apapun.\n"
                    "Gunakan perintah /follow untuk mulai mengikuti notifikasi lowongan."
                )
        except Exception as e:
            logger.error(f"Error in unfollow handler: {e}")
            if self.rollbar:
                self.rollbar.capture_exception(
                    exc_info=(type(e), e, e.__traceback__),
                    request=self.rollbar.telegram_update_to_request(update) if hasattr(self.rollbar, 'telegram_update_to_request') else None,
                    extra_data={'handler': 'unfollow', 'user_id': user_id}
                )
            await update.message.reply_text("Terjadi kesalahan saat memproses permintaan Anda. Silakan coba lagi nanti.")

    async def following(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """
        Display a user's currently followed job keywords.
        
        Args:
            update: The update object from Telegram
            context: The context object from Telegram
        """
        # Cancel any previous commands
        try:
            await self.cancel_previous_commands(update, context, "following")
            
            # Track command usage
            user_id = str(update.effective_user.id)
            username = update.effective_user.username
            if self.analytics:
                self.analytics.track_command(user_id, "following", username=username)
                
            # Get followed keywords from database
            user_keywords = self.db.get_user_subscriptions(user_id)
            
            # Track followed keywords view event
            if self.analytics and hasattr(update, 'effective_user'):
                username = update.effective_user.username
                self.analytics.track_followed_keywords_viewed(user_id, len(user_keywords) if user_keywords else 0, username=username)
                
            if user_keywords:
                keywords_list = ", ".join([f'"{keyword}"' for keyword in user_keywords])
                await update.message.reply_text(
                    f"Anda saat ini mengikuti notifikasi untuk kata kunci berikut:\n\n"
                    f"{keywords_list}\n\n"
                    f"Gunakan /unfollow untuk berhenti mengikuti kata kunci."
                )
            else:
                await update.message.reply_text(
                    "Anda belum mengikuti notifikasi untuk kata kunci apapun.\n"
                    "Gunakan perintah /follow untuk mulai mengikuti notifikasi lowongan."
                )
        except Exception as e:
            logger.error(f"Error in following handler: {e}")
            if self.rollbar:
                self.rollbar.capture_exception(
                    exc_info=(type(e), e, e.__traceback__),
                    request=self.rollbar.telegram_update_to_request(update) if hasattr(self.rollbar, 'telegram_update_to_request') else None,
                    extra_data={'handler': 'following', 'user_id': user_id}
                )
            await update.message.reply_text("Terjadi kesalahan saat memproses permintaan Anda. Silakan coba lagi nanti.")

    async def handle_unfollow_callback(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """
        Handle callback queries from unfollow inline buttons.
        
        Args:
            update: The update object from Telegram
            context: The context object from Telegram
            
        Returns:
            bool: True if callback was handled, False otherwise
        """
        query = update.callback_query
        user_id = str(query.from_user.id)
        
        if not query.data.startswith("unfollow_"):
            return False
            
        await query.answer()
        keywords = query.data[9:]  # Remove 'unfollow_' prefix
        
        # Delete from database
        success = self.db.delete_subscription(user_id, keywords)
        
        if success:
            # Update local cache
            if user_id in self.followed_keywords and keywords in self.followed_keywords[user_id]:
                self.followed_keywords[user_id].remove(keywords)
                
            # Track unfollow event
            if self.analytics:
                username = query.from_user.username if hasattr(query, 'from_user') else None
                self.analytics.track_keyword_unfollowed(user_id, keywords, username=username)
            
            # Check if user still has other followed keywords
            remaining_subscriptions = self.db.get_user_subscriptions(user_id)
            if remaining_subscriptions:
                subscription_list = ", ".join([f'"{sub}"' for sub in remaining_subscriptions])
                await query.edit_message_text(
                    f"Anda tidak akan lagi menerima notifikasi untuk: \"{keywords}\"\n\n"
                    f"Anda masih mengikuti notifikasi untuk kata kunci: {subscription_list}"
                )
            else:
                await query.edit_message_text(
                    f"Anda tidak akan lagi menerima notifikasi untuk: \"{keywords}\"\n\n"
                    f"Anda tidak memiliki kata kunci yang diikuti. Gunakan /follow untuk mulai mengikuti kata kunci."
                )
        else:
            await query.edit_message_text("Maaf, ada masalah saat menghapus kata kunci Anda. Silakan coba lagi nanti.")
            
        return True
