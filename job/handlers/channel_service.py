"""
Channel service for Gigsta Job Bot functionality.
"""
import logging
from telegram.ext import ContextTypes
from .base_handler import BaseHandler
import sys
import os

# Add parent directory to path to allow imports from parent directory
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
from config import CHANNEL_ID

# Configure logging
logger = logging.getLogger(__name__)

class ChannelService(BaseHandler):
    """Service for handling channel-related operations."""
    
    def __init__(self, db):
        """
        Initialize the channel service.
        
        Args:
            db: The database instance to use
        """
        super().__init__(db)
        
    def get_channel_link(self):
        """
        Get a formatted channel link based on CHANNEL_ID.
        
        Returns:
            str: Formatted channel link
        """
        try:
            if CHANNEL_ID.startswith('@'):
                return f"{CHANNEL_ID}"
            else:
                # For numeric IDs, we can create a t.me link
                return f"https://t.me/c/{CHANNEL_ID.replace('-100', '')}"
        except:
            # Fallback if there's any issue with the channel ID
            return "Gigsta Jobs"
