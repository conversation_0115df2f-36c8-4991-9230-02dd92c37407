"""
Base handler for Gigsta Job Bot functionality.
"""
import logging
from telegram import Update
from telegram.ext import ContextTypes

# Configure logging
logger = logging.getLogger(__name__)

class BaseHandler:
    """Base class for all handlers to share common functionality."""
    
    def __init__(self, db, scraper=None):
        """
        Initialize the base handler.
        
        Args:
            db: The database instance to use
            scraper: Optional job scraper instance
        """
        self.db = db
        self.scraper = scraper
        
    async def cancel_previous_commands(self, update: Update, context: ContextTypes.DEFAULT_TYPE, new_command: str):
        """
        Cancel any previous commands and their steps when a new command is called.
        
        Args:
            update: The update object from Telegram
            context: The context object from Telegram
            new_command: The new command being executed
        """
        user_id = str(update.effective_user.id)
        
        # Clear any active state for the user in the context
        for key in list(context.user_data.keys()):
            if key.startswith('awaiting_'):
                del context.user_data[key]
        
        # Add this command to active commands for the user
        if hasattr(self, 'active_commands'):
            self.active_commands[user_id] = new_command
            
        # Log command execution if debug is enabled
        logger.debug(f"User {user_id} executing command: {new_command}")
