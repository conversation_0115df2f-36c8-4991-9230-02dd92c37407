"""
Analytics service for tracking user interactions and bot events using Mixpanel.
"""
import logging
from mixpanel import Mixpanel
import os
from datetime import datetime

logger = logging.getLogger(__name__)

class AnalyticsService:
    """Service for tracking user interactions and bot events."""
    
    def __init__(self, token=None, version=None):
        """
        Initialize the analytics service.
        
        Args:
            token: The Mixpanel project token. If None, will try to get from environment.
            version: The bot version number to include in all events.
        """
        self.token = token or os.getenv("MIXPANEL_TOKEN")
        self.enabled = bool(self.token)
        self.version = version
        
        if self.enabled:
            self.mp = Mixpanel(self.token)
            logger.info("Analytics service initialized with Mixpanel")
        else:
            logger.warning("Analytics service initialized but disabled (no token provided)")
    
    def track(self, user_id, event_name, username=None, properties=None):
        """
        Track an event for a user.
        
        Args:
            user_id: The Telegram user ID
            event_name: Name of the event to track
            username: Optional Telegram username
            properties: Optional dictionary of event properties
        """
        if not self.enabled:
            return
            
        try:
            # Ensure user_id is a string
            user_id = str(user_id)
            
            # Prepare properties with defaults
            props = {
                'timestamp': datetime.now().isoformat(),
                'platform': 'telegram'
            }
            
            # Add version information if available
            if self.version:
                props['app_version'] = self.version
            
            # Add username if available
            if username:
                props['username'] = username
            
            # Add custom properties if provided
            if properties:
                props.update(properties)
                
            # Track the event
            self.mp.track(user_id, event_name, props)
            
        except Exception as e:
            logger.error(f"Error tracking analytics event {event_name}: {e}")
    
    # User events
    def track_user_start(self, user_id, username=None):
        """Track when a user starts the bot."""
        self.track(user_id, 'bot_start', username=username)
    
    def track_command(self, user_id, command, username=None):
        """Track when a user uses a command."""
        self.track(user_id, 'command_used', username=username, properties={'command': command})
    
    # Job following events
    def track_keyword_followed(self, user_id, keyword, username=None):
        """Track when a user follows a job keyword."""
        self.track(user_id, 'keyword_followed', username=username, properties={'keyword': keyword})
    
    def track_keyword_unfollowed(self, user_id, keyword, username=None):
        """Track when a user unfollows a job keyword."""
        self.track(user_id, 'keyword_unfollowed', username=username, properties={'keyword': keyword})
    
    def track_followed_keywords_viewed(self, user_id, count, username=None):
        """Track when a user views their followed keywords."""
        self.track(user_id, 'followed_keywords_viewed', username=username, properties={'count': count})
    
    # Job posting events
    def track_job_post_started(self, user_id, username=None):
        """Track when a user starts the job posting flow."""
        self.track(user_id, 'job_post_started', username=username)
    
    def track_job_post_completed(self, user_id, has_image, username=None):
        """Track when a user completes a job posting."""
        self.track(user_id, 'job_post_completed', username=username, properties={'has_image': has_image})
    
    def track_job_post_cancelled(self, user_id, username=None):
        """Track when a user cancels a job posting."""
        self.track(user_id, 'job_post_cancelled', username=username)
    
    def track_job_posted_to_channel(self, user_id, job_id, username=None):
        """Track when a job is posted to the channel."""
        self.track(user_id, 'job_posted_to_channel', username=username, properties={'job_id': job_id})
    
    def track_job_deleted(self, user_id, job_id, username=None):
        """Track when a user deletes a job."""
        self.track(user_id, 'job_deleted', username=username, properties={'job_id': job_id})
    
    def track_jobs_viewed(self, user_id, count, username=None):
        """Track when a user views their jobs."""
        self.track(user_id, 'jobs_viewed', username=username, properties={'count': count})
    
    # Notification events
    def track_notification_sent(self, user_id, job_id, keyword, username=None):
        """Track when a notification is sent to a user."""
        self.track(user_id, 'notification_sent', username=username, properties={
            'job_id': job_id,
            'keyword': keyword
        })
    
    def track_daily_update(self, job_count, user_count):
        """Track when daily updates are sent."""
        # Using a fixed ID for system events
        self.track('system', 'daily_update', {
            'job_count': job_count,
            'user_count': user_count
        })
