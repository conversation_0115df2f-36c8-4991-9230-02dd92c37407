"""
Job posting handler for Gigsta Job Bot functionality.
"""
import logging
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import ContextTypes
from .base_handler import BaseHandler
from ..ai_contact_extractor import extract_contact_info
from ..contact_validator import extract_contact_info as rule_based_extract
import sys
import os

# Add parent directory to path to allow imports from parent directory
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
from config import is_feature_enabled, CHANNEL_ID

# Configure logging
logger = logging.getLogger(__name__)

class JobPostingHandler(BaseHandler):
    """Handler for job posting related commands and functionality."""
    
    def __init__(self, db, active_commands=None, user_states=None, analytics=None, rollbar=None):
        """
        Initialize the job posting handler.
        
        Args:
            db: The database instance to use
            active_commands: Dictionary to track active commands for each user
            user_states: Dictionary to track conversation states for users
            analytics: The analytics service for tracking events
        """
        super().__init__(db)
        self.active_commands = active_commands or {}
        self.user_states = user_states or {}
        self.analytics = analytics
        self.rollbar = rollbar
    
    def escape_markdown(self, text):
        """
        Escape special Markdown characters in text.
        
        Args:
            text: The text to escape
            
        Returns:
            str: Text with markdown characters escaped
        """
        if not text:
            return ""
        # Escape special Markdown characters
        return text.replace('*', '\\*').replace('_', '\\_').replace('`', '\\`').replace('[', '\\[')
    
    async def send_message_with_image(self, chat_id, text, image_file_id, context, reply_to=None, parse_mode='Markdown', reply_markup=None):
        """
        Send a message with an image, handling caption limit constraints.
        
        Args:
            chat_id: The chat ID to send the message to
            text: The text content to send
            image_file_id: The file ID of the image to send
            context: The Telegram context object
            reply_to: Optional message ID to reply to
            parse_mode: The parse mode to use (default: Markdown)
            
        Returns:
            tuple: (message_id, additional_message_id)
        """
        caption_limit = 1024
        message_id = None
        additional_message_id = None
        
        try:
            # If text fits within caption limit, send as single photo with caption
            if len(text) <= caption_limit:
                # Create kwargs dict with optional parameters
                kwargs = {
                    'chat_id': chat_id,
                    'photo': image_file_id,
                    'caption': text,
                    'parse_mode': parse_mode
                }
                
                # Add reply_to_message_id if provided
                if reply_to:
                    kwargs['reply_to_message_id'] = reply_to
                    
                # Add reply_markup if provided
                if reply_markup:
                    kwargs['reply_markup'] = reply_markup
                    
                photo_message = await context.bot.send_photo(**kwargs)
                message_id = photo_message.message_id
            else:
                # If caption is too long, send image and text separately
                # First send the image without caption
                photo_kwargs = {
                    'chat_id': chat_id,
                    'photo': image_file_id
                }
                
                if reply_to:
                    photo_kwargs['reply_to_message_id'] = reply_to
                    
                photo_message = await context.bot.send_photo(**photo_kwargs)
                message_id = photo_message.message_id
                
                # Then send text as a separate message, replying to the photo
                text_message = await context.bot.send_message(
                    chat_id=chat_id,
                    text=text,
                    reply_to_message_id=message_id,
                    parse_mode=parse_mode,
                    reply_markup=reply_markup
                )
                additional_message_id = text_message.message_id
        except Exception as e:
            logger.error(f"Error sending message with image: {e}")
            # Fall back to sending text-only message
            text_message = await context.bot.send_message(
                chat_id=chat_id,
                text=text,
                parse_mode=parse_mode,
                reply_markup=reply_markup
            )
            additional_message_id = text_message.message_id
            
        return message_id, additional_message_id
        
    def get_channel_link(self):
        """
        Get a formatted channel link based on CHANNEL_ID.
        
        Returns:
            str: Formatted channel link
        """
        try:
            if CHANNEL_ID.startswith('@'):
                return f"{CHANNEL_ID}"
            else:
                # For numeric IDs, we can create a t.me link
                return f"https://t.me/c/{CHANNEL_ID.replace('-100', '')}"
        except:
            # Fallback if there's any issue with the channel ID
            return "Gigsta Jobs"
            
    def create_message_link(self, message_id):
        """
        Create a direct link to a message in the channel.
        
        Args:
            message_id: The message ID to link to
            
        Returns:
            str: Formatted message link
        """
        if not message_id:
            return self.get_channel_link()
            
        try:
            if str(CHANNEL_ID).startswith('@'):
                # For username-based channels
                channel_username = str(CHANNEL_ID)[1:]  # Remove the @ symbol
                return f"https://t.me/{channel_username}/{message_id}"
            else:
                # For channels with numeric IDs
                return f"https://t.me/c/{str(CHANNEL_ID).replace('-100', '')}/{message_id}"
        except Exception as e:
            logger.error(f"Error creating message link: {e}")
            return self.get_channel_link()
            
    def format_date_indonesian(self, date_str):
        """
        Format a date string to Indonesian style.
        
        Args:
            date_str: Date string in format '%Y-%m-%d %H:%M:%S'
            
        Returns:
            str: Formatted date in Indonesian style
        """
        try:
            from datetime import datetime
            # Parse the timestamp from database
            dt = datetime.strptime(date_str, '%Y-%m-%d %H:%M:%S')
            # Format in Indonesian style: day month year, hour:minute
            months_id = ["Januari", "Februari", "Maret", "April", "Mei", "Juni", 
                        "Juli", "Agustus", "September", "Oktober", "November", "Desember"]
            return f"{dt.day} {months_id[dt.month-1]} {dt.year}, {dt.hour:02d}:{dt.minute:02d}"
        except Exception as e:
            logger.error(f"Error formatting date: {e}")
            return date_str
            
    async def delete_messages(self, chat_id, message_ids, context, exclude_id=None):
        """
        Delete multiple messages by their IDs.
        
        Args:
            chat_id: The chat ID where the messages are
            message_ids: List of message IDs to delete
            context: The Telegram context object
            exclude_id: Optional message ID to exclude from deletion
            
        Returns:
            list: IDs of messages that were successfully deleted
        """
        deleted_ids = []
        
        for msg_id in message_ids:
            try:
                # Skip the excluded message ID if provided
                if exclude_id and msg_id == exclude_id:
                    continue
                    
                await context.bot.delete_message(chat_id=chat_id, message_id=msg_id)
                deleted_ids.append(msg_id)
            except Exception as e:
                logger.error(f"Error deleting message {msg_id}: {e}")
                
        return deleted_ids
        
    def create_confirmation_keyboard(self, confirm_text="✅ Konfirmasi", cancel_text="❌ Batal", 
                                   confirm_data="confirm", cancel_data="cancel"):
        """
        Create a standard confirmation keyboard with confirm/cancel buttons.
        
        Args:
            confirm_text: Text for the confirmation button
            cancel_text: Text for the cancel button
            confirm_data: Callback data for confirmation
            cancel_data: Callback data for cancellation
            
        Returns:
            InlineKeyboardMarkup: The configured keyboard markup
        """
        keyboard = [
            [InlineKeyboardButton(confirm_text, callback_data=confirm_data)],
            [InlineKeyboardButton(cancel_text, callback_data=cancel_data)]
        ]
        return InlineKeyboardMarkup(keyboard)
        
    def create_delete_confirmation_keyboard(self, job_id, image_id=None, info_id=None):
        """
        Create a keyboard for confirming job deletion.
        
        Args:
            job_id: The ID of the job to delete
            image_id: Optional ID of associated image message
            info_id: Optional ID of associated info message
            
        Returns:
            InlineKeyboardMarkup: The configured keyboard markup
        """
        # Format the callback data with message IDs for cleanup
        image_id_str = f"_{image_id or 0}"
        info_id_str = f"_{info_id or 0}"
        
        keyboard = [
            [InlineKeyboardButton("✅ Ya, Hapus", 
                                callback_data=f"delete_job_{job_id}{image_id_str}{info_id_str}")],
            [InlineKeyboardButton("❌ Tidak, Batalkan", 
                                callback_data=f"cancel_delete_job_{job_id}{image_id_str}{info_id_str}")]
        ]
        return InlineKeyboardMarkup(keyboard)
        
    def format_job_info(self, job_data):
        """
        Format job information for display.
        
        Args:
            job_data: Dictionary with job data containing at minimum 'is_email', 'contact', and 'poster_file_id'
            
        Returns:
            str: Formatted job information
        """
        # Determine contact type
        contact_type = "Email" if job_data.get("is_email") else "Website"
        
        # Format image info
        image_info = "✓ Gambar poster diunggah" if job_data.get("poster_file_id") else "✗ Tanpa gambar poster"
        
        # Create info message
        info_text = f"📝 Deskripsi lowongan telah diterima\n"
        info_text += f"📷 {image_info}\n"
        info_text += f"📧 {contact_type} kontak: {job_data.get('contact', 'Tidak tersedia')}"
        
        return info_text
        
    async def generate_job_preview(self, chat_id, content, image_file_id, context, state=None):
        """
        Generate a preview of a job posting with optional image.
        
        Args:
            chat_id: The chat ID to send the preview to
            content: The job content to preview
            image_file_id: Optional file ID of an image to include
            context: The Telegram context object
            state: Optional user state dictionary to store message IDs
            
        Returns:
            list: List of message IDs that were sent as part of the preview
        """
        preview_message_ids = []
        
        # Escape markdown in content
        preview_content = self.escape_markdown(content)
        
        # Send preview header
        preview_header_msg = await context.bot.send_message(
            chat_id=chat_id,
            text=f"📄 Preview Postingan:",
            parse_mode='Markdown'
        )
        preview_message_ids.append(preview_header_msg.message_id)
        
        # Send content with image if provided
        if image_file_id:
            # Use our helper method for sending messages with images
            message_id, additional_id = await self.send_message_with_image(
                chat_id=chat_id,
                text=preview_content,
                image_file_id=image_file_id,
                context=context
            )
            
            # Add message IDs to the list
            if message_id:
                preview_message_ids.append(message_id)
            if additional_id:
                preview_message_ids.append(additional_id)
        else:
            # No image, send a text message
            text_msg = await context.bot.send_message(
                chat_id=chat_id,
                text=preview_content,
                parse_mode='Markdown'
            )
            preview_message_ids.append(text_msg.message_id)
        
        # If state is provided, store the message IDs
        if state is not None and isinstance(state, dict):
            if "preview_message_ids" not in state:
                state["preview_message_ids"] = []
            state["preview_message_ids"].extend(preview_message_ids)
        
        return preview_message_ids
    
    async def post_job(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """
        Start the job posting flow with simplified free text input.
        
        Args:
            update: The update object from Telegram
            context: The context object from Telegram
        """
        try:
            # Cancel any previous commands
            await self.cancel_previous_commands(update, context, "post_job")
            
            user_id = str(update.effective_user.id)
            
            # Track job post started
            if self.analytics:
                username = update.effective_user.username
                self.analytics.track_job_post_started(user_id, username=username)
                
            # Initialize user state for job posting flow
            self.user_states[user_id] = {
                "current_step": "input_stage",
                "content": None,
                "poster_file_id": None,  # Store file_id for uploaded images
                "contact": None,
                "is_email": None
            }
            
            await update.message.reply_text(
                "Mari posting lowongan baru! 📝\n\n"
                "Silakan ketik deskripsi lengkap lowongan Anda. Anda dapat menyertakan:\n"
                "• Posisi yang dibuka (nama atau judul pekerjaan)\n"
                "• Deskripsi tugas dan tanggung jawab\n"
                "• Persyaratan kandidat\n"
                "• Kontak untuk melamar (email atau website)\n\n"
                "Anda juga dapat menambahkan satu gambar (jika ada)."
            )
        except Exception as e:
            logger.error(f"Error in post_job handler: {e}")
            if self.rollbar:
                self.rollbar.capture_exception(
                    exc_info=(type(e), e, e.__traceback__),
                    request=self.rollbar.telegram_update_to_request(update) if hasattr(self.rollbar, 'telegram_update_to_request') else None,
                    extra_data={'handler': 'post_job'}
                )
            await update.message.reply_text("Terjadi kesalahan saat memulai proses posting. Silakan coba lagi nanti.")
    
    async def handle_job_posting_flow(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """
        Handle the job posting conversation flow.
        
        Args:
            update: The update object from Telegram
            context: The context object from Telegram
            
        Returns:
            bool: True if the message was handled, False otherwise
        """
        try:
            user_id = str(update.effective_user.id)
            
            # Check if we're in a subscription flow first - don't intercept those messages
            if context.user_data.get('awaiting_keywords'):
                return False
            
            # Get current user state
            if user_id not in self.user_states:
                # Don't respond with error message - just indicate we didn't handle this message
                # Let other handlers try to process it
                return False
                
            state = self.user_states[user_id]
            current_step = state["current_step"]
            
            # If we're in confirmation stage, ignore other content
            if current_step == "confirmation_stage":
                return True  # Handled this message
                
            # We only process inputs during the input stage
            if current_step != "input_stage":
                return False
                
            # Check if user has already uploaded an image
            if "poster_file_id" in state and state["poster_file_id"] is not None:
                # Send warning and track the message ID
                warning_msg = await update.message.reply_text(
                    "⚠️ Anda sudah mengunggah gambar sebelumnya. Tidak bisa mengunggah lebih dari satu gambar.\n"
                    "Silakan kirim deskripsi lowongan atau selesaikan proses posting."
                )
                return True
                
            # Handle photo uploads with captions
            if hasattr(update, 'message') and update.message and update.message.photo:
                # Check if the photo has a caption
                if update.message.caption:
                    # Get the largest photo (best quality)
                    photo = update.message.photo[-1]
                    state["poster_file_id"] = photo.file_id
                    
                    # Use the caption as content
                    content = update.message.caption
                    state["content"] = content
                    
                    # Extract contact info from caption
                    await self._process_content_and_move_to_confirmation(update, context, content)
                else:
                    # They sent an image without caption
                    await update.message.reply_text(
                        "⚠️ Harap sertakan caption yang berisi deskripsi lowongan.\n"
                        "Silakan kirim ulang gambar beserta caption deskripsi."
                    )
                return True
                
            # Check if they're uploading an image as a file/document instead of as a photo
            if hasattr(update, 'message') and update.message and update.message.document:
                # Check if it's an image file
                mime_type = update.message.document.mime_type
                if mime_type and mime_type.startswith('image/'):
                        await update.message.reply_text(
                        "⚠️ Mohon kirim gambar sebagai foto, bukan sebagai file."
                        )
                else:
                    await update.message.reply_text(
                        "⚠️ Format posting lowongan hanya menerima teks atau foto dengan caption.\n\n"
                        "Mohon kirim deskripsi lowongan sebagai teks biasa atau sebagai caption pada foto jika ingin menyertakan gambar."
                    )
                return True
                
            # Handle text messages
            if hasattr(update, 'message') and update.message and update.message.text:
                content = update.message.text
                
                # Save job content
                state["content"] = content
                
                # Process content and move to confirmation
                await self._process_content_and_move_to_confirmation(update, context, content)
                return True
                
            return False
        except Exception as e:
            logger.error(f"Error in handle_job_posting_flow: {e}")
            if self.rollbar:
                self.rollbar.capture_exception(
                    exc_info=(type(e), e, e.__traceback__),
                    request=self.rollbar.telegram_update_to_request(update) if hasattr(self.rollbar, 'telegram_update_to_request') else None,
                    extra_data={'handler': 'handle_job_posting_flow'}
                )
            # Send user-friendly error message
            await update.message.reply_text("Terjadi kesalahan saat memproses pesan Anda. Silakan coba lagi nanti.")
            return True
    
    async def _process_content_and_move_to_confirmation(self, update, context, content):
        """
        Process job content and move to confirmation stage if valid.
        
        Args:
            update: The update object from Telegram
            context: The context object from Telegram
            content: The job posting content to process
        """
        try:
            user_id = str(update.effective_user.id)
            state = self.user_states[user_id]
            
            # Extract contact information from the text (email or URL) using AI with fallback to rule-based
            try:
                valid_emails, valid_urls = extract_contact_info(content)
                
                # Log which extraction method was used (for debugging)
                if is_feature_enabled("debug_logging"):
                    logger.debug(f"Contact extraction found {len(valid_emails)} emails and {len(valid_urls)} URLs")
            except Exception as e:
                # Log the error and fall back to rule-based extraction
                logger.error(f"Error in AI contact extraction: {e}")
                if self.rollbar:
                    self.rollbar.capture_exception(
                        exc_info=(type(e), e, e.__traceback__),
                        request=self.rollbar.telegram_update_to_request(update) if hasattr(self.rollbar, 'telegram_update_to_request') else None,
                        extra_data={'handler': '_process_content_and_move_to_confirmation', 'subfunction': 'ai_contact_extraction'}
                    )
                # Fall back to rule-based extraction
                valid_emails, valid_urls = rule_based_extract(content)
            
            # Check if we have valid contact information
            if not valid_emails and not valid_urls:
                # No valid contact information found
                await update.message.reply_text(
                    "⚠️ PERHATIAN: Tidak ditemukan informasi kontak (email atau website) dalam deskripsi Anda.\n\n"
                    "Informasi kontak diperlukan agar pelamar dapat menghubungi Anda. "
                    "Silakan kirim deskripsi baru yang menyertakan alamat email atau website untuk melamar."
                )
                # Keep the user in the input stage to allow them to try again
                state["current_step"] = "input_stage"
                return
                
            # Prioritize email over URL
            if valid_emails:
                # If we have a valid email, use it regardless of URLs
                state["contact"] = valid_emails[0]
                state["is_email"] = True
                await self._move_to_confirmation_stage(update, context)
            elif len(valid_urls) > 1:
                # If no email but multiple URLs, let the user choose which one to use
                # Save the content and URLs in state to use later
                state["pending_urls"] = valid_urls
                
                # Create buttons for each URL
                keyboard = []
                for i, url in enumerate(valid_urls):
                    # Trim URL for display if too long
                    display_url = url
                    if len(display_url) > 30:
                        display_url = display_url[:27] + "..."
                    keyboard.append([InlineKeyboardButton(f"{i+1}. {display_url}", callback_data=f"select_url_{i}")])
                
                reply_markup = InlineKeyboardMarkup(keyboard)
                
                await update.message.reply_text(
                    "Ditemukan beberapa URL dalam deskripsi Anda. Silakan pilih URL mana yang akan digunakan sebagai kontak untuk melamar pekerjaan:",
                    reply_markup=reply_markup
                )
            else:
                # No email but one URL
                state["contact"] = valid_urls[0]
                state["is_email"] = False
                await self._move_to_confirmation_stage(update, context)
        except Exception as e:
            logger.error(f"Error in _process_content_and_move_to_confirmation: {e}")
            if self.rollbar:
                self.rollbar.capture_exception(
                    exc_info=(type(e), e, e.__traceback__),
                    request=self.rollbar.telegram_update_to_request(update) if hasattr(self.rollbar, 'telegram_update_to_request') else None,
                    extra_data={'handler': '_process_content_and_move_to_confirmation'}
                )
            # Send user-friendly error message
            await update.message.reply_text("Terjadi kesalahan saat memproses konten lowongan Anda. Silakan coba lagi nanti.")
            # Reset user state to input stage
            if user_id in self.user_states:
                self.user_states[user_id]["current_step"] = "input_stage"
    
    async def _move_to_confirmation_stage(self, update, context):
        """
        Move to confirmation stage after valid input.
        
        Args:
            update: The update object from Telegram
            context: The context object from Telegram
        """
        try:
            user_id = str(update.effective_user.id)
            state = self.user_states[user_id]
            
            # Move to confirmation stage
            state["current_step"] = "confirmation_stage"
            
            # Create confirmation buttons using our helper method
            reply_markup = self.create_confirmation_keyboard(
                confirm_text="✅ Posting Sekarang",
                cancel_text="❌ Batal",
                confirm_data="post_job_confirm",
                cancel_data="post_job_cancel"
            )
            
            # Send the confirmation message with buttons
            await update.message.reply_text(
                f"Apakah Anda ingin memposting lowongan ini sekarang?",
                reply_markup=reply_markup,
                parse_mode='Markdown'
            )
        except Exception as e:
            logger.error(f"Error in _move_to_confirmation_stage: {e}")
            if self.rollbar:
                self.rollbar.capture_exception(
                    exc_info=(type(e), e, e.__traceback__),
                    request=self.rollbar.telegram_update_to_request(update) if hasattr(self.rollbar, 'telegram_update_to_request') else None,
                    extra_data={'handler': '_move_to_confirmation_stage'}
                )
            # Send user-friendly error message
            await update.message.reply_text("Terjadi kesalahan saat memproses konfirmasi. Silakan coba lagi nanti.")
            # Reset user state to input stage if possible
            if user_id in self.user_states:
                self.user_states[user_id]["current_step"] = "input_stage"
    
    async def finish_job_posting(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """
        Finalize the job posting process after confirmation.
        
        Args:
            update: The update object from Telegram
            context: The context object from Telegram
        """
        try:
            query = update.callback_query
            user_id = str(query.from_user.id)
            
            if user_id not in self.user_states:
                await query.edit_message_text("Sesi posting lowongan telah berakhir. Silakan gunakan /post untuk memulai lagi.")
                return
                
            state = self.user_states[user_id]
            
            # Get the full content
            content = state["content"]
            if not content:
                await query.edit_message_text("Tidak ada konten lowongan yang diberikan. Posting dibatalkan.")
                del self.user_states[user_id]
                return
            
            # Use first line or first 50 chars for database title (for reference only)
            if "\n" in content:
                title = content.split("\n")[0][:100]  # Limit title to 100 chars
            else:
                title = content[:50] + ("..." if len(content) > 50 else "")
            
            # Check if we have contact information
            if not state["contact"]:
                # Try to ask the user for contact
                await query.edit_message_text(
                    "Untuk menyelesaikan posting, diperlukan kontak untuk melamar (email atau website).\n"
                    "Silakan gunakan /post untuk memulai lagi dan sertakan kontak di deskripsi lowongan."
                )
                del self.user_states[user_id]
                return
            
            # Post the job
            job_id = self.db.post_job(
                user_id=user_id,
                title=title,
                description=content,  # Use full content as description
                poster_file_id=state["poster_file_id"],
                contact=state["contact"],
                is_email=state["is_email"]
            )
            
            # Track job posting completion
            if self.analytics:
                username = query.from_user.username if hasattr(query, 'from_user') else None
                self.analytics.track_job_post_completed(user_id, has_image=bool(state["poster_file_id"]), username=username)
            
            if job_id:
                # Post to channel
                message_id = await self.post_job_to_channel(context, job_id)
                
                # Track job posted to channel
                if self.analytics:
                    username = query.from_user.username if hasattr(query, 'from_user') else None
                    self.analytics.track_job_posted_to_channel(user_id, job_id, username=username)
                
                # Notify followers
                await self.notify_followers(context, job_id)
                
                # Create message link if message_id exists
                message_link = self.get_channel_link()
                if message_id:
                    # Check if CHANNEL_ID is a username (starts with @) or a numeric ID
                    if str(CHANNEL_ID).startswith('@'):
                        channel_username = str(CHANNEL_ID)[1:]  # Remove the @ symbol
                        job_post_url = f"https://t.me/{channel_username}/{message_id}"
                        message_link = job_post_url
                    else:
                        # For channels with numeric IDs
                        job_post_url = f"https://t.me/c/{str(CHANNEL_ID).replace('-100', '')}/{message_id}"
                        message_link = job_post_url
                
                await query.edit_message_text(
                    "🎉 Lowongan kerja Anda berhasil diposting!\n\n"
                    f"✅ Telah dibagikan ke: {message_link}\n"
                    "✅ Notifikasi telah dikirim ke pengguna yang berlangganan kata kunci terkait"
                )
            else:
                await query.edit_message_text(
                    "Maaf, ada masalah saat posting lowongan Anda. Silakan coba lagi nanti."
                )
            
            # Clear user state
            del self.user_states[user_id]
        except Exception as e:
            logger.error(f"Error in finish_job_posting: {e}")
            if self.rollbar:
                self.rollbar.capture_exception(
                    exc_info=(type(e), e, e.__traceback__),
                    request=self.rollbar.telegram_update_to_request(update) if hasattr(self.rollbar, 'telegram_update_to_request') else None,
                    extra_data={'handler': 'finish_job_posting'}
                )
            # Send user-friendly error message through callback query
            try:
                query = update.callback_query
                await query.edit_message_text("Terjadi kesalahan saat menyelesaikan posting lowongan. Silakan coba lagi nanti.")
                # If we have the user's state, clean it up
                if 'user_id' in locals() and user_id in self.user_states:
                    del self.user_states[user_id]
            except Exception as edit_error:
                # If we can't edit the message (e.g., because it's too old), log the error
                logger.error(f"Error editing message after finishing job posting: {edit_error}")
                # We don't want to send this to Rollbar as it's a secondary error
    
    async def cancel_job_posting(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """
        Cancel the job posting process.
        
        Args:
            update: The update object from Telegram
            context: The context object from Telegram
        """
        try:
            query = update.callback_query
            user_id = str(query.from_user.id)
            
            if user_id in self.user_states:
                # Clean up user state
                del self.user_states[user_id]
                
                # Track job post cancelled
                if self.analytics:
                    username = query.from_user.username if hasattr(query, 'from_user') else None
                    self.analytics.track_job_post_cancelled(user_id, username=username)
            
            await query.edit_message_text("Posting lowongan dibatalkan.")
        except Exception as e:
            logger.error(f"Error in cancel_job_posting: {e}")
            if self.rollbar:
                self.rollbar.capture_exception(
                    exc_info=(type(e), e, e.__traceback__),
                    request=self.rollbar.telegram_update_to_request(update) if hasattr(self.rollbar, 'telegram_update_to_request') else None,
                    extra_data={'handler': 'cancel_job_posting'}
                )
            # Try to send a user-friendly error message
            try:
                query = update.callback_query
                await query.edit_message_text("Terjadi kesalahan saat membatalkan posting. Proses telah dibatalkan.")
                # Ensure user state is cleaned up even if an error occurred
                if 'user_id' in locals() and user_id in self.user_states:
                    del self.user_states[user_id]
            except Exception as edit_error:
                logger.error(f"Error editing message after cancelling job posting: {edit_error}")
    
    async def handle_url_selection(self, update: Update, context: ContextTypes.DEFAULT_TYPE, url_index: int):
        """
        Handle URL selection from multiple URLs.
        
        Args:
            update: The update object from Telegram
            context: The context object from Telegram
            url_index: The index of the selected URL
        """
        try:
            query = update.callback_query
            user_id = str(query.from_user.id)
            
            # Update user state with the selected URL
            if user_id in self.user_states:
                state = self.user_states[user_id]
                if "pending_urls" in state:
                    selected_url = state["pending_urls"][url_index]
                    state["contact"] = selected_url
                    state["is_email"] = False
                    del state["pending_urls"]
                    
                    # Move to confirmation stage
                    state["current_step"] = "confirmation_stage"
                    
                    # Create confirmation buttons
                    reply_markup = self.create_confirmation_keyboard(
                        confirm_text="✅ Posting Sekarang",
                        cancel_text="❌ Batal",
                        confirm_data="post_job_confirm",
                        cancel_data="post_job_cancel"
                    )
                    
                    # Edit the message to show confirmation buttons
                    await query.edit_message_text(
                        f"Apakah Anda ingin memposting lowongan ini sekarang?",
                        reply_markup=reply_markup,
                        parse_mode='Markdown'
                    )
                else:
                    await query.answer("Silakan mulai lagi dengan /post")
            else:
                await query.answer("Silakan mulai lagi dengan /post")
        except Exception as e:
            logger.error(f"Error in handle_url_selection: {e}")
            if self.rollbar:
                self.rollbar.capture_exception(
                    exc_info=(type(e), e, e.__traceback__),
                    request=self.rollbar.telegram_update_to_request(update) if hasattr(self.rollbar, 'telegram_update_to_request') else None,
                    extra_data={'handler': 'handle_url_selection', 'url_index': url_index}
                )
            # Try to send a user-friendly error message
            try:
                query = update.callback_query
                await query.answer("Terjadi kesalahan saat memilih URL. Silakan mulai lagi dengan /post")
                # Clean up user state if we can access it
                if 'user_id' in locals() and user_id in self.user_states:
                    del self.user_states[user_id]
            except Exception as answer_error:
                logger.error(f"Error sending answer after URL selection error: {answer_error}")
    
    async def my_posts(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """
        Show the user's posted jobs.
        
        Args:
            update: The update object from Telegram
            context: The context object from Telegram
        """
        try:
            # Cancel any previous commands
            await self.cancel_previous_commands(update, context, "my_posts")
            
            user_id = str(update.effective_user.id)
            
            # Track command usage
            if self.analytics:
                username = update.effective_user.username
                self.analytics.track_command(user_id, "myposts", username=username)
            
            # Get user's posted jobs
            jobs = self.db.get_user_posted_jobs(user_id)
            
            # Track jobs viewed
            if self.analytics:
                username = update.effective_user.username
                self.analytics.track_jobs_viewed(user_id, len(jobs) if jobs else 0, username=username)
            
            if not jobs:
                await update.message.reply_text(
                    "Anda belum posting lowongan apapun. Gunakan /post untuk membuat lowongan pertama Anda!"
                )
                return
            
            # Create a list of jobs with delete buttons
            for job in jobs:
                keyboard = [[InlineKeyboardButton("🗑️ Hapus", callback_data=f"confirm_delete_job_{job['id']}")]]
                reply_markup = InlineKeyboardMarkup(keyboard)
                
                # Escape special Markdown characters using our helper method
                escaped_description = self.escape_markdown(job['description'])
                
                # Create message link if message_id exists using our helper method
                message_link = ""
                if job.get('message_id'):
                    job_post_url = self.create_message_link(job['message_id'])
                    message_link = f"\n🔗 Lihat postingan di: {job_post_url}"
                
                # Create the job info message (without date)
                job_info = message_link if message_link else ""
                
                # Check if job has an image (poster_file_id)
                if job.get('poster_file_id'):
                    try:
                        # First send the job info message (date and link)
                        info_message = await update.message.reply_text(
                            job_info,
                            parse_mode='Markdown'
                        )
                        info_message_id = info_message.message_id
                        
                        # Then send the image without caption
                        image_message = await context.bot.send_photo(
                            chat_id=update.effective_chat.id,
                            photo=job['poster_file_id']
                        )
                        image_message_id = image_message.message_id
                        
                        # Store both message IDs in the callback data for confirmation
                        confirm_delete_callback_data = f"confirm_delete_job_{job['id']}_{image_message_id}_{info_message_id}"
                        
                        keyboard = [[InlineKeyboardButton("🗑️ Hapus", callback_data=confirm_delete_callback_data)]]
                        reply_markup = InlineKeyboardMarkup(keyboard)
                        
                        # Finally send the job description text with delete button
                        await update.message.reply_text(
                            escaped_description,
                            reply_markup=reply_markup,
                            parse_mode='Markdown'
                        )
                    except Exception as e:
                        logger.error(f"Error sending job image: {e}")
                        if self.rollbar:
                            self.rollbar.capture_exception(
                                exc_info=(type(e), e, e.__traceback__),
                                request=self.rollbar.telegram_update_to_request(update) if hasattr(self.rollbar, 'telegram_update_to_request') else None,
                                extra_data={'handler': 'my_posts', 'job_id': job.get('id'), 'subfunction': 'send_image'}
                            )
                        # If image sending fails, fallback to text-only format
                        await update.message.reply_text(
                            f"{job_info}\n\n{escaped_description}",
                            reply_markup=reply_markup,
                            parse_mode='Markdown'
                        )
                else:
                    # No image, send as a single text message
                    await update.message.reply_text(
                        f"{job_info}\n\n{escaped_description}",
                        reply_markup=reply_markup,
                        parse_mode='Markdown'
                    )
        except Exception as e:
            logger.error(f"Error in my_posts: {e}")
            if self.rollbar:
                self.rollbar.capture_exception(
                    exc_info=(type(e), e, e.__traceback__),
                    request=self.rollbar.telegram_update_to_request(update) if hasattr(self.rollbar, 'telegram_update_to_request') else None,
                    extra_data={'handler': 'my_posts'}
                )
            # Send user-friendly error message
            await update.message.reply_text("Terjadi kesalahan saat memuat daftar lowongan Anda. Silakan coba lagi nanti.")
                
    async def post_job_to_channel(self, context: ContextTypes.DEFAULT_TYPE, job_id: int) -> int:
        """
        Post a job to the channel and update the job with the message ID.
        
        Args:
            context: The context object from Telegram
            job_id: The ID of the job to post
            
        Returns:
            int: The message ID of the posted job in the channel, or None if posting failed
        """
        # Get job data
        job = self.db.get_job_details(job_id)
        if not job:
            logger.error(f"Job with ID {job_id} not found")
            return None
        
        try:
            # Escape Markdown characters in the job description using our helper
            post_text = self.escape_markdown(job['description'])
            
            # Set up reply markup for clickable button if we have a URL
            reply_markup = None
            if not job['is_email'] and job.get('contact'):
                button_text = "🔗 Lamar Sekarang"
                url = job['contact'] if job['contact'].startswith(('http://', 'https://')) else f"https://{job['contact']}"
                keyboard = [[InlineKeyboardButton(button_text, url=url)]]
                reply_markup = InlineKeyboardMarkup(keyboard)
            
            # Use our helper method for handling image and text messages
            if job.get('poster_file_id'):
                # For jobs with images, use the send_message_with_image helper
                message_id, additional_message_id = await self.send_message_with_image(
                    chat_id=CHANNEL_ID,
                    text=post_text,
                    image_file_id=job['poster_file_id'],
                    context=context,
                    parse_mode='Markdown',
                    reply_markup=reply_markup
                )
                
                # Prioritize the image message ID if available
                channel_message_id = message_id if message_id else additional_message_id
            else:
                # No image, send a text message
                message = await context.bot.send_message(
                    chat_id=CHANNEL_ID,
                    text=post_text,
                    parse_mode='Markdown',
                    reply_markup=reply_markup
                )
                channel_message_id = message.message_id
            
            # Update the job with the message ID
            if channel_message_id:
                self.db.update_job_message_id(job_id, channel_message_id)
                logger.info(f"Posted job {job_id} to channel with message ID {channel_message_id}")
            
            return channel_message_id
        except Exception as e:
            logger.error(f"Error posting job to channel: {e}")
            if self.rollbar:
                self.rollbar.capture_exception(
                    exc_info=(type(e), e, e.__traceback__),
                    # We don't have an Update object here, so we'll provide job_id in extra_data
                    extra_data={
                        'handler': 'post_job_to_channel',
                        'job_id': job_id,
                        'has_image': bool(job.get('poster_file_id')),
                        'channel_id': CHANNEL_ID
                    }
                )
            return None
            
    async def notify_followers_for_job_data(self, context: ContextTypes.DEFAULT_TYPE, job_data: dict, subscriptions=None):
        """
        Generic function to notify followers about a job posting based on job data.
        Works for both database jobs and scraped jobs.
        
        Args:
            context: The context object from Telegram
            job_data: Dictionary containing the job information
            subscriptions: Optional dictionary of user subscriptions; if None, will be loaded from DB
        
        Returns:
            set: Set of user IDs that were notified
        """
        if not job_data or 'description' not in job_data:
            logger.error("Invalid job data provided for notification")
            return set()
        
        # Get all subscriptions if not provided
        all_subscriptions = subscriptions if subscriptions is not None else self.db.load_subscriptions()
        
        # For each user, check if the job matches their keywords
        notified_users = set()
        
        try:
            job_content = job_data['description'].lower()
            channel_link = self.get_channel_link()
            
            for user_id, keywords_list in all_subscriptions.items():
                for keyword in keywords_list:
                    # Check if the keyword is in the job content
                    if keyword.lower() in job_content:
                        # Only notify each user once, even if multiple keywords match
                        if user_id not in notified_users:
                            try:
                                # Prepare the notification message
                                message_link = None
                                reply_markup = None
                                if 'message_id' in job_data and job_data['message_id']:
                                    # If we have a message_id, create a direct link
                                    if channel_link.startswith('https://t.me/c/'):
                                        # For numeric channel IDs
                                        channel_id_part = channel_link.split('https://t.me/c/')[1]
                                        message_link = f"https://t.me/c/{channel_id_part}/{job_data['message_id']}"
                                    elif channel_link.startswith('@'):
                                        # For username-based channels
                                        channel_username = channel_link.replace('@', '')
                                        message_link = f"https://t.me/{channel_username}/{job_data['message_id']}"
                                    else:
                                        message_link = channel_link
                                
                                # Format notification based on available data
                                if message_link:
                                    notification = (
                                        f"🔔 Ada lowongan baru yang cocok dengan kata kunci Anda: \"{keyword}\"\n\n"
                                        f"Cek lowongannya di sini: {message_link}"
                                    )
                                else:
                                    # For scraped jobs that don't have message_id
                                    notification = (
                                        f"🔔 Ada lowongan baru yang cocok dengan kata kunci Anda: \"{keyword}\"\n\n"
                                        f"💼 {job_data.get('title', 'Tidak ada judul')}\n"
                                        f"🏢 {job_data.get('company', 'Tidak ada perusahaan')}\n"
                                        f"📍 {job_data.get('location', 'Tidak ada lokasi')}\n\n"
                                    )
                                    
                                    # Add application URL as a button if available
                                    if 'url' in job_data and job_data['url']:
                                        reply_markup = InlineKeyboardMarkup([
                                            [InlineKeyboardButton("Lamar Sekarang", url=job_data['url'])]
                                        ])
                                    
                                # Send the notification
                                await context.bot.send_message(
                                    chat_id=user_id,
                                    text=notification,
                                    disable_web_page_preview=True,
                                    reply_markup=reply_markup
                                )
                                
                                notified_users.add(user_id)
                                job_identifier = job_data.get('id', job_data.get('url', 'Unknown job'))
                                logger.info(f"Notified user {user_id} about job {job_identifier} with keyword {keyword}")
                            except Exception as e:
                                logger.error(f"Error sending notification to user {user_id}: {e}")
                                if self.rollbar:
                                    self.rollbar.capture_exception(
                                        exc_info=(type(e), e, e.__traceback__),
                                        extra_data={
                                            'handler': 'notify_followers',
                                            'subfunction': 'send_notification',
                                            'job_id': job_data.get('id', 'None'),
                                            'job_title': job_data.get('title', 'Unknown'),
                                            'user_id': user_id,
                                            'keyword': keyword
                                        }
                                    )
                        break  # No need to check other keywords for this user
            
            return notified_users
            
        except Exception as e:
            logger.error(f"Error in notify_followers: {e}")
            if self.rollbar:
                self.rollbar.capture_exception(
                    exc_info=(type(e), e, e.__traceback__),
                    extra_data={
                        'handler': 'notify_followers',
                        'job_id': job_data.get('id', 'None'),
                        'job_title': job_data.get('title', 'Unknown')
                    }
                )
            return set()
    
    async def notify_followers(self, context: ContextTypes.DEFAULT_TYPE, job_id: int):
        """
        Notify followers about a new job posting based on job ID.
        
        Args:
            context: The context object from Telegram
            job_id: The ID of the job to notify about
        """
        # Get job data
        job = self.db.get_job_details(job_id)
        if not job:
            logger.error(f"Job with ID {job_id} not found")
            return set()
        
        # Use the generic function
        notified_users = await self.notify_followers_for_job_data(context, job)
        
        logger.info(f"Notified {len(notified_users)} users about job {job_id}")
        return notified_users
            
    async def handle_job_callback(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """
        Handle callback queries for job posting flow.
        
        Args:
            update: The update object from Telegram
            context: The context object from Telegram
            
        Returns:
            bool: True if callback was handled, False otherwise
        """
        try:
            query = update.callback_query
            user_id = str(query.from_user.id)
            callback_data = query.data  # Store for error reporting
            
            # Handle contact type selection for job posting
            if query.data.startswith("contact_type_"):
                contact_type = query.data.replace("contact_type_", "")
                
                # Update user state
                if user_id in self.user_states:
                    self.user_states[user_id]["is_email"] = (contact_type == "email")
                    self.user_states[user_id]["current_step"] = "contact"
                    
                    contact_type_text = "email" if contact_type == "email" else "website atau URL"
                    await query.answer(f"Jenis kontak dipilih: {contact_type_text}")
                    
                    await query.edit_message_text(
                        f"Silakan masukkan {contact_type_text} untuk melamar pekerjaan ini:"
                    )
                else:
                    await query.answer("Silakan mulai lagi dengan /post")
                return True
            
            # Handle job posting confirmation
            elif query.data == "post_job_confirm":
                await query.answer()
                await self.finish_job_posting(update, context)
                return True
            elif query.data == "post_job_cancel":
                await query.answer()
                await self.cancel_job_posting(update, context)
                return True
            
            # Handle confirmation for job deletion
            elif query.data.startswith("confirm_delete_job_"):
                await query.answer()
                
                # Parse callback data which can include message IDs (image and info)
                callback_parts = query.data.replace("confirm_delete_job_", "").split("_")
                job_id = int(callback_parts[0])
                
                # Check if we have message IDs to include in the confirmation
                image_message_id = None
                info_message_id = None
                
                # Extract message IDs from callback data
                if len(callback_parts) > 1:
                    try:
                        image_message_id = int(callback_parts[1])
                    except (ValueError, IndexError):
                        logger.error(f"Invalid image message ID format in callback: {query.data}")
                        
                if len(callback_parts) > 2:
                    try:
                        info_message_id = int(callback_parts[2])
                    except (ValueError, IndexError):
                        logger.error(f"Invalid info message ID format in callback: {query.data}")
                
                # Create confirmation buttons using our helper method
                reply_markup = self.create_delete_confirmation_keyboard(
                    job_id=job_id,
                    image_id=image_message_id,
                    info_id=info_message_id
                )
                
                # Show confirmation message
                await query.edit_message_text(
                    "⚠️ Apakah Anda yakin ingin menghapus lowongan ini? \n\n"
                    "Tindakan ini tidak dapat dibatalkan dan akan menghapus lowongan dari channel.",
                    reply_markup=reply_markup
                )
                return True
                
            # Handle cancellation of job deletion
            elif query.data.startswith("cancel_delete_job_"):
                await query.answer("Penghapusan dibatalkan")
                
                # Get the job ID and message IDs from the callback data
                callback_parts = query.data.replace("cancel_delete_job_", "").split("_")
                job_id = int(callback_parts[0])
                
                # Extract message IDs from callback data (for preserving them)
                image_message_id = None
                info_message_id = None
                
                if len(callback_parts) > 1 and callback_parts[1] != "0":
                    try:
                        image_message_id = int(callback_parts[1])
                    except (ValueError, IndexError):
                        logger.error(f"Invalid image message ID format in callback: {query.data}")
                        
                if len(callback_parts) > 2 and callback_parts[2] != "0":
                    try:
                        info_message_id = int(callback_parts[2])
                    except (ValueError, IndexError):
                        logger.error(f"Invalid info message ID format in callback: {query.data}")
                
                # Get job details to re-show the job post
                job = self.db.get_job_details(job_id)
                if not job:
                    await query.edit_message_text("❌ Penghapusan lowongan dibatalkan, tetapi data lowongan tidak ditemukan.")
                    return True
                
                # Escape special Markdown characters
                escaped_description = job['description'].replace('*', '\\*').replace('_', '\\_').replace('`', '\\`').replace('[', '\\[')
                
                # Recreate the delete button with confirmation
                # Include the image and info message IDs in the callback data to preserve them
                delete_callback_data = f"confirm_delete_job_{job['id']}"
                if image_message_id:
                    delete_callback_data = f"confirm_delete_job_{job['id']}_{image_message_id}_{info_message_id or 0}"
                elif info_message_id:
                    delete_callback_data = f"confirm_delete_job_{job['id']}_0_{info_message_id}"
                    
                keyboard = [[InlineKeyboardButton("🗑️ Hapus", callback_data=delete_callback_data)]]
                reply_markup = InlineKeyboardMarkup(keyboard)
                
                # Check if job has an image (poster_file_id)
                if job.get('poster_file_id'):
                    # For jobs with images, just show the job description with the delete button
                    # This is similar to how my_posts shows it - the date and link are in a separate message
                    await query.edit_message_text(
                        escaped_description,
                        reply_markup=reply_markup,
                        parse_mode='Markdown'
                    )
                else:
                    # For jobs without images, include the date and link info
                    # Create message link if message_id exists
                    message_link = ""
                    if job.get('message_id'):
                        # Check if CHANNEL_ID is a username (starts with @) or a numeric ID
                        if str(CHANNEL_ID).startswith('@'):
                            channel_username = str(CHANNEL_ID)[1:]  # Remove the @ symbol
                            job_post_url = f"https://t.me/{channel_username}/{job['message_id']}"
                            message_link = f"\n🔗 Lihat postingan di: {job_post_url}"
                        else:
                            # For channels with numeric IDs
                            job_post_url = f"https://t.me/c/{str(CHANNEL_ID).replace('-100', '')}/{job['message_id']}"
                            message_link = f"\n🔗 Lihat postingan di: {job_post_url}"
                    
                    # Create the job info message (without date)
                    job_info = message_link if message_link else ""
                    
                    # Combined job info and description for non-image posts
                    await query.edit_message_text(
                        f"{job_info}\n\n{escaped_description}",
                        reply_markup=reply_markup,
                        parse_mode='Markdown'
                    )
                return True
                
            # Handle actual job deletion after confirmation
            elif query.data.startswith("delete_job_"):
                await query.answer()
                
                # Parse callback data which can include message IDs (image and info)
                callback_parts = query.data.replace("delete_job_", "").split("_")
                job_id = int(callback_parts[0])
                
                # Check if we have message IDs to delete
                image_message_id = None
                info_message_id = None
                
                # Extract message IDs from callback data
                if len(callback_parts) > 1 and int(callback_parts[1]) > 0:
                    image_message_id = int(callback_parts[1])
                        
                if len(callback_parts) > 2 and int(callback_parts[2]) > 0:
                    info_message_id = int(callback_parts[2])
                
                # Delete the job from database
                channel_message_id = self.db.delete_posted_job(job_id, user_id)
                
                # Track successful operations for status message
                deletion_results = []
                
                # Delete channel message if exists
                if channel_message_id:
                    try:
                        # Convert message_id to integer if it's a string
                        channel_message_id_int = int(channel_message_id) if channel_message_id else None
                        
                        if channel_message_id_int:
                            await context.bot.delete_message(chat_id=CHANNEL_ID, message_id=channel_message_id_int)
                            deletion_results.append("✅ Lowongan berhasil dihapus dari channel")
                        else:
                            deletion_results.append("⚠️ Tidak ada pesan di channel untuk dihapus")
                    except Exception as e:
                        logger.error(f"Error deleting message from channel: {e}")
                        if self.rollbar:
                            self.rollbar.capture_exception(
                                exc_info=(type(e), e, e.__traceback__),
                                request=self.rollbar.telegram_update_to_request(update) if hasattr(self.rollbar, 'telegram_update_to_request') else None,
                                extra_data={
                                    'handler': 'handle_job_callback',
                                    'subfunction': 'delete_channel_message',
                                    'job_id': job_id,
                                    'channel_message_id': channel_message_id
                                }
                            )
                        deletion_results.append("❌ Gagal menghapus lowongan dari channel")
                else:
                    deletion_results.append("❌ Gagal menemukan lowongan di database")
                
                # Use our helper method to delete image and info messages
                messages_to_delete = []
                if image_message_id:
                    messages_to_delete.append(image_message_id)
                if info_message_id:
                    messages_to_delete.append(info_message_id)
                    
                if messages_to_delete:
                    deleted_ids = await self.delete_messages(
                        chat_id=update.effective_chat.id,
                        message_ids=messages_to_delete,
                        context=context
                    )
                    
                    # Add results based on what was successfully deleted
                    if image_message_id in deleted_ids:
                        deletion_results.append("✅ Gambar lowongan berhasil dihapus")
                    elif image_message_id:
                        deletion_results.append("❌ Gagal menghapus gambar lowongan")
                
                # Edit the message with delete button to show deletion status
                await query.edit_message_text("\n".join(deletion_results))
                return True
            
            # Handle URL selection
            elif query.data.startswith("select_url_"):
                await query.answer()
                url_index = int(query.data.replace("select_url_", ""))
                await self.handle_url_selection(update, context, url_index)
                return True
                
            return False
        except Exception as e:
            logger.error(f"Error in handle_job_callback: {e}")
            if self.rollbar:
                # Create detailed error context
                extra_data = {
                    'handler': 'handle_job_callback',
                    'callback_data': callback_data if 'callback_data' in locals() else 'unknown'
                }
                
                # Add user_id if available
                if 'user_id' in locals():
                    extra_data['user_id'] = user_id
                
                # Add job_id if we're processing a job-related callback
                if 'job_id' in locals():
                    extra_data['job_id'] = job_id
                    
                self.rollbar.capture_exception(
                    exc_info=(type(e), e, e.__traceback__),
                    request=self.rollbar.telegram_update_to_request(update) if hasattr(self.rollbar, 'telegram_update_to_request') else None,
                    extra_data=extra_data
                )
            
            # Try to provide feedback to the user
            try:
                query = update.callback_query
                await query.answer("Terjadi kesalahan saat memproses permintaan Anda. Silakan coba lagi.")
            except Exception as answer_error:
                logger.error(f"Error sending answer after callback error: {answer_error}")
                
            # Return True to indicate we handled the callback (even though there was an error)
            return True
