"""
Rollbar service for error tracking and monitoring.
"""
import rollbar
import logging
import os
from functools import wraps
from telegram import Update

logger = logging.getLogger(__name__)

class RollbarService:
    """Service for tracking errors and exceptions with Rollbar."""
    
    def __init__(self, token=None, environment=None, code_version=None):
        """
        Initialize the Rollbar service.
        
        Args:
            token: The Rollbar access token
            environment: Deployment environment (production/staging/development)
            code_version: Version of the application
        """
        self.token = token or os.getenv("ROLLBAR_TOKEN")
        self.enabled = bool(self.token)
        self.environment = environment or "development"
        self.code_version = code_version
        
        if self.enabled:
            rollbar.init(
                access_token=self.token,
                environment=self.environment,
                code_version=self.code_version,
                handler='blocking',  # Use blocking handler instead of async to avoid event loop issues
                locals={'enabled': False}  # Disable local variable collection for security
            )
            # Don't send test message during initialization - it will be sent when the bot starts
            logger.info("Rollbar service initialized")
        else:
            logger.warning("Rollbar service initialized but disabled (no token provided)")
            
    async def send_test_message(self):
        """
        Send a test message to Rollbar to verify configuration.
        This should be called from within an async context (like the main bot loop).
        """
        if self.enabled:
            try:
                rollbar.report_message('Rollbar is configured correctly', 'info')
                logger.info("Rollbar test message sent successfully")
            except Exception as e:
                logger.error(f"Failed to send Rollbar test message: {e}")
    
    def capture_exception(self, exc_info=None, request=None, extra_data=None, user_data=None):
        """
        Capture and report an exception to Rollbar.
        
        Args:
            exc_info: Exception info tuple (type, value, traceback)
            request: Flask/Werkzeug request object or dict with request info
            extra_data: Additional context data
            user_data: User information dict
        """
        if not self.enabled:
            return
            
        try:
            rollbar.report_exc_info(
                exc_info=exc_info,
                request=request,
                extra_data=extra_data,
                person=user_data
            )
        except Exception as e:
            logger.error(f"Error reporting to Rollbar: {e}")
    
    def capture_message(self, message, level='error', request=None, extra_data=None, user_data=None):
        """
        Capture and report a message to Rollbar.
        
        Args:
            message: The message to log
            level: Severity level (debug/info/warning/error/critical)
            request: Flask/Werkzeug request object or dict with request info
            extra_data: Additional context data
            user_data: User information dict
        """
        if not self.enabled:
            return
            
        try:
            rollbar.report_message(
                message=message,
                level=level,
                request=request,
                extra_data=extra_data,
                person=user_data
            )
        except Exception as e:
            logger.error(f"Error reporting to Rollbar: {e}")
    
    def telegram_update_to_request(self, update: Update):
        """Convert a Telegram update to Rollbar request format."""
        if not update:
            return None
            
        return {
            'url': f"telegram://{update.effective_chat.id if update.effective_chat else 'unknown'}",
            'method': update.message.text if update.message else 'callback',
            'headers': {
                'chat_id': update.effective_chat.id if update.effective_chat else None,
                'message_id': update.message.message_id if update.message else None
            },
            'params': update.to_dict()
        }
    
    def error_handler(self, func):
        """
        Decorator to automatically capture exceptions in bot handlers.
        
        Usage:
        @rollbar_service.error_handler
        async def my_handler(update, context):
            # handler code
        """
        @wraps(func)
        async def wrapper(update: Update, context, *args, **kwargs):
            try:
                return await func(update, context, *args, **kwargs)
            except Exception as e:
                # Capture to Rollbar
                self.capture_exception(
                    exc_info=(type(e), e, e.__traceback__),
                    request=self.telegram_update_to_request(update),
                    extra_data={
                        'handler': func.__name__,
                        'context': context.user_data if context else None
                    },
                    user_data={
                        'id': update.effective_user.id if update and update.effective_user else None,
                        'username': update.effective_user.username if update and update.effective_user else None
                    } if update else None
                )
                # Re-raise the exception
                raise
        return wrapper
