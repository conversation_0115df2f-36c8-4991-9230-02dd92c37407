"""
Notification handler for Gigsta Job Bot functionality.
"""
import logging
import asyncio
import aiohttp
from bs4 import BeautifulSoup
from datetime import datetime, time as datetime_time
import pytz
from telegram.ext import ContextTypes
from .base_handler import BaseHandler
import sys
import os

# Add parent directory to path to allow imports from parent directory
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
from config import is_feature_enabled

# Configure logging
logger = logging.getLogger(__name__)

class NotificationHandler(BaseHandler):
    """Handler for sending notifications and daily updates."""
    
    def __init__(self, db, scraper, channel_service=None, followed_keywords=None, analytics=None, rollbar=None):
        """
        Initialize the notification handler.
        
        Args:
            db: The database instance to use
            scraper: JobScraper instance
            channel_service: Service to handle channel operations
            followed_keywords: Dictionary to cache user followed keywords
            analytics: The analytics service for tracking events
        """
        super().__init__(db, scraper)
        self.channel_service = channel_service
        self.followed_keywords = followed_keywords or {}
        self.analytics = analytics
        self.rollbar = rollbar
        
    async def send_daily_updates(self, context: ContextTypes.DEFAULT_TYPE):
        """
        Scrape jobs and send updates to users following job keywords.
        
        Args:
            context: The context object from Telegram
        """
        # Don't run this in development unless explicitly enabled
        if not is_feature_enabled("enable_daily_scraping"):
            logger.info("Daily scraping is disabled in the current environment")
            return
            
        logger.info("Starting daily job updates")
        
        try:
            # Get all job posts
            jobs = await self.scraper.scrape_jobs()
            
            if not jobs:
                logger.info("No new jobs found during scheduled update")
                return
                
            # Get all followed keywords
            all_followed_keywords = self.db.load_subscriptions()
            
            # Set to track which users have been notified
            notified_users = {}
            total_notifications = 0
            new_jobs_count = 0
            
            # Create a job URL cache to check if a job is new and avoid DB checks for each keyword
            job_url_cache = {}  # Cache of job URLs to whether they exist in the database
            
            for job in jobs:
                # Check if this job already exists in the database using our cache
                if job['url'] in job_url_cache:
                    job_exists = job_url_cache[job['url']]
                else:
                    job_exists = self.db.job_exists(job['url'])
                    job_url_cache[job['url']] = job_exists
                
                # Only process new jobs
                if not job_exists:
                    # Save new job to database
                    self.db.save_job(job['url'], job['title'], job['company'], job['date_posted'])
                    new_jobs_count += 1
                    
                    # Get the job content (title and description) for keyword matching
                    job_content = f"{job['title']} {job['company']} {job['content']}".lower()
                    
                    # For each user, check if the job matches their keywords
                    for user_id, keywords_list in all_followed_keywords.items():
                        # Initialize counter for this user if not already done
                        if user_id not in notified_users:
                            notified_users[user_id] = 0
                            
                        for keyword in keywords_list:
                            if keyword.lower() in job_content:
                                # If this is the first match for this user and job, notify them
                                if notified_users[user_id] < 5:  # Limit notifications per user
                                    try:
                                        job_message = (
                                            f"🔔 <b>Ada lowongan baru yang cocok dengan kata kunci Anda:</b> \"{keyword}\"\n\n"
                                            f"<b>{job['title']}</b>\n"
                                            f"Perusahaan: {job['company']}\n"
                                            f"Diposting: {job['date_posted']}\n\n"
                                            f"<a href=\"{job['url']}\">Lihat detail & lamar</a>"
                                        )
                                        
                                        await context.bot.send_message(
                                            chat_id=user_id,
                                            text=job_message,
                                            parse_mode='HTML',
                                            disable_web_page_preview=True
                                        )
                                        notified_users[user_id] += 1
                                        total_notifications += 1
                                        
                                        logger.info(f"Notified user {user_id} about job: {job['title']}")
                                        # Add a small delay to avoid rate limiting
                                        await asyncio.sleep(0.2)
                                    except Exception as e:
                                        logger.error(f"Error sending notification to user {user_id}: {e}")
                                break  # Skip other keywords for this user once we have a match
            
            # Track daily update metrics
            if self.analytics:
                self.analytics.track_daily_update(new_jobs_count, len(notified_users))
                
            logger.info(f"Daily update complete. Found {new_jobs_count} new jobs. Sent {total_notifications} notifications to {len(notified_users)} users.")
        except Exception as e:
            logger.error(f"Error in daily job updates: {e}")
        
    async def schedule_daily_jobs(self, context: ContextTypes.DEFAULT_TYPE):
        """
        Schedule daily job updates.
        
        Args:
            context: The context object from Telegram
        """
        try:
            # Set up update times - default around 8 AM and 5 PM Indonesia time
            update_times = []
            
            # Add morning update
            if is_feature_enabled("morning_update"):
                morning_time = datetime_time(8, 0, 0, tzinfo=pytz.timezone('Asia/Jakarta'))
                update_times.append(morning_time)
                logger.info("Morning update enabled")
            
            # Add evening update
            if is_feature_enabled("evening_update"):
                evening_time = datetime_time(17, 0, 0, tzinfo=pytz.timezone('Asia/Jakarta'))
                update_times.append(evening_time)
                logger.info("Evening update enabled")
            
            # Additional update time for testing in development
            if is_feature_enabled("test_update"):
                # Schedule a test update 2 minutes from now
                now = datetime.now(pytz.timezone('Asia/Jakarta'))
                test_minute = (now.minute + 2) % 60
                test_hour = now.hour + (1 if test_minute < now.minute else 0)
                test_time = datetime_time(test_hour % 24, test_minute, 0, tzinfo=pytz.timezone('Asia/Jakarta'))
                update_times.append(test_time)
                logger.info(f"Test update scheduled at {test_hour}:{test_minute}")
            
            for update_time in update_times:
                context.job_queue.run_daily(
                    self.send_daily_updates,
                    time=update_time,
                    name=f"daily_update_{update_time.hour}_{update_time.minute}"
                )
                logger.info(f"Scheduled daily update at {update_time.hour}:{update_time.minute} Jakarta time")
        
            if not update_times:
                logger.warning("No update times scheduled!")
        except Exception as e:
            logger.error(f"Error scheduling daily jobs: {e}")
            
    async def notify_followers_about_job(self, context: ContextTypes.DEFAULT_TYPE, job_id: int):
        """
        Notify users following job keywords about a job posting.
        
        Args:
            context: The context object from Telegram
            job_id: The ID of the job to notify about
        """
        # Get job data
        job = self.db.get_job_details(job_id)
        if not job:
            logger.error(f"Job with ID {job_id} not found")
            return
        
        # Get all subscriptions
        all_subscriptions = self.db.load_subscriptions()
        
        # For each user, check if the job matches their keywords
        notified_users = set()
        
        try:
            job_content = job['description'].lower()
            channel_link = self.channel_service.get_channel_link() if self.channel_service else "Gigsta Jobs"
            
            for user_id, keywords_list in all_subscriptions.items():
                for keyword in keywords_list:
                    # Check if the keyword is in the job content
                    if keyword.lower() in job_content:
                        # Only notify each user once, even if multiple keywords match
                        if user_id not in notified_users:
                            try:
                                # Format the notification message
                                notification = (
                                    f"🔔 Ada lowongan baru yang cocok dengan kata kunci Anda: \"{keyword}\"\n\n"
                                    f"Cek lowongan di channel: {channel_link}"
                                )
                                
                                await context.bot.send_message(
                                    chat_id=user_id,
                                    text=notification
                                )
                                
                                notified_users.add(user_id)
                                
                                # Track notification in analytics
                                if self.analytics:
                                    # Try to get user info for analytics
                                    try:
                                        chat = await context.bot.get_chat(user_id)
                                        username = chat.username
                                        self.analytics.track_notification_sent(user_id, job_id, keyword, username=username)
                                    except:
                                        # If we can't get username, still track without it
                                        self.analytics.track_notification_sent(user_id, job_id, keyword)
                                        
                                logger.info(f"Notified user {user_id} about job {job_id} with keyword {keyword}")
                            except Exception as e:
                                logger.error(f"Error sending notification to user {user_id}: {e}")
                        break  # No need to check other keywords for this user
        except Exception as e:
            logger.error(f"Error in notify_followers: {e}")
            
        logger.info(f"Notified {len(notified_users)} users about job {job_id}")
