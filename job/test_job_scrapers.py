import asyncio
import json
import logging
import sys
from job_scraper import JobScraper

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def preview_jobs(jobs, source_name):
    print(f"\nPreview of the first 5 jobs from {source_name}:")
    for i, job in enumerate(jobs[:5]):
        print(f"\nJob {i+1}:")
        for key, value in job.items():
            print(f"  {key}: {value}")

async def main(selected_sources=None):
    scraper = JobScraper()
    results = {}

    if not selected_sources or 'glints' in selected_sources:
        print("Scraping jobs from Glints...")
        glints_jobs = await scraper.scrape_glints()
        print(f"Found {len(glints_jobs)} jobs on Glints")
        results['glints'] = glints_jobs
        output_file = "glints_jobs.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(glints_jobs, f, ensure_ascii=False, indent=2)
        print(f"Results saved to {output_file}")
        if glints_jobs:
            preview_jobs(glints_jobs, "Glints")

    if not selected_sources or 'jobstreet' in selected_sources:
        print("\nScraping jobs from Jobstreet...")
        jobstreet_jobs = await scraper.scrape_jobstreet()
        print(f"Found {len(jobstreet_jobs)} jobs on Jobstreet")
        results['jobstreet'] = jobstreet_jobs
        output_file = "jobstreet_jobs.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(jobstreet_jobs, f, ensure_ascii=False, indent=2)
        print(f"Results saved to {output_file}")
        if jobstreet_jobs:
            preview_jobs(jobstreet_jobs, "Jobstreet")

if __name__ == "__main__":
    # Usage: python test_job_scrapers.py [glints|jobstreet|both]
    sources = None
    if len(sys.argv) > 1:
        arg = sys.argv[1].lower()
        if arg == 'glints':
            sources = ['glints']
        elif arg == 'jobstreet':
            sources = ['jobstreet']
        elif arg == 'both':
            sources = None  # Both
        else:
            print("Unknown argument. Use 'glints', 'jobstreet', or 'both'. Running both by default.")
    asyncio.run(main(sources))
