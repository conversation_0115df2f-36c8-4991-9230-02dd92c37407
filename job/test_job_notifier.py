import logging
import async<PERSON>
import random
import pytz
from datetime import datetime, time, timedelta
from telegram.ext import Application
from credentials import BOT_TOKEN
from gigsta_job_notifier_bot import JobNotifierBot

# Configure logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

class TestJobNotifierBot(JobNotifierBot):
    async def schedule_test_jobs(self, context):
        """
        Schedule jobs for the next 3 minutes at random times
        instead of morning, afternoon, and evening
        """
        # Clear existing jobs (except this scheduler)
        current_jobs = context.job_queue.jobs()
        for job in current_jobs:
            if job.name != "test_scheduler":
                job.schedule_removal()
        
        # Get current time
        now = datetime.now(pytz.timezone('Asia/Jakarta'))
        
        # Generate 3 random times within the next 3 minutes
        test_times = []
        for i in range(3):
            # Random seconds between 10 and 60*3 (3 minutes)
            random_seconds = random.randint(10 + i*60, 10 + (i+1)*60)
            test_time = now + timedelta(seconds=random_seconds)
            test_times.append(test_time)
        
        # Log the scheduled times
        time_strings = [t.strftime("%H:%M:%S") for t in test_times]
        print(f"\n\n*** SCHEDULED TEST JOBS FOR: {', '.join(time_strings)} ***\n\n")
        logger.info(f"Scheduled test jobs for: {', '.join(time_strings)}")
        
        # Schedule the jobs
        for i, test_time in enumerate(test_times):
            context.job_queue.run_once(
                self.send_daily_updates,
                when=test_time,
                name=f"test_update_{i+1}"
            )
        
        logger.info("Test jobs scheduled successfully")
    
    async def send_daily_updates(self, context):
        """Override to avoid database errors during testing"""
        try:
            logger.info("Starting test job execution...")
            
            # Get jobs from scraper using the correct methods
            all_jobs = []
            
            # Collect jobs from Glints
            glints_jobs = await self.scraper.scrape_glints()
            if glints_jobs:
                all_jobs.extend(glints_jobs)
                logger.info(f"Collected {len(glints_jobs)} jobs from Glints")
            
            # Collect jobs from Jobstreet
            jobstreet_jobs = await self.scraper.scrape_jobstreet()
            if jobstreet_jobs:
                all_jobs.extend(jobstreet_jobs)
                logger.info(f"Collected {len(jobstreet_jobs)} jobs from Jobstreet")
            
            if not all_jobs:
                logger.info("No jobs found to send")
                return
            
            # Skip database operations that might cause errors
            new_jobs = all_jobs[:5]  # Just use the first 5 jobs for testing
            logger.info(f"Using {len(new_jobs)} jobs for testing")
            
            # Format and send message
            message_header = f" {len(new_jobs)} Test Job Listings \n\n"
            
            # Create individual job messages
            job_messages = []
            for job in new_jobs:
                job_message = (
                    f" {job['title']}\n"
                    f" {job['company']}\n"
                    f" {job['location']}\n"
                    f" {job['source']}\n"
                    f"Link: {job['url']}\n\n"
                )
                job_messages.append(job_message)
            
            # Group job messages into chunks that fit within Telegram's limit
            messages = []
            current_message = message_header
            
            for job_message in job_messages:
                # Check if adding this job would exceed the limit
                if len(current_message) + len(job_message) > 4000:  # Using 4000 to leave some buffer
                    messages.append(current_message)
                    current_message = message_header + job_message
                else:
                    current_message += job_message
            
            # Add the last message if it's not empty
            if current_message != message_header:
                messages.append(current_message)
            
            # Send all messages
            for msg in messages:
                await context.bot.send_message(
                    chat_id=context.bot_data.get("test_chat_id", "YOUR_TEST_CHAT_ID"),
                    text=msg,
                    disable_web_page_preview=True
                )
            
            logger.info(f"Test completed: Sent {len(new_jobs)} job listings")
        except Exception as e:
            logger.error(f"Error in test job execution: {e}")

async def main():
    """Run the test bot."""
    print("\n\n*** STARTING TEST BOT ***\n\n")
    logger.info("Starting test bot...")
    
    # Create the Application
    application = Application.builder().token(BOT_TOKEN).build()
    
    # Create bot instance
    bot = TestJobNotifierBot()
    
    # Schedule test jobs to run in the next 3 minutes
    application.job_queue.run_once(bot.schedule_test_jobs, when=1)
    
    # Start the Bot
    logger.info("Starting polling...")
    
    # Run the bot for a limited time (5 minutes should be enough for our test)
    await application.initialize()
    await application.start()
    
    # Wait for 5 minutes
    await asyncio.sleep(5 * 60)
    
    # Stop the bot
    await application.stop()
    await application.shutdown()

if __name__ == '__main__':
    asyncio.run(main())
