import logging
import as<PERSON><PERSON>
import os
from pydantic import BaseModel, Field
from crawl4ai import <PERSON>ync<PERSON>eb<PERSON>rawler, BrowserConfig, CrawlerRunConfig, CacheMode, LLMConfig
from crawl4ai.extraction_strategy import LLMExtractionStrategy
from crawl4ai.async_crawler_strategy import AsyncPlaywrightCrawlerStrategy
from crawl4ai.browser_manager import BrowserManager
import sys
import os
import json

# Add parent directory to path to allow imports from parent directory
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config import GEMINI_API_KEY

# Configure logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

# Patch for crawl4ai to fix resource cleanup issue
async def patched_async_playwright__crawler_strategy_close(self) -> None:
    """
    Close the browser and clean up resources.

    This patch addresses an issue with Playwright instance cleanup where the static instance
    wasn't being properly reset, leading to issues with multiple crawls.

    Issue: https://github.com/unclecode/crawl4ai/issues/842

    Returns:
        None
    """
    await self.browser_manager.close()

    # Reset the static Playwright instance
    BrowserManager._playwright_instance = None

# Apply the patch
AsyncPlaywrightCrawlerStrategy.close = patched_async_playwright__crawler_strategy_close

# Define the Pydantic model for job list
class JobList(BaseModel):
    url: str = Field(..., description="URL of the job.")

# Define the Pydantic model for job details
class JobDetails(BaseModel):
    url: str = Field(..., description="URL of the job.")
    title: str = Field(..., description="Title of the job.")
    description: str = Field(..., description="Description of the job.")
    company: str = Field(..., description="Company of the job.")
    location: str = Field(..., description="Location of the job.")
    source: str = Field(..., description="Source of the job.")

class JobScraper:
    async def _scrape_jobs(self, *, search_url, job_url_instruction, job_url_schema, job_detail_instruction, job_detail_schema, source_name):
        jobs = []
        try:
            browser_config = BrowserConfig(verbose=True)
            run_config = CrawlerRunConfig(
                word_count_threshold=1,
                extraction_strategy=LLMExtractionStrategy(
                    llm_config=LLMConfig(
                        provider="gemini/gemini-2.0-flash",
                        api_token=GEMINI_API_KEY
                    ),
                    schema=job_detail_schema,
                    extraction_type="schema",
                    instruction=job_detail_instruction
                ),
                cache_mode=CacheMode.BYPASS,
                check_robots_txt=True,
            )

            async with AsyncWebCrawler(config=browser_config) as crawler:
                job_list_config = CrawlerRunConfig(
                    word_count_threshold=1,
                    extraction_strategy=LLMExtractionStrategy(
                        llm_config=LLMConfig(
                            provider="gemini/gemini-2.0-flash",
                            api_token=GEMINI_API_KEY
                        ),
                        schema=job_url_schema,
                        extraction_type="schema",
                        instruction=job_url_instruction
                    ),
                    cache_mode=CacheMode.BYPASS,
                    check_robots_txt=True,
                )

                search_result = await crawler.arun(url=search_url, config=job_list_config, magic=True, simulate_user=True, override_navigator=True)
                job_items_str = search_result.extracted_content

                if not job_items_str:
                    logger.error(f"Failed to extract job URLs from {source_name} search page")
                    return jobs

                job_items = json.loads(job_items_str)
                print(f"Extracted {source_name} job URLs: {job_items}")

                if not isinstance(job_items, list):
                    logger.error(f"Unexpected format for {source_name} job URLs: {job_items}")
                    return jobs

                job_items = job_items[:10] # TODO: don't limit
                print(f"Extracted {source_name} job URLs: {len(job_items)}")

                for job_item in job_items:
                    try:
                        url = job_item['url'] if isinstance(job_item, dict) and 'url' in job_item else job_item
                        job_result = await crawler.arun(url=url, config=run_config, magic=True, simulate_user=True, override_navigator=True)
                        detailed_jobs_str = job_result.extracted_content

                        if not detailed_jobs_str:
                            logger.error(f"Failed to extract job details from {url}")
                            continue

                        detailed_jobs = json.loads(detailed_jobs_str)
                        print(f"Extracted {source_name} job details: {detailed_jobs}")

                        if isinstance(detailed_jobs, list) and len(detailed_jobs) > 0:
                            job = detailed_jobs[0]
                            if not job.get('error', False):
                                job_dict = dict(job)
                                print(f"Job keys: {job_dict.keys()}")
                                if 'error' in job_dict:
                                    del job_dict['error']
                                jobs.append(job_dict)
                        elif isinstance(detailed_jobs, dict):
                            if not detailed_jobs.get('error', False):
                                job_dict = dict(detailed_jobs)
                                if 'error' in job_dict:
                                    del job_dict['error']
                                jobs.append(job_dict)
                    except Exception as e:
                        logger.error(f"Error scraping {source_name} job detail: {e}")
        except Exception as e:
            logger.error(f"Error scraping {source_name}: {e}")
        return jobs

    async def scrape_glints(self):
        return await self._scrape_jobs(
            search_url="https://glints.com/id/opportunities/jobs/explore?country=ID&locationName=All+Cities%2FProvinces&lowestLocationLevel=1&sortBy=LATEST",
            job_url_instruction="""From the crawled content, extract ONLY the first 10 job listing URLs as a JSON array.\nEach URL should be in the format 'https://glints.com/id/opportunities/jobs/[job-title]/[job-id]'.\nDo not include any parameters or query strings.\nReturn a JSON array of strings containing only these 10 URLs and nothing else.""", # TODO: don't limit
            job_url_schema=JobList.model_json_schema(),
            job_detail_instruction="""From the crawled content, extract all mentioned job listings. For each job listing, extract the job title, description, company, location, source, and job post URL.

For the job description, follow this formatting:
- Use section headers if any (such as Kualifikasi, Tanggung Jawab, Benefit, Job Responsibilities, Job Requirements, Preferred Qualifications, etc) on their own lines, without any extra characters or formatting.
- For each item under a section, start the line with a bullet point (•) and a space.
- There should be no extra line spacing between each item and the header.
- Ensure there is a blank line between different sections or paragraphs, so the text is easy to read.
- Do NOT use asterisks (*), Markdown, or HTML tags in the output.
- Make sure each bullet point appears on its own line.
- The description text can be in any languages.
- Do NOT include the description title line like "Deskripsi pekerjaan.....".
- Example format:

Kami mencari barista keliling yang tidak hanya bisa menyajikan kopi, tapi juga punya inisiatif tinggi dan mampu menganalisis lokasi/jalan yang strategis untuk berjualan.

Kualifikasi:
• Suka tantangan dan aktif di lapangan
• Bisa ambil keputusan cepat di lokasi (dimana harus berhenti, pindah, dll)
• Jujur, tanggung jawab, dan komunikatif

Benefit:
• Uang kehadiran: Rp50.000/hari
• Uang makan: Rp15.000/hari

Return the description as a single plain text string following the above format, with clear spacing between sections and bullet points.""",
            job_detail_schema=JobDetails.model_json_schema(),
            source_name="Glints"
        )

    async def scrape_jobstreet(self):
        return await self._scrape_jobs(
            search_url="https://id.jobstreet.com/id/jobs?sortmode=ListedDate",
            job_url_instruction="""From the crawled content, extract ONLY the first 10 job listing URLs as a JSON array.\nEach URL should be in the format 'https://id.jobstreet.com/id/job/[job-id]'.\nDo not include any parameters or query strings.\nReturn a JSON array of strings containing only these 10 URLs and nothing else.""", # TODO: don't limit
            job_url_schema=JobList.model_json_schema(),
            job_detail_instruction="""From the crawled content, extract all mentioned job listings. For each job listing, extract the job title, description, company, location, source, and job post URL.

For the job description, follow this formatting:
- Use section headers if any (such as Deskripsi pekerjaan, Kualifikasi, Tanggung Jawab, Benefit, Job Responsibilities, Job Requirements, Preferred Qualifications, etc) on their own lines, without any extra characters or formatting.
- For each item under a section, start the line with a bullet point (•) and a space.
- There should be no extra line spacing between each item and the header.
- Ensure there is a blank line between different sections or paragraphs, so the text is easy to read.
- Do NOT use asterisks (*), Markdown, or HTML tags in the output.
- Make sure each bullet point appears on its own line.
- The description text can be in any languages.
- Example format:

Kami mencari barista keliling yang tidak hanya bisa menyajikan kopi, tapi juga punya inisiatif tinggi dan mampu menganalisis lokasi/jalan yang strategis untuk berjualan.

Kualifikasi:
• Suka tantangan dan aktif di lapangan
• Bisa ambil keputusan cepat di lokasi (dimana harus berhenti, pindah, dll)
• Jujur, tanggung jawab, dan komunikatif

Benefit:
• Uang kehadiran: Rp50.000/hari
• Uang makan: Rp15.000/hari

Return the description as a single plain text string following the above format, with clear spacing between sections and bullet points.""",
            job_detail_schema=JobDetails.model_json_schema(),
            source_name="Jobstreet"
        )