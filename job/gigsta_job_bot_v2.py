"""
Gig<PERSON> Job Bot - Telegram bot for job posting and job following management.
Refactored to use a modular architecture with separate handlers for different functionalities.
"""

# Version information
__version__ = '1.2.0'

import logging
import pytz
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import Application, CommandHandler, CallbackQueryHandler, ContextTypes, MessageHandler, filters
from datetime import datetime, time as datetime_time, timedelta
import sys
import os
import random

# Add parent directory to path to allow imports from parent directory
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config import BOT_TOKEN, ENV, is_feature_enabled, MIXPANEL_TOKEN, CHANNEL_ID, ROLLBAR_TOKEN
from job_scraper import <PERSON><PERSON><PERSON>raper
from gigsta_database import JobDatabase

# Import handlers
from job.handlers.following_handler import <PERSON><PERSON>and<PERSON>
from job.handlers.job_posting_handler import <PERSON>P<PERSON>ing<PERSON>andler
from job.handlers.notification_handler import NotificationHandler
from job.handlers.channel_service import ChannelService
from job.handlers.analytics_service import AnalyticsService
from job.handlers.rollbar_service import RollbarService

# Configure logging
logging_level = logging.DEBUG if is_feature_enabled("debug_logging") else logging.INFO
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging_level
)
logger = logging.getLogger(__name__)

# Log environment information
logger.info(f"Starting bot in {ENV} environment")

# Bot configuration
TELEGRAM_TOKEN = BOT_TOKEN

class JobBot:
    """Main bot class that coordinates handlers and services."""
    HELP_CONFIRM_CALLBACK = "help_contact_confirm"
    
    def __init__(self):
        """Initialize the bot and its handlers."""
        # Initialize shared state
        self.db = JobDatabase()
        self.scraper = JobScraper()
        self.active_commands = {}
        self.user_states = {}
        self.followed_keywords = self.db.load_subscriptions()  # Database method name will be updated later
        
        # Initialize analytics and error tracking services
        self.analytics = AnalyticsService(MIXPANEL_TOKEN, version=__version__)
        self.rollbar = RollbarService(ROLLBAR_TOKEN, environment=ENV, code_version=__version__)
        self.db.set_rollbar(self.rollbar)
        
        # Initialize handlers and services
        self.channel_service = ChannelService(self.db)
        self.following_handler = FollowingHandler(self.db, self.active_commands, self.followed_keywords, self.analytics, self.rollbar)
        self.job_posting_handler = JobPostingHandler(self.db, self.active_commands, self.user_states, self.analytics, self.rollbar)
        self.notification_handler = NotificationHandler(self.db, self.scraper, self.channel_service, self.followed_keywords, self.analytics, self.rollbar)
        
    async def help_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle the /help command."""
        keyboard = [
            [InlineKeyboardButton("Ya, hubungi saya", callback_data=self.HELP_CONFIRM_CALLBACK)],
            [InlineKeyboardButton("Tidak", callback_data="help_contact_cancel")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        await update.message.reply_text(
            "Jika Anda memiliki kendala, tim kami dapat membantu Anda melalui chat Telegram. Apakah Anda ingin dihubungi oleh tim kami?",
            reply_markup=reply_markup
        )

    async def start(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle the /start command."""
        # Track user interaction
        user_id = update.effective_user.id
        username = update.effective_user.username
        self.analytics.track_user_start(user_id, username)
        
        # Cancel any previous commands
        await self.following_handler.cancel_previous_commands(update, context, "start")
        
        # Get channel link using the helper method
        channel_link = self.channel_service.get_channel_link()
        
        # Format the channel info text
        channel_info = f"Channel: {channel_link}"
            
        welcome_message = (
            f"Hai! Selamat datang di Gigsta Bot!\n\n"
            f"Saya bisa membantu Anda:\n"
            f"1. Mendapatkan notifikasi lowongan berdasarkan kata kunci 🔔\n"
            f"2. Posting lowongan kerja Anda ke channel kami dan ke para pengikut lowongan 📝\n"
            f"3. Mengelola lowongan yang sudah Anda posting 🗂️\n\n"
            f"{channel_info}\n\n"
            f"Perintah yang tersedia:\n"
            f"/follow - Mendapatkan notifikasi lowongan berdasarkan kata kunci\n"
            f"/following - Lihat kata kunci yang Anda ikuti\n"
            f"/unfollow - Berhenti mengikuti kata kunci\n"
            f"/post - Posting lowongan kerja baru\n"
            f"/myposts - Lihat dan kelola lowongan yang telah Anda posting\n"
            f"/help - Dapatkan bantuan"
        )
        await update.message.reply_text(welcome_message)
    
    async def handle_message(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Route messages to the appropriate handler."""
        # First try the job posting handler
        if await self.job_posting_handler.handle_job_posting_flow(update, context):
            return
            
        # Then try the following handler
        if await self.following_handler.handle_following(update, context, self.user_states):
            return
            
        # If no handler processed the message, provide a helpful response
        await update.message.reply_text(
            "Silakan gunakan salah satu perintah berikut:\n"
            "/follow - Dapatkan notifikasi lowongan berdasarkan kata kunci\n"
            "/following - Lihat kata kunci yang Anda ikuti\n"
            "/unfollow - Berhenti mengikuti kata kunci\n"
            "/post - Posting lowongan kerja baru\n"
            "/myposts - Lihat dan kelola lowongan yang telah Anda posting\n"
            "/help - Dapatkan bantuan"
        )
        
    async def handle_callback(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Route callback queries to the appropriate handler."""
        query = update.callback_query
        data = query.data
        user = query.from_user
        
        # Handle /help confirmation
        if data == self.HELP_CONFIRM_CALLBACK:
            # Notify @amwalul with the user's username
            username = user.username

            if not username:
                await query.edit_message_text("Username tidak ditemukan. Kami membutuhkan username agar dapat menghubungi Anda.")
                return
            
            await context.bot.send_message(
                chat_id='342821960', # @amwalul
                text=f"User meminta bantuan: @{username}"
            )
            await query.edit_message_text("Tim kami akan segera menghubungi Anda melalui Telegram. Terima kasih!")
            return
        elif data == "help_contact_cancel":
            await query.edit_message_text("Permintaan bantuan dibatalkan.")
            return
        
        # First try the job posting handler
        if await self.job_posting_handler.handle_job_callback(update, context):
            return
            
        # Then try the following handler
        if await self.following_handler.handle_unfollow_callback(update, context):
            return
            
        # If no handler processed the callback, log it
        logger.warning(f"Unhandled callback query: {update.callback_query.data}")
        await update.callback_query.answer("Command not recognized")
        
    async def scrape_jobs(self, context: ContextTypes.DEFAULT_TYPE):
        logger.info("Starting to collect jobs...")
        # Collect jobs from all sources
        all_jobs = []
        
        # Collect jobs from Glints
        glints_jobs = await self.scraper.scrape_glints()
        if glints_jobs:
            all_jobs.extend(glints_jobs)
            logger.info(f"Collected {len(glints_jobs)} jobs from Glints")
        
        # Collect jobs from Jobstreet
        jobstreet_jobs = await self.scraper.scrape_jobstreet()
        if jobstreet_jobs:
            all_jobs.extend(jobstreet_jobs)
            logger.info(f"Collected {len(jobstreet_jobs)} jobs from Jobstreet")
        
        if not all_jobs:
            logger.info("No jobs found to send")
            return

        # Save jobs to scraped_jobs database and get only new ones
        new_jobs = self.db.save_jobs(all_jobs)
        
        if not new_jobs:
            logger.info("No new jobs to send - all jobs already exist in database")
            return
            
        logger.info(f"Found {len(new_jobs)} new jobs out of {len(all_jobs)} total scraped jobs")

        # Helper to determine delivery window based on scraping time
        def get_delivery_window(scraped_time):
            tz = pytz.timezone('Asia/Jakarta')
            now = datetime.now(tz)
            if ENV == "development":
                start = now
                end = now + timedelta(minutes=5)
                return (start, end)
            # Production: Return (start_datetime, end_datetime)
            if 5 <= scraped_time.hour < 7:
                start = now.replace(hour=7, minute=0, second=0, microsecond=0)
                end = now.replace(hour=10, minute=30, second=0, microsecond=0)
            elif 11 <= scraped_time.hour < 13:
                start = now.replace(hour=13, minute=0, second=0, microsecond=0)
                end = now.replace(hour=16, minute=30, second=0, microsecond=0)
            elif 17 <= scraped_time.hour < 19:
                start = now.replace(hour=19, minute=0, second=0, microsecond=0)
                end = now.replace(hour=0, minute=0, second=0, microsecond=0) + timedelta(days=1)
            else:
                return None
            if end <= start:
                end += timedelta(days=1)
            return (start, end)

        # Helper to schedule a single job delivery
        def schedule_job_delivery(job, delivery_window):
            # delivery_window is always (start_datetime, end_datetime)
            start, end = delivery_window
            delta = (end - start).total_seconds()
            send_time = start + timedelta(seconds=random.randint(0, int(delta)))
            context.job_queue.run_once(
                self.send_single_job,
                when=send_time,
                data=job
            )

        # Schedule each new job to be sent individually in the correct delivery window
        now = datetime.now(pytz.timezone('Asia/Jakarta'))
        for job in new_jobs:
            # Use the current time as the scrape time for this batch
            delivery_window = get_delivery_window(now)
            if delivery_window is not None:
                schedule_job_delivery(job, delivery_window)
            else:
                # Fallback: send immediately if window can't be determined
                context.job_queue.run_once(
                    self.send_single_job,
                    when=now + timedelta(seconds=random.randint(0, 60)),
                    data=job
                )
        logger.info(f"Scheduled {len(new_jobs)} new jobs for individual delivery to channel and followers.")

    async def send_single_job(self, context: ContextTypes.DEFAULT_TYPE):
        job = context.job.data
        job_message = (
            f"💼 {job['title']}\n"
            f"🏢 {job['company']}\n"
            f"📍 {job['location']}\n\n"
            f"{job['description']}"
        )
        # Add 'Lamar Sekarang' button if URL is present
        reply_markup = None
        if 'url' in job and job['url']:
            reply_markup = InlineKeyboardMarkup([
                [InlineKeyboardButton("Lamar Sekarang", url=job['url'])]
            ])
        # Send to channel and get the message object
        message = await context.bot.send_message(
            chat_id=CHANNEL_ID,
            text=job_message,
            disable_web_page_preview=True,
            reply_markup=reply_markup
        )
        
        # Store the message ID in the database
        if message and message.message_id:
            # Update the job data with the message ID
            job['message_id'] = str(message.message_id)
            # Update the database record
            self.db.update_scraped_job_message_id(job['url'], str(message.message_id))
            logger.info(f"Updated message ID {message.message_id} for job '{job['title']}'")
        
        # Notify followers who have subscribed to keywords in this job
        # Using the generic notification function for scraped jobs
        notified_users = await self.job_posting_handler.notify_followers_for_job_data(context, job)
        logger.info(f"Sent job '{job['title']}' to channel and {len(notified_users)} followers.")

    async def schedule_daily_jobs(self, context: ContextTypes.DEFAULT_TYPE):
        # Clear existing jobs (except this scheduler)
        current_jobs = context.job_queue.jobs()
        for job in current_jobs:
            if job.name != "midnight_scheduler":
                job.schedule_removal()
        
        if ENV == "development":
            # Development mode: Schedule jobs every 5 minutes for debugging
            logger.info("Development mode: scheduling job updates every 5 minutes for debugging")
            context.job_queue.run_repeating(
                self.scrape_jobs,
                interval=300,  # every 5 minutes
                first=10,      # start after 10 seconds
                name="dev_debug_update"
            )
        else:
            # Production mode: Schedule jobs at fixed random windows (3 times per day)
            logger.info("Using fixed random windows for job scheduling (production mode)")
            windows = [
                (5, 6),   # 5:00–6:00
                (11, 12), # 11:00–12:00
                (17, 18), # 17:00–18:00
            ]
            now = datetime.now(pytz.timezone('Asia/Jakarta'))
            for i, (start_hour, end_hour) in enumerate(windows):
                hour = random.randint(start_hour, end_hour - 1)
                minute = random.randint(0, 59)
                second = random.randint(0, 59)
                job_time = now.replace(hour=hour, minute=minute, second=second, microsecond=0)
                # If the scheduled time has already passed today, schedule for tomorrow
                if job_time <= now:
                    job_time += timedelta(days=1)
                job_name = f"daily_update_{i+1}"
                logger.info(f"Scheduling job {job_name} for {job_time.strftime('%Y-%m-%d %H:%M:%S')}")
                context.job_queue.run_once(
                    self.scrape_jobs,
                    when=job_time,
                    name=job_name
                )

async def post_init(application: Application):
    """Set up the bot menu commands that appear in the chat interface."""
    commands = [
        ("start", "Mulai bot dan lihat bantuan"),
        ("follow", "Mengikuti kata kunci"),
        ("following", "Lihat kata kunci yang Anda ikuti"),
        ("unfollow", "Berhenti mengikuti kata kunci"),
        ("post", "Posting lowongan kerja baru"),
        ("myposts", "Lihat dan kelola postingan"),
        ("help", "Dapatkan bantuan"),
    ]

    await application.bot.delete_my_commands()
    # Temporary enable commands for all of Telegram app languages
    await application.bot.set_my_commands(commands)
    logger.info("Bot commands have been set up")

def main():
    """Initialize and run the bot."""
    # Log version information
    logger.info(f"Starting Gigsta Job Bot v{__version__}")
    # Create the bot instance
    bot = JobBot()
    
    # Create the Application and pass it your bot's token
    application = Application.builder().token(TELEGRAM_TOKEN).post_init(post_init).build()

    # Add handlers for commands
    application.add_handler(CommandHandler("start", bot.start))
    application.add_handler(CommandHandler("help", bot.help_command))
    
    # Job following handlers
    application.add_handler(CommandHandler("follow", bot.following_handler.follow))
    application.add_handler(CommandHandler("following", bot.following_handler.following))
    application.add_handler(CommandHandler("unfollow", bot.following_handler.unfollow))
    
    # Job posting handlers
    application.add_handler(CommandHandler("post", bot.job_posting_handler.post_job))
    application.add_handler(CommandHandler("myposts", bot.job_posting_handler.my_posts))
    
    # Add callback query handler
    application.add_handler(CallbackQueryHandler(bot.handle_callback))
    
    # Add message handler (should be after command handlers)
    application.add_handler(MessageHandler(filters.TEXT | filters.PHOTO | filters.Document.ALL, bot.handle_message))
    
    # Schedule daily updates
    job_queue = application.job_queue
    jakarta_tz = pytz.timezone('Asia/Jakarta')
    
    # Schedule the midnight job that will set up each day's random times
    job_queue.run_daily(
        bot.schedule_daily_jobs, 
        time=datetime_time(hour=0, minute=1, tzinfo=jakarta_tz),
        name="midnight_scheduler"
    )
    
    # Run the scheduler once at startup to set up today's jobs
    application.job_queue.run_once(bot.schedule_daily_jobs, when=1)
    
    # Send a test message to Rollbar to verify that it's working properly
    # This runs in the async context after the bot has started
    application.job_queue.run_once(lambda context: bot.rollbar.send_test_message(), when=1)
    
    # Log environment information
    logger.info(f"Bot started in {ENV} environment")
    if ENV == "development":
        logger.info("Development mode active - using 5-minute repeating job schedule for debugging")
    else:
        logger.info("Production mode active - using regular job schedule")
    
    # Start the Bot
    application.run_polling()
    
    logger.info("Bot started successfully")

if __name__ == '__main__':
    main()
