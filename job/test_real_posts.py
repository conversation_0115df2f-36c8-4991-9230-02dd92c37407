"""
Test script for AI contact extractor using real-world job posts.
"""

import time
import random
import logging
from typing import List, Dict, Tuple

from ai_contact_extractor import extract_contact_info

# Configure logging
logging.basicConfig(level=logging.INFO, 
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Test cases for the AI contact extractor
test_cases = [
    {
        "name": "Standard email and URL format",
        "text": """
        Senior Software Engineer - Remote
        
        TechFirm is looking for a Senior Software Engineer to join our growing team!
        
        Requirements:
        - 5+ years of experience with Python
        - Strong knowledge of web frameworks
        - Experience with cloud platforms
        
        Benefits:
        - Competitive salary
        - Remote work option
        - Health insurance
        
        To apply, send your <NAME_EMAIL> or visit our careers page at 
        techfirm.com/jobs/software-engineer
        """
    },
    {
        "name": "Multiple emails and URLs",
        "text": """
        Marketing Manager Position
        
        We're looking for a talented Marketing Manager to join our team. 
        
        Responsibilities:
        - Develop marketing strategies
        - Manage social media campaigns
        - Analyze market trends
        
        Qualifications:
        - Bachelor's degree in Marketing
        - 3+ years of experience
        
        Contact <NAME_EMAIL> or <EMAIL>
        Learn more at company.org/careers or follow us at linkedin.com/company/companyorg
        """
    },
    {
        "name": "Obfuscated contact details",
        "text": """
        Product Designer Needed
        
        Join our team as a Product Designer!
        
        What we're looking for:
        - UX/UI design expertise
        - Portfolio of previous work
        - Collaborative mindset
        
        Reach out to us at design [at] creative-studio [dot] com
        Our website: www (dot) creative-studio (dot) com/jobs
        """
    },
    {
        "name": "Contact details in different formats",
        "text": """
        Data Scientist Opening
        
        We have an exciting opportunity for a Data Scientist!
        
        Required skills:
        - Python, R, SQL
        - Machine learning experience
        - Statistical analysis
        
        Email your resume: <EMAIL>
        Apply online: https://analytics.co/apply
        Or call: (555) 123-4567
        """
    },
    {
        "name": "Very minimal contact info",
        "text": """
        Freelance Writer
        
        Looking for experienced writers for ongoing blog content.
        
        Topics include technology, finance, and health.
        Competitive pay based on experience.
        
        <EMAIL>
        """
    },
    {
        "name": "No explicit contact, implied website",
        "text": """
        Junior Developer Position
        
        Great opportunity for a junior developer to join our startup!
        
        Requirements:
        - Basic coding skills
        - Eager to learn
        - Team player
        
        Check our careers page for application details.
        techstartup.io
        """
    },
    {
        "name": "Indonesian job post - standard format",
        "text": """
        Lowongan Kerja: Social Media Manager
        
        PT Digital Kreatif Indonesia sedang mencari Social Media Manager untuk bergabung dengan tim kami.
        
        Persyaratan:
        - Minimal lulusan S1 Komunikasi atau bidang terkait
        - Memiliki pengalaman 2-3 tahun mengelola media sosial brand
        - Mampu membuat konten kreatif dan menarik
        
        Kirim <NAME_EMAIL> atau kunjungi digitalkreatif.co.id/karir
        """
    },
    {
        "name": "Indonesian job post - minimal info",
        "text": """
        DIBUTUHKAN SEGERA!
        
        Admin Kantor untuk perusahaan distribusi makanan.
        - Pria/Wanita
        - Usia max 30 tahun
        - Domisili Jakarta Utara
        - Menguasai Excel dan Word
        
        Hubungi: <EMAIL>
        """
    },
    {
        "name": "Indonesian job post - with WhatsApp",
        "text": """
        Dibuka Lowongan Staf Accounting
        
        Kualifikasi:
        - Lulusan D3/S1 Akuntansi
        - Menguasai program akuntansi dan pajak
        - Teliti dan bertanggung jawab
        - Berpengalaman min. 1 tahun
        
        Lamaran <NAME_EMAIL>
        Info lebih lanjut hubungi WA: 0812-3456-7890
        Website: pt-sukses.com
        """
    },
    {
        "name": "Indonesian job post - informal style",
        "text": """
        Butuh Graphic Designer Freelance
        
        Project: Desain post & story Instagram untuk brand fashion
        Deadline: 1 minggu (7 desain)
        Fee: Rp 1.5 juta
        
        Minat? Contact via <NAME_EMAIL>
        Portofolio kirim ke link yg sama ya
        
        IG: @fashionista.id
        """
    },
    {
        "name": "Mixed language job post",
        "text": """
        OPEN RECRUITMENT - IT DEVELOPER
        
        We are looking for a talented IT Developer to join our growing team in Jakarta.
        
        Requirements:
        - Bachelor's degree in Computer Science
        - Min. 2 years experience in web development
        - Proficient in PHP and JavaScript
        
        Benefit:
        - Gaji kompetitif
        - BPJS Kesehatan & Ketenagakerjaan
        - Tunjangan transport & makan
        
        Apply via email: <EMAIL>
        Website: www.tech-indo.com/karir
        """
    },
    {
        "name": "No contact information",
        "text": """
        Entry Level Marketing Position
        
        Join our dynamic marketing team! We're looking for recent graduates who are passionate about digital marketing.
        
        What you'll do:
        - Create engaging content
        - Assist with campaign management
        - Analyze marketing metrics
        
        Requirements:
        - Bachelor's degree in Marketing or related field
        - Strong communication skills
        - Knowledge of social media platforms
        
        Great opportunity to start your career in a supportive environment with competitive benefits!
        """
    },
    {
        "name": "Contact info in paragraphs",
        "text": """
        Senior Project Manager Position
        
        We are expanding our team and looking for an experienced Project Manager to lead our client projects.
        
        The ideal candidate will have PMP certification and at least 5 years of experience managing complex projects.
        
        If you're interested in this role, please send your resume and cover letter to our hiring manager, Jane Smith, 
        at <EMAIL>. You can also visit our careers page at www.projectco.com/jobs for more information 
        about our company and culture. We look forward to hearing from qualified candidates!
        """
    },
    {
        "name": "Indonesian job post - contact within text",
        "text": """
        LOWONGAN PEKERJAAN - GURU BAHASA INGGRIS
        
        Sekolah Bahasa Inggris First Class membutuhkan guru untuk program kursus intensif. Minimal pendidikan S1 Pendidikan 
        Bahasa Inggris dengan pengalaman mengajar minimal 1 tahun. Untuk informasi lebih lanjut, silahkan mengirimkan CV 
        dan surat lamaran <NAME_EMAIL>. Kami juga menerima pertanyaan melalui WhatsApp 0811-2233-4455 atau 
        kunjungi website kami di www.firstclass-edu.id untuk informasi lebih detail.
        """
    },
    {
        "name": "Confusing formats and typos",
        "text": """
        Social Media Speciallist Needed
        
        Were looking for a social media speciallist to join our teem. Must have experiance with Instragram, Facebok, and TikTok.
        
        Reqirements:
        - 2 year experiance
        - Knowledge of conten creation
        - Good writing skill
        
        Send you're resume to: socialmedia @ company . com (remove spaces)
        Or check more detials at: www company com/careers (add dots)
        """
    },
    {
        "name": "Email in image description",
        "text": """
        Graphic Designer - Part Time
        
        We're looking for a part-time graphic designer to create social media assets and marketing materials.
        
        Skills needed:
        - Adobe Creative Suite
        - UI/UX understanding
        - Brand identity development
        
        To apply, send your portfolio to the email address shown in the image below:
        [Image: Contact <NAME_EMAIL>]
        
        Or visit our website to apply directly.
        """
    },
    {
        "name": "Only general company URL, no job-specific URL",
        "text": """
        Customer Support Representative
        
        Join our growing team at TechSupport Inc. We're looking for customer support representatives to help our 
        clients troubleshoot technical issues.
        
        Requirements:
        - Strong communication skills
        - Problem-solving abilities
        - Basic technical knowledge
        
        Email your <NAME_EMAIL>
        
        Learn more about our company at www.techsupport.com
        """
    },
    {
        "name": "LinkedIn profile without direct application link",
        "text": """
        Seeking Marketing Specialist
        
        A well-established marketing agency is seeking a marketing specialist with expertise in digital advertising.
        
        The ideal candidate will have:
        - 3+ years in digital marketing
        - Experience with Google Ads and Facebook Ads
        - Analytics background
        
        To learn more, connect with our recruiter on LinkedIn: linkedin.com/in/marketing-recruiter
        
        Serious candidates only.
        """
    },
    {
        "name": "Company social media without job link",
        "text": """
        UX/UI Designer - Junior Level
        
        Technology startup looking for a junior UX/UI designer for our mobile app team.
        
        Role involves:
        - Creating wireframes
        - Prototyping interfaces
        - User testing
        
        Send portfolio <NAME_EMAIL>
        
        Follow us on Twitter @startupmobile for company updates
        Check our Instagram: @startupmobile.design for team culture
        """
    },
    {
        "name": "General company website with careers mention",
        "text": """
        Software QA Engineer
        
        Major software company seeking QA Engineers for our development team.
        
        Skills needed:
        - Experience with automated testing
        - Knowledge of CI/CD pipelines
        - Bug tracking systems familiarity
        
        Competitive salary and excellent benefits package.
        
        Our company website is www.majorsoft.com where you can find more information about our open positions.
        """
    },
    {
        "name": "Job board reference without direct link",
        "text": """
        Remote Full Stack Developer
        
        We need a full stack developer to join our remote team. Must be comfortable with both frontend and backend development.
        
        Tech stack:
        - React
        - Node.js
        - MongoDB
        
        To apply, search for job ID #FSR-2025 on JobPortal.com or email your <NAME_EMAIL>
        """
    },
    {
        "name": "Indonesian job post - main website only",
        "text": """
        LOWONGAN ADMIN GUDANG
        
        PT Maju Jaya Sentosa membutuhkan admin gudang untuk penempatan di Jakarta Timur.
        
        Kualifikasi:
        - Pendidikan min. SMA/SMK
        - Menguasai Ms. Office
        - Teliti dan disiplin
        - Bisa bekerja dalam tekanan
        
        Untuk mengetahui informasi lebih lanjut, silahkan kunjungi website kami di www.majujayasentosa.co.id
        """
    },
    {
        "name": "Indonesian job post - social media contact only",
        "text": """
        DIBUTUHKAN TENAGA MARKETING
        
        Perusahaan properti di Surabaya membutuhkan tenaga marketing dengan pengalaman min. 1 tahun.
        
        Keuntungan:
        - Gaji pokok + Komisi tinggi
        - Bonus bulanan & tahunan
        - Training gratis
        
        Info lebih lanjut, follow Instagram kami @properti_surabaya atau hubungi kami melalui DM Instagram.
        """
    },
    {
        "name": "Indonesian job post - company profile link",
        "text": """
        LOKER: CUSTOMER SERVICE ONLINE
        
        Toko online fashion terkemuka di Indonesia membutuhkan customer service untuk shift pagi dan sore.
        
        Syarat:
        - Wanita, usia max 30 tahun
        - Komunikatif dan ramah
        - Bisa bekerja dalam tim
        
        Apply <NAME_EMAIL> atau kunjungi website fashiononline.id
        """
    },
    {
        "name": "Indonesian job post - marketplace account",
        "text": """
        DICARI DESAINER GRAFIS PART TIME
        
        Usaha clothing membutuhkan desainer grafis part time untuk pembuatan desain kaos dan merchandise.
        
        Persyaratan:
        - Menguasai Adobe Photoshop dan Illustrator
        - Memiliki portfolio desain
        - Domisili Bandung
        
        Kirim <NAME_EMAIL> atau cek toko kami di tokopedia.com/clothingkreatif
        """
    },
    {
        "name": "Indonesian job post - shortened links",
        "text": """
        LOWONGAN: DEVELOPER MOBILE APP
        
        Startup fintech sedang mencari mobile app developer untuk bergabung dengan tim kami.
        
        Kualifikasi:
        - Menguasai React Native
        - Pengalaman min. 1 tahun
        - Problem solver
        
        Untuk info lengkap dan cara apply, klik: bit.ly/lowongan-dev-2025
        """
    },
    {
        "name": "Indonesian job post - various phone number formats",
        "text": """
        DIBUTUHKAN SEGERA: ADMIN KANTOR
        
        PT Mitra Solusi mencari admin kantor untuk penempatan di Jakarta Selatan.
        
        Kualifikasi:
        - Lulusan min. D3 segala jurusan
        - Menguasai MS Office
        - Teliti dan bertanggung jawab
        
        Kirim <NAME_EMAIL> atau hubungi HRD di:
        - 0812-3456-7890
        - (021) 5553-2299
        - +62 877 1234 5678
        """
    },
    {
        "name": "Indonesian job post - multiple messaging platforms",
        "text": """
        LOWONGAN CONTENT CREATOR
        
        Brand fashion lokal membutuhkan content creator untuk social media marketing.
        
        Yang kami cari:
        - Kreatif dan up-to-date dengan tren
        - Menguasai editing foto dan video
        - Bisa membuat copywriting menarik
        
        Hubungi kami melalui:
        Telegram: @fashion_brand
        Line: @fashion_brand_id
        Discord: FashionBrand#1234
        """
    },
    {
        "name": "Indonesian job post - contact info in hashtags",
        "text": """
        KESEMPATAN MAGANG DIGITAL MARKETING
        
        Startup e-commerce membuka program magang untuk posisi digital marketing selama 3 bulan.
        
        Benefit:
        - Sertifikat magang
        - Uang transport
        - Kesempatan direkrut sebagai karyawan tetap
        
        #<EMAIL> #InfoLebihLanjut021555888 #DMInstagram@ecommerce_startup #ApplyViaWeb-ecommerce.co.id/career
        """
    },
    {
        "name": "Job post with embedded links in text",
        "text": """
        Software Engineer - Backend Development
        
        We're looking for a talented backend engineer to join our growing team.
        
        Requirements:
        - 3+ years experience with Node.js
        - Good understanding of databases
        - Experience with cloud services
        
        Interested? Apply by clicking [here](careers.techcompany.com/apply) or visit our [LinkedIn page](linkedin.com/company/techcompany) for more information.
        """
    },
    {
        "name": "Indonesian job post with regional platform references",
        "text": """
        LOWONGAN CUSTOMER RELATIONSHIP OFFICER
        
        Bank swasta terkemuka mencari kandidat untuk posisi Customer Relationship Officer.
        
        Persyaratan:
        - S1 segala jurusan, IPK min. 3.00
        - Berpenampilan menarik dan komunikatif
        - Berpengalaman di bidang perbankan (nilai plus)
        
        Hubungi kami di Kaskus: forum.kaskus.co.id/bankkarir
        Atau via Line: @bankkarir_id
        """
    },
    {
        "name": "Job post with contact info buried in long text",
        "text": """
        SENIOR ARCHITECT POSITION
        
        Our award-winning architectural firm is expanding and looking for senior architects to join our team. The ideal candidate will have extensive experience in sustainable design and urban planning. You will work closely with our international clients on high-profile projects throughout Southeast Asia.
        
        Key responsibilities include client consultation, project management, team leadership, and design review. We value innovation, attention to detail, and collaborative thinking. Our office provides a dynamic environment with opportunities for professional growth and development.
        
        All applicants must have at least 8 years of experience in architectural practice, proficiency in AutoCAD and Revit, excellent communication skills, and a portfolio demonstrating diverse project types. We offer competitive salary packages, health benefits, and relocation assistance if needed. Candidates who are passionate about sustainable architecture and have LEED certification will be given priority consideration. Please submit your portfolio and resume to our hiring <NAME_EMAIL> by the end of this month. For questions regarding this position, contact our HR department at +62-21-55566777 or visit our website www.architectfirm.co.id for more information about our firm's work and values.
        """
    },
    {
        "name": "Mixed language post with code-switching",
        "text": """
        URGENTLY NEEDED: BILINGUAL CUSTOMER SERVICE
        
        We are looking for Customer Service Representatives yang fasih berbahasa Inggris dan Indonesia untuk handle international clients.
        
        Requirements:
        - Fluent in English and Bahasa Indonesia
        - Min. 1 year experience in customer service role
        - Good problem-solving skills
        
        Benefits:
        - Competitive salary
        - Health insurance
        - Tunjangan transportasi dan makan
        
        Send your <NAME_EMAIL> and jangan lupa follow Instagram kami @global_service_id untuk update lowongan terbaru.
        """
    },
    {
        "name": "Job post with domain-specific extensions",
        "text": """
        IT SUPPORT SPECIALIST
        
        Perusahaan logistik di Jakarta Utara membutuhkan IT Support Specialist.
        
        Tanggung jawab:
        - Maintenance hardware dan software
        - Troubleshooting jaringan
        - Memberikan support kepada pengguna
        
        Kirim lamaran ke:
        - <EMAIL>
        - <EMAIL>
        - Atau kunjungi situs kami di www.logistik.co.id/karir
        """
    }
]

# Function to test all job posts with rate limit handling
def test_all_job_posts():
    """Test AI contact extraction on all defined job posts with rate limit handling."""
    success_count = 0
    total_count = len(test_cases)
    no_contact_count = 0
    
    print("Testing AI Contact Extractor with Real Job Postings")
    print("=" * 70 + "\n")
    
    # Keep track of retried cases
    retried_cases = set()
    
    # Process test cases with rate limit handling
    idx = 0
    while idx < len(test_cases):
        test_case = test_cases[idx]
        name = test_case["name"]
        text = test_case["text"]
        
        # Print test case header
        print(f"Test Case {idx+1}: {name}")
        print("-" * 70)
        
        # Display a preview of the job post (truncated if too long)
        preview = "\n".join(line for line in text.split("\n")[:10])
        if len(text.split("\n")) > 10:
            preview += "\n..."
        print(f"Job Post (preview): \n{preview}")
        
        try:
            # Extract contact information using AI
            emails, urls = extract_contact_info(text)
            
            # Print results
            print(f"Extracted Emails: {emails}")
            print(f"Extracted URLs: {urls}")
            
            # Check if any contact information was found
            if emails or urls:
                print("✓ Contacts found!")
                success_count += 1
            else:
                print("✗ No contacts found.")
                
                # Check if this is the test case explicitly for no contact info
                if "no contact information" in name.lower():
                    print("  (Expected result for this test case)")
                    no_contact_count += 1
            
            # Add a delay between API calls to avoid rate limiting
            # Use a random delay between 2-3 seconds for regular requests
            delay = random.uniform(2, 3)
            time.sleep(delay)
            
            # Increment the index to move to the next test case
            idx += 1
            
        except Exception as e:
            error_msg = str(e)
            print(f"Error: {error_msg}")
            
            # Check if it's a rate limit error (429)
            if "429" in error_msg and "quota" in error_msg:
                # Get the retry delay from the error message if available
                import re
                retry_seconds = 60  # Default retry delay
                
                # Try to extract the retry delay from the error message
                match = re.search(r'retry_delay\s*{\s*seconds:\s*(\d+)\s*}', error_msg)
                if match:
                    retry_seconds = int(match.group(1))
                
                # Add some buffer time
                retry_seconds += 5
                
                # Log the rate limit and wait time
                print(f"Rate limit exceeded. Waiting for {retry_seconds} seconds before retrying...")
                
                # Track retried cases to avoid infinite loops
                case_id = f"{idx}:{name}"
                if case_id in retried_cases:
                    # If we've already retried this case, skip it to avoid being stuck
                    print("Skipping this test case after retry attempt to avoid being stuck.")
                    idx += 1
                else:
                    retried_cases.add(case_id)
                    # Wait for the specified time before retrying
                    time.sleep(retry_seconds)
                    # Don't increment idx so we retry this test case
                    
            else:
                # For non-rate-limit errors, log and continue to the next test case
                print("Continuing to the next test case...")
                idx += 1
                
                # Add a smaller delay for error cases
                time.sleep(1)
        
        print()
    
    # Print summary
    print("=" * 70)
    print(f"Summary: Found contacts in {success_count}/{total_count} job postings")
    print(f"Success rate: {success_count/total_count:.0%}")
    
    # Calculate adjusted success rate (excluding the no-contact test case)
    if no_contact_count > 0:
        adjusted_success_rate = success_count / (total_count - no_contact_count)
        print(f"Adjusted success rate (excluding 'No contact information' case): {adjusted_success_rate:.0%}")

# Run the tests if this script is executed directly
if __name__ == "__main__":
    test_all_job_posts()
