import asyncio
import json
import logging
from job_scraper import JobScraper

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

async def main():
    # Create an instance of the JobScraper
    scraper = JobScraper()
    
    # Scrape jobs from Glints
    print("Scraping jobs from Glints...")
    glints_jobs = await scraper.scrape_glints()
    
    # Print the number of jobs found
    print(f"Found {len(glints_jobs)} jobs on Glints")
    
    # Save the results to a JSON file
    output_file = "glints_jobs.json"
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(glints_jobs, f, ensure_ascii=False, indent=2)
    
    print(f"Results saved to {output_file}")
    
    # Print the first 5 jobs as a preview
    if glints_jobs:
        print("\nPreview of the first 5 jobs:")
        for i, job in enumerate(glints_jobs[:5]):
            print(f"\nJob {i+1}:")
            for key, value in job.items():
                print(f"  {key}: {value}")

if __name__ == "__main__":
    asyncio.run(main())
