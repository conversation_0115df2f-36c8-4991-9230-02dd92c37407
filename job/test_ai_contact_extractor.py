"""
Unit tests for the AI-based contact information extractor.
"""

import unittest
from unittest.mock import patch, MagicMock
import json
import sys
import os
import logging

# Add parent directory to path to allow imports from parent directory
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from job.ai_contact_extractor import extract_contact_info, configure_genai

# Disable logging during tests
logging.disable(logging.CRITICAL)

class MockGeminiResponse:
    """Mock response from Gemini API"""
    def __init__(self, emails=None, urls=None, error=False, empty=False):
        self.emails = emails or []
        self.urls = urls or []
        self.error = error
        self.empty = empty
        
        if not error and not empty:
            # Create a JSON response
            response_json = {
                "emails": self.emails,
                "urls": self.urls
            }
            self.text = json.dumps(response_json)
        elif empty:
            # Empty response
            self.text = "{}"
        else:
            # Error response - not valid JSON
            self.text = "Invalid response"


class TestAIContactExtractor(unittest.TestCase):
    """Test the AI-based contact extractor"""
    
    @patch('job.ai_contact_extractor.configure_genai')
    @patch('google.generativeai.GenerativeModel')
    def test_successful_extraction(self, mock_model_class, mock_configure):
        """Test successful extraction of contacts using AI"""
        # Configure mocks
        mock_configure.return_value = True
        mock_model = MagicMock()
        mock_model_class.return_value = mock_model
        
        # Set up test data
        test_emails = ["<EMAIL>", "<EMAIL>"]
        test_urls = ["https://careers.example.com", "https://company.com/apply"]
        mock_response = MockGeminiResponse(emails=test_emails, urls=test_urls)
        
        # Configure the mock model to return our test response
        mock_model.generate_content.return_value = mock_response
        
        # Test the extractor
        text = "Apply for this <NAME_EMAIL> or <EMAIL>. Visit our careers page at careers.example.com or company.com/apply"
        emails, urls = extract_contact_info(text)
        
        # Verify results
        self.assertEqual(emails, test_emails)
        self.assertEqual(urls, test_urls)
        mock_model.generate_content.assert_called_once()

    @patch('job.ai_contact_extractor.configure_genai')
    def test_empty_results_when_api_key_missing(self, mock_configure):
        """Test empty results when API key is missing"""
        # Configure mocks
        mock_configure.return_value = False
        
        # Test the extractor
        text = "Some text with contact info"
        emails, urls = extract_contact_info(text)
        
        # Verify results
        self.assertEqual(emails, [])
        self.assertEqual(urls, [])

    @patch('job.ai_contact_extractor.configure_genai')
    @patch('google.generativeai.GenerativeModel')
    def test_empty_results_when_ai_fails(self, mock_model_class, mock_configure):
        """Test empty results when AI extraction fails"""
        # Configure mocks
        mock_configure.return_value = True
        mock_model = MagicMock()
        mock_model_class.return_value = mock_model
        
        # Configure model to raise an exception
        mock_model.generate_content.side_effect = Exception("API error")
        
        # Test the extractor
        text = "Some text with contact info"
        emails, urls = extract_contact_info(text)
        
        # Verify results
        self.assertEqual(emails, [])
        self.assertEqual(urls, [])

    @patch('job.ai_contact_extractor.configure_genai')
    @patch('google.generativeai.GenerativeModel')
    def test_empty_results_when_invalid_response(self, mock_model_class, mock_configure):
        """Test empty results when AI returns invalid response format"""
        # Configure mocks
        mock_configure.return_value = True
        mock_model = MagicMock()
        mock_model_class.return_value = mock_model
        
        # Configure model to return an invalid response
        mock_model.generate_content.return_value = MockGeminiResponse(error=True)
        
        # Test the extractor
        text = "Some text with contact info"
        emails, urls = extract_contact_info(text)
        
        # Verify results
        self.assertEqual(emails, [])
        self.assertEqual(urls, [])

    @patch('job.ai_contact_extractor.configure_genai')
    @patch('google.generativeai.GenerativeModel')
    def test_empty_results_when_empty_extraction(self, mock_model_class, mock_configure):
        """Test empty results when AI extracts no contacts"""
        # Configure mocks
        mock_configure.return_value = True
        mock_model = MagicMock()
        mock_model_class.return_value = mock_model
        
        # Configure model to return empty lists
        mock_model.generate_content.return_value = MockGeminiResponse(emails=[], urls=[])
        
        # Test the extractor
        text = "Some text with contact info"
        emails, urls = extract_contact_info(text)
        
        # Verify results
        self.assertEqual(emails, [])
        self.assertEqual(urls, [])

    def test_empty_input(self):
        """Test behavior with empty input"""
        emails, urls = extract_contact_info("")
        self.assertEqual(emails, [])
        self.assertEqual(urls, [])

    @patch('job.ai_contact_extractor.configure_genai')
    @patch('google.generativeai.GenerativeModel')
    def test_real_world_job_posting(self, mock_model_class, mock_configure):
        """Test extraction on a real-world job posting example"""
        # Configure mocks
        mock_configure.return_value = True
        mock_model = MagicMock()
        mock_model_class.return_value = mock_model
        
        # Example response
        test_emails = ["<EMAIL>"]
        test_urls = ["https://techfirm.com/jobs/software-engineer"]
        mock_response = MockGeminiResponse(emails=test_emails, urls=test_urls)
        mock_model.generate_content.return_value = mock_response
        
        # Real-world job posting text
        job_text = """
        Senior Software Engineer - Remote
        
        TechFirm is looking for a Senior Software Engineer to join our growing team!
        
        Requirements:
        - 5+ years of experience with Python
        - Strong knowledge of web frameworks
        - Experience with cloud platforms
        
        Benefits:
        - Competitive salary
        - Remote work option
        - Health insurance
        
        To apply, send your <NAME_EMAIL> or visit our careers page at 
        techfirm.com/jobs/software-engineer
        """
        
        emails, urls = extract_contact_info(job_text)
        
        # Verify results
        self.assertEqual(emails, test_emails)
        self.assertEqual(urls, test_urls)


if __name__ == "__main__":
    unittest.main()
