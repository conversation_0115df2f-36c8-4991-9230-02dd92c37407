"""
Test WhatsApp number extraction specifically.
"""

from ai_contact_extractor import extract_contact_info
import re

# Function to normalize WhatsApp URLs for comparison
def normalize_whatsapp_urls(urls):
    normalized = []
    whatsapp_pattern = re.compile(r'https://wa\.me/(\d+)')
    
    for url in urls:
        match = whatsapp_pattern.match(url)
        if match:
            # Get the number part
            number = match.group(1)
            
            # Normalize it - ensure it starts with 62 for Indonesian numbers
            if number.startswith('0'):
                normalized_number = '62' + number[1:]
            else:
                normalized_number = number
                
            # Create normalized URL
            normalized_url = f"https://wa.me/{normalized_number}"
            
            # Only add if not a duplicate
            if normalized_url not in normalized:
                normalized.append(normalized_url)
        else:
            # Not a WhatsApp URL, add as is
            normalized.append(url)
    
    return normalized

# Test case with WhatsApp numbers
test_case = """
Dibuka Lowongan Staf Accounting

Kualifikasi:
- Lulusan D3/S1 Akuntansi
- Menguasai program akuntansi dan pajak
- Teliti dan bertanggung jawab
- Berpengalaman min. 1 tahun

Lamaran <NAME_EMAIL>
Info lebih lanjut hubungi WA: 0812-3456-7890
Website: pt-sukses.com
"""

# Test case with various Indonesian phone number formats
test_case2 = """
DIBUTUHKAN SEGERA: ADMIN KANTOR

PT Mitra Solusi mencari admin kantor untuk penempatan di Jakarta Selatan.

Kualifikasi:
- Lulusan min. D3 segala jurusan
- Menguasai MS Office
- Teliti dan bertanggung jawab

Kirim <NAME_EMAIL> atau hubungi HRD di:
- 0812-3456-7890
- (021) 5553-2299
- +62 877 1234 5678
- WhatsApp: ***********
"""

# Test case with WhatsApp as the ONLY contact method
test_case3 = """
LOKER ADMIN GUDANG JAKARTA UTARA

Perusahaan distributor membutuhkan admin gudang untuk area Jakarta Utara.
Tanggung jawab: pencatatan barang masuk/keluar dan kontrol stok.

Persyaratan:
- Minimal SMK/SMA
- Teliti dan disiplin
- Mampu mengoperasikan komputer
- Domisili Jakarta Utara

Hubungi kami hanya lewat WhatsApp: 0878-7654-3210
"""

# Test case with multiple WhatsApp numbers but in different formats
test_case4 = """
DRIVER PRIBADI / SUPIR KELUARGA

Dibutuhkan driver pribadi untuk keluarga di area BSD Tangerang.
Syarat:
- Pria, usia 25-40 tahun
- SIM A aktif
- Pengalaman min. 2 tahun
- Mengenal area Jakarta & Tangerang

Kontak:
WhatsApp: 08111222333
WA Cadangan: +62 811-1222-333
Info: wa.me/62811122233
"""

# Test case with WhatsApp link in URL format
test_case5 = """
SALES MARKETING PROPERTI

Developer properti di Bandung membuka lowongan untuk Sales Marketing.
Benefit:
- Gaji pokok + komisi tinggi
- BPJS
- Training product knowledge

Persyaratan:
- Min. SMA/SMK, max. 35 thn
- Komunikatif & berpenampilan menarik
- Punya kendaraan sendiri

Apply: https://wa.me/6282233445566 (Marketing Manager)
"""

# Test case with WhatsApp in Indonesian instruction patterns
test_case6 = """
DIBUTUHKAN SEGERA!
Karyawan untuk posisi KASIR dan PRAMUNIAGA di minimarket area Depok.

Syarat:
- Pria/Wanita usia 18-25 tahun
- Pendidikan min. SMA/SMK
- Berpenampilan rapi dan menarik
- Bersedia kerja shift

Cara Apply:
Kirim data diri (Nama, Usia, Alamat) ke:
Ketik: LOKER_(Nama)_(Domisili) kirim ke WA: 0898-7654-321
"""

# Test case with non-standard WhatsApp notation
test_case7 = """
URGENTLY NEEDED: CONTENT CREATOR
Studio fotografi di Yogyakarta mencari content creator untuk social media.

Kualifikasi:
- Menguasai editing foto & video
- Kreatif & update tren sosmed
- Bisa bekerja dalam tim

Lamaran:
Kirim CV dan portofolio ke
WA: wa.me/628765432109 (tulis "CREATOR" di awal pesan)
atau hubungi langsung: wa ***********
"""

# Test case with WhatsApp mixed with other messaging apps
test_case8 = """
TEKNISI KOMPUTER & JARINGAN

Dibutuhkan teknisi komputer & jaringan untuk service center di Surabaya.
- Pengalaman min. 1 tahun
- Mahir troubleshooting hardware/software
- Mengerti jaringan LAN/WAN

Info lowongan:
- Telepon: 031-5556666
- WhatsApp: 081-3579-2468
- Line: @techservice
- Telegram: t.me/techservice
"""

# Test case with WhatsApp Business and international numbers
test_case9 = """
LOWONGAN: CUSTOMER SERVICE INTERNATIONAL

Perusahaan e-commerce dengan customer global membutuhkan customer service.
Requirements:
- Fluent in English & Bahasa
- Available for rotating shifts
- Min. 1 year experience in e-commerce

Contact:
- WhatsApp Business: wa.me/628111000999
- International Support: +65 8765 4321 (WhatsApp available)
- HR WhatsApp: https://api.whatsapp.com/send?phone=628987654321
"""

# Test case with various WhatsApp URLs in different formats
test_case10 = """
GRAPHIC DESIGNER DIBUTUHKAN SEGERA!

Studio desain & percetakan di Jakarta Selatan membuka lowongan.
Syarat:
- Menguasai Adobe Photoshop, Illustrator, InDesign
- Portfolio menarik
- Bisa bekerja dalam deadline ketat

Kirim portfolio melalui:
- WA: https://wa.me/message/ABCDEFGHIJKLMN1
- WhatsApp Click to Chat: wa.me/6287654321098
- WhatsApp Business: business.whatsapp.com/STUDIOJAKARTASELATAN
"""

# Test case with international WhatsApp numbers using + format
test_case11 = """
INTERNATIONAL SALES REPRESENTATIVE

Global export company based in Jakarta is looking for Sales Representative.
Requirements:
- Bachelor's degree in any field
- Excellent in both English and Bahasa
- Willing to travel abroad

Please contact via WhatsApp:
- Indonesia: +62 812-9876-5432
- Singapore office: +65-9876-5432
- Malaysia branch: +60 12-345 6789
"""

# Test case with both + and 00 international format
test_case12 = """
OVERSEAS JOB OPPORTUNITY - CRUISE SHIP STAFF

International cruise line hiring various positions:
- F&B Service
- Housekeeping
- Entertainment

Requirements:
- Min. high school diploma
- English communication skills
- Valid passport

Apply through:
- WhatsApp: +62 878 7878 7878
- Alternative contact: 00628787878787
- European office: +33 6 12 34 56 78
"""

# Test case with + sign in the URL itself
test_case13 = """
URGENTLY NEEDED: MARKETING EXECUTIVE

Local company in Jakarta is seeking a Marketing Executive.
Key responsibilities:
- Develop marketing strategies
- Execute promotional campaigns
- Analyze market trends

Qualifications:
- Bachelor's degree in Marketing or related field
- 2+ years experience in similar role
- Excellent communication skills

Send CV directly to:
- WhatsApp: https://wa.me/+6281234567890
- WhatsApp Business: wa.me/+6287654321098
- Click here: https://api.whatsapp.com/send?phone=+628111222333
"""

# Test only
if __name__ == "__main__":
    print("Testing WhatsApp number extraction...")
    
    print("\nTEST CASE 1: Standard WhatsApp mention")
    emails, urls = extract_contact_info(test_case)
    print(f"Extracted Emails: {emails}")
    print(f"Extracted URLs (raw): {urls}")
    print(f"Normalized WhatsApp URLs: {normalize_whatsapp_urls(urls)}")
    
    print("\nTEST CASE 2: Multiple phone formats")
    emails, urls = extract_contact_info(test_case2)
    print(f"Extracted Emails: {emails}")
    print(f"Extracted URLs (raw): {urls}")
    print(f"Normalized WhatsApp URLs: {normalize_whatsapp_urls(urls)}")
    
    print("\nTEST CASE 3: WhatsApp as ONLY contact method")
    emails, urls = extract_contact_info(test_case3)
    print(f"Extracted Emails: {emails}")
    print(f"Extracted URLs (raw): {urls}")
    print(f"Normalized WhatsApp URLs: {normalize_whatsapp_urls(urls)}")
    
    print("\nTEST CASE 4: Multiple WhatsApp numbers in different formats")
    emails, urls = extract_contact_info(test_case4)
    print(f"Extracted Emails: {emails}")
    print(f"Extracted URLs (raw): {urls}")
    print(f"Normalized WhatsApp URLs: {normalize_whatsapp_urls(urls)}")
    
    print("\nTEST CASE 5: WhatsApp link in URL format")
    emails, urls = extract_contact_info(test_case5)
    print(f"Extracted Emails: {emails}")
    print(f"Extracted URLs (raw): {urls}")
    print(f"Normalized WhatsApp URLs: {normalize_whatsapp_urls(urls)}")
    
    print("\nTEST CASE 6: Indonesian instruction patterns")
    emails, urls = extract_contact_info(test_case6)
    print(f"Extracted Emails: {emails}")
    print(f"Extracted URLs (raw): {urls}")
    print(f"Normalized WhatsApp URLs: {normalize_whatsapp_urls(urls)}")
    
    print("\nTEST CASE 7: Non-standard WhatsApp notation")
    emails, urls = extract_contact_info(test_case7)
    print(f"Extracted Emails: {emails}")
    print(f"Extracted URLs (raw): {urls}")
    print(f"Normalized WhatsApp URLs: {normalize_whatsapp_urls(urls)}")
    
    print("\nTEST CASE 8: WhatsApp mixed with other messaging apps")
    emails, urls = extract_contact_info(test_case8)
    print(f"Extracted Emails: {emails}")
    print(f"Extracted URLs (raw): {urls}")
    print(f"Normalized WhatsApp URLs: {normalize_whatsapp_urls(urls)}")
    
    print("\nTEST CASE 9: WhatsApp Business and international numbers")
    emails, urls = extract_contact_info(test_case9)
    print(f"Extracted Emails: {emails}")
    print(f"Extracted URLs (raw): {urls}")
    print(f"Normalized WhatsApp URLs: {normalize_whatsapp_urls(urls)}")
    
    print("\nTEST CASE 10: Various WhatsApp URLs in different formats")
    emails, urls = extract_contact_info(test_case10)
    print(f"Extracted Emails: {emails}")
    print(f"Extracted URLs (raw): {urls}")
    print(f"Normalized WhatsApp URLs: {normalize_whatsapp_urls(urls)}")
    
    print("\nTEST CASE 11: International WhatsApp numbers using + format")
    emails, urls = extract_contact_info(test_case11)
    print(f"Extracted Emails: {emails}")
    print(f"Extracted URLs (raw): {urls}")
    print(f"Normalized WhatsApp URLs: {normalize_whatsapp_urls(urls)}")
    
    print("\nTEST CASE 12: Both + and 00 international format")
    emails, urls = extract_contact_info(test_case12)
    print(f"Extracted Emails: {emails}")
    print(f"Extracted URLs (raw): {urls}")
    print(f"Normalized WhatsApp URLs: {normalize_whatsapp_urls(urls)}")
    
    print("\nTEST CASE 13: + sign in the URL itself")
    emails, urls = extract_contact_info(test_case13)
    print(f"Extracted Emails: {emails}")
    print(f"Extracted URLs (raw): {urls}")
    print(f"Normalized WhatsApp URLs: {normalize_whatsapp_urls(urls)}")
