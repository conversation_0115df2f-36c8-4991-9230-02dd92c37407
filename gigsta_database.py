import sqlite3
import logging
from typing import List, Dict, Optional
import os
from config import get_database_file, ENV, get_mysql_config

# Conditional import for MySQL
if os.environ.get('DB_BACKEND', 'sqlite').lower() == 'mysql':
    import mysql.connector

# Configure logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

# Use environment-specific database file
DATABASE_FILE = get_database_file()
DB_BACKEND = os.environ.get('DB_BACKEND', 'sqlite').lower()
logger.info(f"Using database backend: {DB_BACKEND}")
if DB_BACKEND == 'sqlite':
    logger.info(f"Using database file: {DATABASE_FILE} in {ENV} environment")
else:
    logger.info(f"Using MySQL connection for environment: {ENV}")

def get_mysql_connection():
    cfg = get_mysql_config()
    return mysql.connector.connect(
        host=cfg['host'],
        user=cfg['user'],
        password=cfg['password'],
        database=cfg['database'],
        port=cfg['port']
    )

def get_db_connection():
    if DB_BACKEND == 'mysql':
        return get_mysql_connection()
    else:
        return sqlite3.connect(DATABASE_FILE)

class JobDatabase:
    rollbar = None  # Class-level Rollbar service instance

    @classmethod
    def set_rollbar(cls, rollbar_service):
        """Set the Rollbar service instance for error tracking."""
        cls.rollbar = rollbar_service
        
    @staticmethod
    def setup_database():
        """Initialize the database with required tables if they don't exist."""
        try:
            conn = get_db_connection()
            cursor = conn.cursor()

            if DB_BACKEND == 'mysql':
                # MySQL syntax adjustments
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS subscriptions (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        user_id VARCHAR(255) NOT NULL,
                        keywords TEXT NOT NULL,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
                ''')
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS followings (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        user_id VARCHAR(255) NOT NULL,
                        keywords TEXT NOT NULL,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
                ''')
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS scraped_jobs (
                        url VARCHAR(512) PRIMARY KEY,
                        title VARCHAR(255) NOT NULL,
                        company VARCHAR(255) NOT NULL,
                        location VARCHAR(255),
                        source VARCHAR(255) NOT NULL,
                        description TEXT,
                        message_id VARCHAR(255),
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
                ''')
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS jobs (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        user_id VARCHAR(255) NOT NULL,
                        title VARCHAR(255) NOT NULL,
                        description TEXT NOT NULL,
                        poster_file_id VARCHAR(255),
                        contact VARCHAR(255) NOT NULL,
                        is_email BOOLEAN NOT NULL,
                        message_id VARCHAR(255),
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
                ''')
            else:
                # SQLite original
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS subscriptions (
                        id INTEGER PRIMARY KEY,
                        user_id TEXT NOT NULL,
                        keywords TEXT NOT NULL,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS followings (
                        id INTEGER PRIMARY KEY,
                        user_id TEXT NOT NULL,
                        keywords TEXT NOT NULL,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS scraped_jobs (
                        url TEXT PRIMARY KEY,
                        title TEXT NOT NULL,
                        company TEXT NOT NULL,
                        location TEXT,
                        source TEXT NOT NULL,
                        description TEXT,
                        message_id TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS jobs (
                        id INTEGER PRIMARY KEY,
                        user_id TEXT NOT NULL,
                        title TEXT NOT NULL,
                        description TEXT NOT NULL,
                        poster_file_id TEXT,
                        contact TEXT NOT NULL,
                        is_email BOOLEAN NOT NULL,
                        message_id TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')

            conn.commit()
            logger.info("Database setup completed successfully")
        except Exception as e:
            logger.error(f"Database setup error: {e}")
            if JobDatabase.rollbar:
                JobDatabase.rollbar.capture_exception(
                    exc_info=(type(e), e, e.__traceback__),
                    extra_data={'method': 'setup_database'}
                )
        finally:
            if conn:
                conn.close()
    
    @staticmethod
    def _get_placeholder():
        return '%s' if DB_BACKEND == 'mysql' else '?'

    @staticmethod
    def _dict_cursor(conn):
        if DB_BACKEND == 'mysql':
            return conn.cursor(dictionary=True)
        else:
            conn.row_factory = sqlite3.Row
            return conn.cursor()

    @staticmethod
    def load_subscriptions() -> Dict[str, List[str]]:
        """Load all followed keywords from the database and format them as a dictionary."""
        return JobDatabase.load_followings()
        
    @staticmethod
    def load_followings() -> Dict[str, List[str]]:
        """Load all followed keywords from the followings table and format them as a dictionary."""
        followings = {}
        try:
            conn = get_db_connection()
            cursor = JobDatabase._dict_cursor(conn)
            cursor.execute("SELECT user_id, keywords FROM followings")
            rows = cursor.fetchall()
            for row in rows:
                user_id = row['user_id'] if isinstance(row, dict) else row[0]
                keywords = row['keywords'] if isinstance(row, dict) else row[1]
                if user_id not in followings:
                    followings[user_id] = []
                followings[user_id].append(keywords)
            logger.info(f"Loaded {len(followings)} user followed keywords from database")
        except Exception as e:
            logger.error(f"Error loading followings: {e}")
            if JobDatabase.rollbar:
                JobDatabase.rollbar.capture_exception(
                    exc_info=(type(e), e, e.__traceback__),
                    extra_data={'method': 'load_followings'}
                )
        finally:
            if conn:
                conn.close()
        
        return followings
    
    @staticmethod
    def save_subscription(user_id: str, keywords: str) -> bool:
        """Save a new followed keyword to the database."""
        return JobDatabase.save_following(user_id, keywords)
    
    @staticmethod
    def save_following(user_id: str, keywords: str) -> bool:
        """Save a new followed keyword to the followings table."""
        try:
            conn = get_db_connection()
            cursor = JobDatabase._dict_cursor(conn)
            ph = JobDatabase._get_placeholder()
            cursor.execute(
                f"INSERT INTO followings (user_id, keywords) VALUES ({ph}, {ph})",
                (user_id, keywords)
            )
            conn.commit()
            logger.info(f"Saved followed keyword for user {user_id}: {keywords}")
            return True
        except Exception as e:
            logger.error(f"Error saving following: {e}")
            if JobDatabase.rollbar:
                JobDatabase.rollbar.capture_exception(
                    exc_info=(type(e), e, e.__traceback__),
                    extra_data={'method': 'save_following', 'user_id': user_id, 'keywords': keywords}
                )
            return False
        finally:
            if conn:
                conn.close()
    
    @staticmethod
    def delete_subscription(user_id: str, keywords: str) -> bool:
        """Delete a followed keyword from the database."""
        return JobDatabase.delete_following(user_id, keywords)
    
    @staticmethod
    def delete_following(user_id: str, keywords: str) -> bool:
        """Delete a followed keyword from the followings table."""
        try:
            conn = get_db_connection()
            cursor = JobDatabase._dict_cursor(conn)
            ph = JobDatabase._get_placeholder()
            cursor.execute(
                f"DELETE FROM followings WHERE user_id = {ph} AND keywords = {ph}",
                (user_id, keywords)
            )
            conn.commit()
            if cursor.rowcount > 0:
                logger.info(f"Deleted followed keyword for user {user_id}: {keywords}")
                return True
            else:
                logger.warning(f"No followed keyword found for user {user_id}: {keywords}")
                return False
        except Exception as e:
            logger.error(f"Error deleting following: {e}")
            if JobDatabase.rollbar:
                JobDatabase.rollbar.capture_exception(
                    exc_info=(type(e), e, e.__traceback__),
                    extra_data={'method': 'delete_following', 'user_id': user_id, 'keywords': keywords}
                )
            return False
        finally:
            if conn:
                conn.close()
    
    @staticmethod
    def get_user_subscriptions(user_id: str) -> List[str]:
        """Get all followed keywords for a specific user."""
        return JobDatabase.get_user_followings(user_id)
    
    @staticmethod
    def get_user_followings(user_id: str) -> List[str]:
        """Get all followed keywords for a specific user from followings table."""
        followings = []
        try:
            conn = get_db_connection()
            cursor = JobDatabase._dict_cursor(conn)
            ph = JobDatabase._get_placeholder()
            cursor.execute(f"SELECT keywords FROM followings WHERE user_id = {ph}", (user_id,))
            rows = cursor.fetchall()
            followings = [row['keywords'] if isinstance(row, dict) else row[0] for row in rows]
            logger.info(f"Retrieved {len(followings)} followed keywords for user {user_id}")
        except Exception as e:
            logger.error(f"Error getting user followings: {e}")
            if JobDatabase.rollbar:
                JobDatabase.rollbar.capture_exception(
                    exc_info=(type(e), e, e.__traceback__),
                    extra_data={'method': 'get_user_followings', 'user_id': user_id}
                )
        finally:
            if conn:
                conn.close()
        
        return followings
    
    @staticmethod
    def save_job(job: Dict) -> bool:
        """Save a scraped job to the database, using URL as the primary key."""
        try:
            conn = get_db_connection()
            cursor = JobDatabase._dict_cursor(conn)
            ph = JobDatabase._get_placeholder()
            if DB_BACKEND == 'mysql':
                sql = f"INSERT IGNORE INTO scraped_jobs (url, title, company, location, source, description, message_id) VALUES ({ph}, {ph}, {ph}, {ph}, {ph}, {ph}, {ph})"
            else:
                sql = f"INSERT OR IGNORE INTO scraped_jobs (url, title, company, location, source, description, message_id) VALUES ({ph}, {ph}, {ph}, {ph}, {ph}, {ph}, {ph})"
            cursor.execute(
                sql,
                (
                    job['url'],
                    job['title'],
                    job['company'],
                    job.get('location', ''),
                    job['source'],
                    job.get('description', ''),
                    job.get('message_id', '')
                )
            )
            conn.commit()
            is_new = cursor.rowcount > 0
            logger.info(f"Job {'saved' if is_new else 'already exists'}: {job['title']} from {job['source']}")
            return is_new
        except Exception as e:
            logger.error(f"Error saving job: {e}")
            if JobDatabase.rollbar:
                JobDatabase.rollbar.capture_exception(
                    exc_info=(type(e), e, e.__traceback__),
                    extra_data={'method': 'save_job', 'job': job}
                )
            return False
        finally:
            if conn:
                conn.close()
    
    @staticmethod
    def save_jobs(jobs: List[Dict]) -> List[Dict]:
        """
        Save multiple scraped jobs to the database and return only the new jobs that didn't exist before.
        This is more efficient than calling save_job multiple times.
        """
        new_jobs = []
        try:
            conn = get_db_connection()
            cursor = JobDatabase._dict_cursor(conn)
            ph = JobDatabase._get_placeholder()
            if DB_BACKEND == 'mysql':
                sql = f"INSERT IGNORE INTO scraped_jobs (url, title, company, location, source, description, message_id) VALUES ({ph}, {ph}, {ph}, {ph}, {ph}, {ph}, {ph})"
            else:
                sql = f"INSERT OR IGNORE INTO scraped_jobs (url, title, company, location, source, description, message_id) VALUES ({ph}, {ph}, {ph}, {ph}, {ph}, {ph}, {ph})"
            for job in jobs:
                cursor.execute(
                    sql,
                    (
                        job['url'],
                        job['title'],
                        job['company'],
                        job.get('location', ''),
                        job['source'],
                        job.get('description', ''),
                        job.get('message_id', '')
                    )
                )
                if cursor.rowcount > 0:
                    new_jobs.append(job)
            conn.commit()
            logger.info(f"Saved {len(new_jobs)} new jobs out of {len(jobs)} total jobs")
        except Exception as e:
            logger.error(f"Error saving jobs: {e}")
            if JobDatabase.rollbar:
                JobDatabase.rollbar.capture_exception(
                    exc_info=(type(e), e, e.__traceback__),
                    extra_data={'method': 'save_jobs', 'jobs_count': len(jobs)}
                )
        finally:
            if conn:
                conn.close()
        
        return new_jobs
    
    @staticmethod
    def is_job_exists(url: str) -> bool:
        """Check if a scraped job with the given URL already exists in the database."""
        try:
            conn = get_db_connection()
            cursor = JobDatabase._dict_cursor(conn)
            ph = JobDatabase._get_placeholder()
            cursor.execute(f"SELECT 1 FROM scraped_jobs WHERE url = {ph} LIMIT 1", (url,))
            exists = cursor.fetchone() is not None
            return exists
        except Exception as e:
            logger.error(f"Error checking job existence: {e}")
            if JobDatabase.rollbar:
                JobDatabase.rollbar.capture_exception(
                    exc_info=(type(e), e, e.__traceback__),
                    extra_data={'method': 'is_job_exists', 'url': url}
                )
            return False
        finally:
            if conn:
                conn.close()
                
    @staticmethod
    def update_scraped_job_message_id(url: str, message_id: str) -> bool:
        """Update the message ID for a scraped job."""
        try:
            conn = get_db_connection()
            cursor = JobDatabase._dict_cursor(conn)
            ph = JobDatabase._get_placeholder()
            cursor.execute(
                f"UPDATE scraped_jobs SET message_id = {ph} WHERE url = {ph}",
                (message_id, url)
            )
            conn.commit()
            success = cursor.rowcount > 0
            if success:
                logger.info(f"Updated message ID for scraped job with URL {url}")
            else:
                logger.warning(f"Failed to update message ID for scraped job with URL {url}")
            return success
        except Exception as e:
            logger.error(f"Error updating scraped job message ID: {e}")
            if JobDatabase.rollbar:
                JobDatabase.rollbar.capture_exception(
                    exc_info=(type(e), e, e.__traceback__),
                    extra_data={'method': 'update_scraped_job_message_id', 'url': url, 'message_id': message_id}
                )
            return False
        finally:
            if conn:
                conn.close()
    
    @staticmethod
    def get_all_scraped_jobs() -> List[Dict]:
        """Get all scraped jobs from the database."""
        jobs = []
        try:
            conn = get_db_connection()
            cursor = JobDatabase._dict_cursor(conn)
            cursor.execute("SELECT * FROM scraped_jobs ORDER BY created_at DESC")
            rows = cursor.fetchall()
            for row in rows:
                jobs.append({
                    'url': row['url'] if isinstance(row, dict) else row[0],
                    'title': row['title'] if isinstance(row, dict) else row[1],
                    'company': row['company'] if isinstance(row, dict) else row[2],
                    'location': row['location'] if isinstance(row, dict) else row[3],
                    'source': row['source'] if isinstance(row, dict) else row[4],
                    'description': row['description'] if isinstance(row, dict) else row[5],
                    'message_id': row['message_id'] if isinstance(row, dict) else row[6],
                    'created_at': row['created_at'] if isinstance(row, dict) else row[7]
                })
            logger.info(f"Retrieved {len(jobs)} scraped jobs from database")
        except Exception as e:
            logger.error(f"Error getting scraped jobs: {e}")
            if JobDatabase.rollbar:
                JobDatabase.rollbar.capture_exception(
                    exc_info=(type(e), e, e.__traceback__),
                    extra_data={'method': 'get_all_scraped_jobs'}
                )
        finally:
            if conn:
                conn.close()
        
        return jobs
    
    @staticmethod
    def post_job(user_id: str, title: str, description: str, 
                poster_file_id: Optional[str], contact: str, is_email: bool) -> Optional[int]:
        """Post a new job and return the job ID if successful."""
        try:
            conn = get_db_connection()
            cursor = JobDatabase._dict_cursor(conn)
            ph = JobDatabase._get_placeholder()
            cursor.execute(
                f"INSERT INTO jobs (user_id, title, description, poster_file_id, contact, is_email) VALUES ({ph}, {ph}, {ph}, {ph}, {ph}, {ph})",
                (user_id, title, description, poster_file_id, contact, is_email)
            )
            job_id = cursor.lastrowid if DB_BACKEND == 'sqlite' else cursor.lastrowid if hasattr(cursor, 'lastrowid') else cursor.lastrowid
            conn.commit()
            logger.info(f"Posted new job with ID {job_id} by user {user_id}")
            return job_id
        except Exception as e:
            logger.error(f"Error posting job: {e}")
            if JobDatabase.rollbar:
                JobDatabase.rollbar.capture_exception(
                    exc_info=(type(e), e, e.__traceback__),
                    extra_data={'method': 'post_job', 'user_id': user_id, 'title': title}
                )
            return None
        finally:
            if conn:
                conn.close()
    
    @staticmethod
    def update_job_message_id(job_id: int, message_id: str) -> bool:
        """Update the message ID for a posted job."""
        try:
            conn = get_db_connection()
            cursor = JobDatabase._dict_cursor(conn)
            ph = JobDatabase._get_placeholder()
            cursor.execute(
                f"UPDATE jobs SET message_id = {ph} WHERE id = {ph}",
                (message_id, job_id)
            )
            conn.commit()
            success = cursor.rowcount > 0
            if success:
                logger.info(f"Updated message ID for job {job_id}")
            else:
                logger.warning(f"Failed to update message ID for job {job_id}")
            return success
        except Exception as e:
            logger.error(f"Error updating job message ID: {e}")
            if JobDatabase.rollbar:
                JobDatabase.rollbar.capture_exception(
                    exc_info=(type(e), e, e.__traceback__),
                    extra_data={'method': 'update_job_message_id', 'job_id': job_id, 'message_id': message_id}
                )
            return False
        finally:
            if conn:
                conn.close()
    
    @staticmethod
    def get_user_posted_jobs(user_id: str) -> List[Dict]:
        """Get all jobs posted by a specific user."""
        jobs = []
        try:
            conn = get_db_connection()
            cursor = JobDatabase._dict_cursor(conn)
            ph = JobDatabase._get_placeholder()
            cursor.execute(f"SELECT * FROM jobs WHERE user_id = {ph} ORDER BY created_at DESC", (user_id,))
            rows = cursor.fetchall()
            for row in rows:
                jobs.append({
                    'id': row['id'] if isinstance(row, dict) else row[0],
                    'title': row['title'] if isinstance(row, dict) else row[2],
                    'description': row['description'] if isinstance(row, dict) else row[3],
                    'poster_file_id': row['poster_file_id'] if isinstance(row, dict) else row[4],
                    'contact': row['contact'] if isinstance(row, dict) else row[5],
                    'is_email': bool(row['is_email']) if isinstance(row, dict) else bool(row[6]),
                    'message_id': row['message_id'] if isinstance(row, dict) else row[7],
                    'created_at': row['created_at'] if isinstance(row, dict) else row[8]
                })
            logger.info(f"Retrieved {len(jobs)} posted jobs for user {user_id}")
        except Exception as e:
            logger.error(f"Error getting user posted jobs: {e}")
            if JobDatabase.rollbar:
                JobDatabase.rollbar.capture_exception(
                    exc_info=(type(e), e, e.__traceback__),
                    extra_data={'method': 'get_user_posted_jobs', 'user_id': user_id}
                )
        finally:
            if conn:
                conn.close()
        
        return jobs
    
    @staticmethod
    def delete_posted_job(job_id: int, user_id: str) -> Optional[str]:
        """Delete a posted job and return the message ID if successful."""
        try:
            conn = get_db_connection()
            cursor = JobDatabase._dict_cursor(conn)
            ph = JobDatabase._get_placeholder()
            cursor.execute(f"SELECT message_id FROM jobs WHERE id = {ph} AND user_id = {ph}", (job_id, user_id))
            result = cursor.fetchone()
            if not result:
                logger.warning(f"No job found with ID {job_id} for user {user_id}")
                return None
            message_id = result['message_id'] if isinstance(result, dict) else result[0]
            cursor.execute(f"DELETE FROM jobs WHERE id = {ph} AND user_id = {ph}", (job_id, user_id))
            conn.commit()
            logger.info(f"Deleted job {job_id} posted by user {user_id}")
            return message_id
        except Exception as e:
            logger.error(f"Error deleting posted job: {e}")
            if JobDatabase.rollbar:
                JobDatabase.rollbar.capture_exception(
                    exc_info=(type(e), e, e.__traceback__),
                    extra_data={'method': 'delete_posted_job', 'job_id': job_id, 'user_id': user_id}
                )
            return None
        finally:
            if conn:
                conn.close()
    
    @staticmethod
    def get_job_details(job_id: int) -> Optional[Dict]:
        """Get details of a specific job by ID."""
        try:
            conn = get_db_connection()
            cursor = JobDatabase._dict_cursor(conn)
            ph = JobDatabase._get_placeholder()
            cursor.execute(f"SELECT * FROM jobs WHERE id = {ph}", (job_id,))
            row = cursor.fetchone()
            if row:
                job = {
                    'id': row['id'] if isinstance(row, dict) else row[0],
                    'user_id': row['user_id'] if isinstance(row, dict) else row[1],
                    'title': row['title'] if isinstance(row, dict) else row[2],
                    'description': row['description'] if isinstance(row, dict) else row[3],
                    'poster_file_id': row['poster_file_id'] if isinstance(row, dict) else row[4],
                    'contact': row['contact'] if isinstance(row, dict) else row[5],
                    'is_email': bool(row['is_email']) if isinstance(row, dict) else bool(row[6]),
                    'message_id': row['message_id'] if isinstance(row, dict) else row[7],
                    'created_at': row['created_at'] if isinstance(row, dict) else row[8]
                }
                return job
            else:
                return None
        except Exception as e:
            logger.error(f"Error getting job details: {e}")
            if JobDatabase.rollbar:
                JobDatabase.rollbar.capture_exception(
                    exc_info=(type(e), e, e.__traceback__),
                    extra_data={'method': 'get_job_details', 'job_id': job_id}
                )
            return None
        finally:
            if conn:
                conn.close()
    
    @staticmethod
    def get_recent_jobs(limit: int = 10) -> List[Dict]:
        """Get the most recent user-posted jobs."""
        jobs = []
        try:
            conn = get_db_connection()
            cursor = JobDatabase._dict_cursor(conn)
            ph = JobDatabase._get_placeholder()
            # MySQL does not allow LIMIT as a parameter, so interpolate limit for MySQL
            if DB_BACKEND == 'mysql':
                cursor.execute(f"SELECT * FROM jobs ORDER BY created_at DESC LIMIT {limit}")
            else:
                cursor.execute(f"SELECT * FROM jobs ORDER BY created_at DESC LIMIT {ph}", (limit,))
            rows = cursor.fetchall()
            for row in rows:
                jobs.append({
                    'id': row['id'] if isinstance(row, dict) else row[0],
                    'user_id': row['user_id'] if isinstance(row, dict) else row[1],
                    'title': row['title'] if isinstance(row, dict) else row[2],
                    'description': row['description'] if isinstance(row, dict) else row[3],
                    'poster_file_id': row['poster_file_id'] if isinstance(row, dict) else row[4],
                    'contact': row['contact'] if isinstance(row, dict) else row[5],
                    'is_email': bool(row['is_email']) if isinstance(row, dict) else bool(row[6]),
                    'message_id': row['message_id'] if isinstance(row, dict) else row[7],
                    'created_at': row['created_at'] if isinstance(row, dict) else row[8]
                })
            logger.info(f"Retrieved {len(jobs)} recent jobs")
        except Exception as e:
            logger.error(f"Error getting recent jobs: {e}")
            if JobDatabase.rollbar:
                JobDatabase.rollbar.capture_exception(
                    exc_info=(type(e), e, e.__traceback__),
                    extra_data={'method': 'get_recent_jobs', 'limit': limit}
                )
        finally:
            if conn:
                conn.close()
        
        return jobs

    @staticmethod
    def migrate_subscriptions_to_followings():
        """Migrates data from subscriptions table to followings table for SQLite only."""
        try:
            # Only run this migration for SQLite
            conn = sqlite3.connect(DATABASE_FILE)
            cursor = conn.cursor()
            
            # Check if followings table exists
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='followings'")
            followings_table_exists = cursor.fetchone() is not None
            
            # Check if subscriptions table exists
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='subscriptions'")
            subscriptions_table_exists = cursor.fetchone() is not None
            
            if not followings_table_exists:
                logger.warning("Followings table does not exist, cannot migrate subscriptions")
                return
                
            if not subscriptions_table_exists:
                logger.warning("Subscriptions table does not exist, nothing to migrate")
                return
            
            # Check if migration is needed (if followings table is empty)
            cursor.execute("SELECT COUNT(*) FROM followings")
            followings_count = cursor.fetchone()[0]
            
            if followings_count == 0:
                # Copy data from subscriptions to followings
                cursor.execute("""
                    INSERT INTO followings (user_id, keywords, created_at)
                    SELECT user_id, keywords, created_at FROM subscriptions
                """)
                
                # Get row count
                cursor.execute("SELECT COUNT(*) FROM subscriptions")
                migrated_count = cursor.fetchone()[0]
                
                conn.commit()
                logger.info(f"Migrated {migrated_count} subscriptions to followings table")
            else:
                logger.info("Followings table already contains data, skipping migration")
                
        except sqlite3.Error as e:
            logger.error(f"Error migrating subscriptions to followings: {e}")
            if JobDatabase.rollbar:
                JobDatabase.rollbar.capture_exception(
                    exc_info=(type(e), e, e.__traceback__),
                    extra_data={'method': 'migrate_subscriptions_to_followings'}
                )
        finally:
            if conn:
                conn.close()
    
    @staticmethod
    def migrate_scraped_jobs_table():
        """Migrates the scraped_jobs table schema as needed."""
        try:
            conn = sqlite3.connect(DATABASE_FILE)
            cursor = conn.cursor()
            
            # Check current schema
            cursor.execute("PRAGMA table_info(scraped_jobs)")
            columns = [column[1] for column in cursor.fetchall()]
            
            # Migrate from old schema if needed (date column -> description column)
            if 'date' in columns and 'description' not in columns:
                logger.info("Migrating scraped_jobs table: removing date field, adding description field")
                
                # Create new table with updated schema
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS scraped_jobs_new (
                        url TEXT PRIMARY KEY,
                        title TEXT NOT NULL,
                        company TEXT NOT NULL,
                        location TEXT,
                        source TEXT NOT NULL,
                        description TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                
                # Copy data from old table to new table
                cursor.execute('''
                    INSERT INTO scraped_jobs_new (url, title, company, location, source, created_at)
                    SELECT url, title, company, location, source, created_at FROM scraped_jobs
                ''')
                
                # Drop old table
                cursor.execute("DROP TABLE scraped_jobs")
                
                # Rename new table to original name
                cursor.execute("ALTER TABLE scraped_jobs_new RENAME TO scraped_jobs")
                
                conn.commit()
                logger.info("Migration completed successfully")
                
                # Re-get columns after first migration
                cursor.execute("PRAGMA table_info(scraped_jobs)")
                columns = [column[1] for column in cursor.fetchall()]
            
            # Add message_id column if it doesn't exist
            if 'message_id' not in columns:
                logger.info("Migrating scraped_jobs table: adding message_id field")
                
                # Add the message_id column to the existing table
                cursor.execute("ALTER TABLE scraped_jobs ADD COLUMN message_id TEXT")
                
                conn.commit()
                logger.info("Message ID column migration completed successfully")
            else:
                logger.info("No migration needed or already completed")
        except sqlite3.Error as e:
            logger.error(f"Migration error: {e}")
            if JobDatabase.rollbar:
                JobDatabase.rollbar.capture_exception(
                    exc_info=(type(e), e, e.__traceback__),
                    extra_data={'method': 'migrate_scraped_jobs_table'}
                )
        finally:
            if conn:
                conn.close()


# Initialize and migrate the database when the module is imported
JobDatabase.setup_database()
JobDatabase.migrate_scraped_jobs_table()
JobDatabase.migrate_subscriptions_to_followings()
