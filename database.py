import sqlite3

# Database setup
def setup_database():
    conn = sqlite3.connect("gigsta.db")
    c = conn.cursor()
    c.execute(
        """CREATE TABLE IF NOT EXISTS jobs
                 (id INTEGER PRIMARY KEY, category TEXT, title TEXT, description TEXT, contact TEXT)"""
    )
    c.execute(
        """CREATE TABLE IF NOT EXISTS subscriptions
                 (user_id INTEGER, category TEXT,
                  UNIQUE(user_id, category))"""
    )
    c.execute(
        """CREATE TABLE IF NOT EXISTS user_preferences
             (user_id INTEGER PRIMARY KEY, region TEXT, language_code TEXT)"""
    )
    conn.commit()
    conn.close()

async def get_user_region(user_id: int):
    conn = sqlite3.connect("gigsta.db")
    c = conn.cursor()
    c.execute("SELECT region FROM user_preferences WHERE user_id = ?", (user_id,))
    region = c.fetchone()
    conn.close()

    if region:
        return region[0]

    return None


# Define a function to set the user's region
async def set_user_region(user_id: int, region: str):
    conn = sqlite3.connect("gigsta.db")
    c = conn.cursor()
    c.execute(
        "INSERT OR REPLACE INTO user_preferences (user_id, region) VALUES (?, ?)", (user_id, region)
    )
    conn.commit()
    conn.close()

async def get_user_language(user_id: int) -> str:
    conn = sqlite3.connect("gigsta.db")
    c = conn.cursor()
    c.execute("SELECT language_code FROM user_preferences WHERE user_id = ?", (user_id,))
    result = c.fetchone()
    conn.close()
    return result[0] if result else None

async def set_user_language(user_id: int, language_code: str):
    conn = sqlite3.connect("gigsta.db")
    c = conn.cursor()
    c.execute(
        "INSERT OR REPLACE INTO user_preferences (user_id, language_code) VALUES (?, ?)",
        (user_id, language_code)
    )
    conn.commit()
    conn.close()

async def save_job(category, title, description, contact) -> int:
    conn = sqlite3.connect('gigsta.db')
    c = conn.cursor()
    c.execute("INSERT INTO jobs (category, title, description, contact) VALUES (?, ?, ?, ?)", (category, title, description, contact))
    job_id = c.lastrowid
    conn.commit()
    conn.close()

    return job_id

async def get_jobs(category=None):
    conn = sqlite3.connect('gigsta.db')
    c = conn.cursor()
    if category:
        c.execute("SELECT * FROM jobs WHERE category = ?", (category,))
    else:
        c.execute("SELECT * FROM jobs")
    jobs = c.fetchall()
    conn.close()
    return jobs

async def subscribe_user(user_id, category) -> bool:
    conn = sqlite3.connect('gigsta.db')
    c = conn.cursor()
    try:
        c.execute("INSERT OR IGNORE INTO subscriptions (user_id, category) VALUES (?, ?)", (user_id, category))
        conn.commit()
        conn.close()
        return True
    except sqlite3.IntegrityError:
        conn.close()
        return False

async def unsubscribe_user(user_id, category):
    conn = sqlite3.connect('gigsta.db')
    c = conn.cursor()
    c.execute("DELETE FROM subscriptions WHERE user_id = ? AND category = ?", (user_id, category))
    conn.commit()
    conn.close()

async def get_category_subscriptions(user_id):
    conn = sqlite3.connect('gigsta.db')
    c = conn.cursor()
    c.execute("SELECT DISTINCT category FROM subscriptions WHERE user_id = ?", (user_id,))
    categories = c.fetchall()
    conn.close()
    return categories

async def search_jobs_by_term(search_term: str):
    conn = sqlite3.connect("gigsta.db")
    c = conn.cursor()
    c.execute(
        "SELECT * FROM jobs WHERE category LIKE ? OR title LIKE ? OR description LIKE ?",
        (f"%{search_term}%", f"%{search_term}%", f"%{search_term}%"),
    )
    jobs = c.fetchall()
    conn.close()
    return jobs

async def get_subscribers_for_category(category: str):
    conn = sqlite3.connect("gigsta.db")
    c = conn.cursor()
    c.execute("SELECT user_id FROM subscriptions WHERE category = ?", (category,))
    subscribers = c.fetchall()
    conn.close()
    return subscribers

async def get_job_info(job_id: int):
    conn = sqlite3.connect("gigsta.db")
    c = conn.cursor()
    c.execute("SELECT title, description FROM jobs WHERE id = ?", (job_id,))
    job_info = c.fetchone()
    conn.close()
    return job_info if job_info else None