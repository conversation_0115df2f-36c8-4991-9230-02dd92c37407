import os
import sqlite3
import mysql.connector
from config import get_database_file, get_mysql_config

# Load SQLite DB
sqlite_db = get_database_file()
conn_sqlite = sqlite3.connect(sqlite_db)
cursor_sqlite = conn_sqlite.cursor()

# Connect to MySQL using config
mysql_cfg = get_mysql_config()
mysql_conn = mysql.connector.connect(
    host=mysql_cfg['host'],
    user=mysql_cfg['user'],
    password=mysql_cfg['password'],
    database=mysql_cfg['database'],
    port=mysql_cfg['port']
)
cursor_mysql = mysql_conn.cursor()

def migrate_table(table_name, columns, insert_sql):
    cursor_sqlite.execute(f'SELECT {", ".join(columns)} FROM {table_name}')
    rows = cursor_sqlite.fetchall()
    for row in rows:
        try:
            cursor_mysql.execute(insert_sql, row)
        except Exception as e:
            print(f"Error inserting row into {table_name}: {e}")
    mysql_conn.commit()
    print(f"Migrated {len(rows)} rows from {table_name}")

# Migrate subscriptions
migrate_table(
    'subscriptions',
    ['id', 'user_id', 'keywords', 'created_at'],
    'INSERT IGNORE INTO subscriptions (id, user_id, keywords, created_at) VALUES (%s, %s, %s, %s)'
)

# Migrate followings
migrate_table(
    'followings',
    ['id', 'user_id', 'keywords', 'created_at'],
    'INSERT IGNORE INTO followings (id, user_id, keywords, created_at) VALUES (%s, %s, %s, %s)'
)

# Migrate scraped_jobs
migrate_table(
    'scraped_jobs',
    ['url', 'title', 'company', 'location', 'source', 'description', 'message_id', 'created_at'],
    'INSERT IGNORE INTO scraped_jobs (url, title, company, location, source, description, message_id, created_at) VALUES (%s, %s, %s, %s, %s, %s, %s, %s)'
)

# Migrate jobs
migrate_table(
    'jobs',
    ['id', 'user_id', 'title', 'description', 'poster_file_id', 'contact', 'is_email', 'message_id', 'created_at'],
    'INSERT IGNORE INTO jobs (id, user_id, title, description, poster_file_id, contact, is_email, message_id, created_at) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)'
)

# # Also migrate any existing followings data directly
# try:
#     cursor_sqlite.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='followings'")
#     if cursor_sqlite.fetchone():
#         migrate_table(
#             'followings',
#             ['id', 'user_id', 'keywords', 'created_at'],
#             'INSERT IGNORE INTO followings (id, user_id, keywords, created_at) VALUES (%s, %s, %s, %s)'
#         )
#     else:
#         print("No followings table in SQLite yet, skipping direct followings migration")
# except Exception as e:
#     print(f"Error checking for followings table: {e}")

print("Migration completed. Your SQLite data is now in MySQL.")

# Close connections
cursor_sqlite.close()
conn_sqlite.close()
cursor_mysql.close()
mysql_conn.close()
