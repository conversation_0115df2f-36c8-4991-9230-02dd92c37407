FROM python:3.11-slim

WORKDIR /app

# Install system dependencies required for Playwright
RUN apt-get update && apt-get install -y \
    wget \
    gnupg \
    libglib2.0-0 \
    libnss3 \
    libnspr4 \
    libatk1.0-0 \
    libatk-bridge2.0-0 \
    libcups2 \
    libdrm2 \
    libdbus-1-3 \
    libexpat1 \
    libxcb1 \
    libxkbcommon0 \
    libx11-6 \
    libxcomposite1 \
    libxdamage1 \
    libxext6 \
    libxfixes3 \
    libxrandr2 \
    libgbm1 \
    libpango-1.0-0 \
    libcairo2 \
    libasound2 \
    libatspi2.0-0 \
    xvfb \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements file
COPY requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt
RUN pip install "python-telegram-bot[job-queue]==22.0"

# Install crawl4ai tools and Playwright dependencies
RUN crawl4ai-setup
RUN crawl4ai-doctor
<PERSON><PERSON> playwright install chromium --with-deps

# Copy application code
COPY . .

# Create a symlink for job_scraper.py in the root directory to fix imports
RUN ln -sf /app/job/job_scraper.py /app/job_scraper.py

# Command to run the application
CMD ["python", "job/gigsta_job_bot_v2.py"]
