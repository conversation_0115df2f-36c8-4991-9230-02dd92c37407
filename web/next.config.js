/** @type {import('next').NextConfig} */
const nextConfig = {
  webpack: (config, { buildId, dev, isServer, defaultLoaders, webpack }) => {
    // Add handlebars-loader configuration
    config.module.rules.push({
      test: /\.hbs$/,
      use: [
        {
          loader: 'handlebars-loader',
          options: {
            helperDirs: [
              // Path to custom helpers directory
              require('path').join(__dirname, 'utils/handlebars-helpers')
            ],
            runtime: 'handlebars/runtime', // Use runtime-only version
            precompileOptions: {
              knownHelpersOnly: false,
            }
          }
        }
      ]
    });

    // Important: Return the modified config
    return config;
  },
  // Other Next.js config options can be added here
  experimental: {
    // Enable if you need experimental features
  }
};

module.exports = nextConfig;