const fs = require('fs');
const path = require('path');

// Mock Handlebars for testing
const Handlebars = require('handlebars');

// Register helpers from the helpers file
const helpers = require('./utils/handlebars-helpers/index.js');
Object.keys(helpers).forEach(helperName => {
  Handlebars.registerHelper(helperName, helpers[helperName]);
});

// Test data with various empty field scenarios
const emptyFieldTestCases = [
  {
    name: "Complete Empty Fields Test",
    description: "Tests all possible empty field scenarios",
    data: {
      personalInfo: {
        fullName: "<PERSON>",
        email: "<EMAIL>",
        phone: "", // Empty string
        linkedin: "", // Empty string
        location: "Jakarta",
        website: "", // Empty string
        github: "" // Empty string - missing optional fields
      },
      professionalSummary: "Experienced professional", // Has content
      targetPosition: "Software Engineer",
      experiences: [
        {
          id: "1",
          jobTitle: "Senior Developer",
          company: "Tech Corp",
          location: "Jakarta",
          startDate: "2022-01-01",
          endDate: "Present",
          responsibilities: [] // Empty array
        },
        {
          id: "2", 
          jobTitle: "Junior Developer",
          company: "StartupCo",
          location: "",
          startDate: "2020-01-01",
          endDate: "2021-12-31",
          responsibilities: ["", "   ", "   "] // Array with empty/whitespace strings
        }
      ],
      education: [
        {
          id: "1",
          degree: "Bachelor of Computer Science", 
          institution: "University of Jakarta",
          location: "",
          graduationDate: "2020-06-01",
          gpa: "", // Empty string
          relevantCoursework: [], // Empty array
          honors: [] // Empty array
        }
      ],
      skills: {
        categories: [
          {
            category: "Programming Languages",
            skills: ["JavaScript", "Python"]
          },
          {
            category: "", // Empty category name
            skills: ["Docker", "Git"]
          },
          {
            category: "Databases",
            skills: [] // Empty skills array
          }
        ]
      },
      certifications: [], // Empty array
      projects: [
        {
          id: "1",
          title: "Web Application",
          description: "",
          technologies: [], // Empty array
          link: "",
          achievements: [] // Empty array
        }
      ],
      languages: [], // Empty array
      awards: [] // Empty array
    }
  },
  {
    name: "Minimal Data Test",
    description: "Tests with only required fields",
    data: {
      personalInfo: {
        fullName: "Jane Smith",
        email: "<EMAIL>",
        location: "Bandung"
      },
      professionalSummary: "",
      targetPosition: "Designer",
      experiences: [],
      education: [],
      skills: {
        categories: []
      }
    }
  },
  {
    name: "Mixed Empty Content Test", 
    description: "Tests mix of empty and populated sections",
    data: {
      personalInfo: {
        fullName: "Bob Johnson",
        email: "<EMAIL>",
        phone: "+62-***********",
        linkedin: "",
        location: "",
        website: "https://bob.dev",
        github: ""
      },
      professionalSummary: "",
      targetPosition: "Data Scientist",
      experiences: [
        {
          id: "1",
          jobTitle: "Data Analyst",
          company: "DataCorp",
          location: "Remote",
          startDate: "2021-01-01", 
          endDate: "Present",
          responsibilities: [
            "Analyzed data trends",
            "", // Empty responsibility
            "Created reports"
          ]
        }
      ],
      education: [
        {
          id: "1",
          degree: "Master of Data Science",
          institution: "Tech University", 
          graduationDate: "2020-12-01",
          gpa: "3.8",
          relevantCoursework: [""],
          honors: ["Cum Laude", ""]
        }
      ],
      skills: {
        categories: [
          {
            category: "Programming",
            skills: ["Python", "R", "SQL"]
          },
          {
            category: "Tools",
            skills: ["", "Tableau", ""]  // Mixed empty strings
          }
        ]
      },
      projects: [],
      certifications: [
        {
          id: "1",
          name: "AWS Certified",
          issuer: "Amazon",
          date: "2023-01-01",
          credentialId: ""
        }
      ]
    }
  }
];

// Template files to test
const templateFiles = [
  'utils/handlebars-templates/clean-professional.hbs',
  'utils/handlebars-templates/modern-clean.hbs',
  'utils/handlebars-templates/classic-professional.hbs'
];

// Convert structured data to template format (simplified version of convertToTemplateData)
function convertToTemplateData(data) {
  const extractYear = (dateString) => {
    if (!dateString) return '';
    const year = new Date(dateString).getFullYear();
    return isNaN(year) ? dateString : year.toString();
  };

  return {
    name: data.personalInfo.fullName,
    email: data.personalInfo.email,
    phone: data.personalInfo.phone,
    linkedin: data.personalInfo.linkedin,
    location: data.personalInfo.location,
    website: data.personalInfo.website,
    github: data.personalInfo.github,
    summary: data.professionalSummary,
    jobTitle: data.targetPosition,
    experiences: Array.isArray(data.experiences) ? data.experiences.map(exp => ({
      id: exp.id,
      title: exp.jobTitle,
      jobTitle: exp.jobTitle,
      company: exp.company,
      location: exp.location,
      startDate: exp.startDate,
      endDate: exp.endDate,
      responsibilities: Array.isArray(exp.responsibilities) ? exp.responsibilities : [],
    })) : [],
    education: Array.isArray(data.education) ? data.education.map(edu => {
      const endYear = extractYear(edu.graduationDate);
      const startYearNum = parseInt(endYear) - 4;
      return {
        id: edu.id,
        degree: edu.degree,
        institution: edu.institution,
        school: edu.institution,
        location: edu.location,
        graduationDate: edu.graduationDate,
        startYear: startYearNum > 0 ? startYearNum.toString() : '',
        endYear: endYear,
        gpa: edu.gpa,
        relevantCoursework: Array.isArray(edu.relevantCoursework) ? edu.relevantCoursework.join(', ') : '',
        honors: Array.isArray(edu.honors) ? edu.honors.join(', ') : '',
      };
    }) : [],
    skills: data.skills?.categories?.map(cat => ({
      category: cat.category,
      skills: Array.isArray(cat.skills) ? cat.skills.join(', ') : '',
    })) || [],
    skillCategories: data.skills?.categories?.map(cat => ({
      category: cat.category,
      skills: Array.isArray(cat.skills) ? cat.skills.join(', ') : '',
    })) || [],
    projects: data.projects?.map(proj => ({
      id: proj.id,
      title: proj.title,
      description: proj.description,
      technologies: Array.isArray(proj.technologies) ? proj.technologies.join(', ') : '',
      link: proj.link,
      achievements: proj.achievements,
    })),
    certifications: data.certifications?.map(cert => ({
      id: cert.id,
      name: cert.name,
      issuer: cert.issuer,
      date: cert.date,
      year: extractYear(cert.date),
      credentialId: cert.credentialId,
    })),
    languages: data.languages,
    awards: data.awards,
  };
}

// Function to check for empty field issues in HTML
function checkEmptyFieldIssues(html, templateName, testCaseName) {
  const issues = [];
  
  // Check for empty <li> elements
  const emptyLiRegex = /<li>\s*<\/li>/g;
  const emptyLiMatches = html.match(emptyLiRegex);
  if (emptyLiMatches) {
    issues.push(`Found ${emptyLiMatches.length} empty <li> elements`);
  }

  // Check for trailing separators in contact info
  const trailingSeparators = [
    /\|\s*<\/div>/g, // Trailing pipe
    /•\s*<\/div>/g,  // Trailing bullet
    /\|\s*<\/span>/g, // Trailing pipe in span
    /•\s*<\/span>/g   // Trailing bullet in span
  ];
  
  trailingSeparators.forEach((regex, index) => {
    const matches = html.match(regex);
    if (matches) {
      issues.push(`Found ${matches.length} trailing separators (pattern ${index + 1})`);
    }
  });

  // Check for labels without values
  const labelPatterns = [
    /IPK:\s*(<\/div>|<\/span>)/g,
    /Mata Kuliah Terkait:\s*(<\/div>|<\/span>)/g,
    /Penghargaan:\s*(<\/div>|<\/span>)/g,
    /Teknologi:\s*(<\/div>|<\/span>)/g,
    /LinkedIn:\s*(<\/div>|<\/span>)/g,
    /GitHub:\s*(<\/div>|<\/span>)/g,
    /ID:\s*(<\/div>|<\/span>)/g
  ];

  labelPatterns.forEach((regex, index) => {
    const matches = html.match(regex);
    if (matches) {
      issues.push(`Found ${matches.length} labels without values (pattern ${index + 1})`);
    }
  });

  // Check for empty spans and divs with only whitespace
  const emptyElements = [
    /<div[^>]*>\s*<\/div>/g,
    /<span[^>]*>\s*<\/span>/g
  ];

  emptyElements.forEach((regex, index) => {
    const matches = html.match(regex);
    if (matches) {
      // Filter out styling divs (those with only style attributes)
      const nonStyleMatches = matches.filter(match => 
        !match.includes('style=') || match.includes('class=')
      );
      if (nonStyleMatches.length > 0) {
        issues.push(`Found ${nonStyleMatches.length} empty elements (type ${index + 1})`);
      }
    }
  });

  // Check for section titles with no content
  const sectionTitleRegex = /<div class="section-title">([^<]+)<\/div>\s*<\/div>/g;
  const emptySectionMatches = html.match(sectionTitleRegex);
  if (emptySectionMatches) {
    issues.push(`Found ${emptySectionMatches.length} empty sections with titles`);
  }

  return issues;
}

// Main test function
async function runEmptyFieldTests() {
  console.log('='.repeat(80));
  console.log('HANDLEBARS TEMPLATE EMPTY FIELD TESTING');
  console.log('='.repeat(80));
  console.log();

  const results = {
    totalTests: 0,
    passedTests: 0,
    failedTests: 0,
    templateResults: {},
    summary: []
  };

  for (const templateFile of templateFiles) {
    const templateName = path.basename(templateFile, '.hbs');
    console.log(`\n${'='.repeat(60)}`);
    console.log(`TESTING TEMPLATE: ${templateName.toUpperCase()}`);
    console.log(`${'='.repeat(60)}`);

    results.templateResults[templateName] = {
      testCases: {},
      totalIssues: 0
    };

    try {
      // Read template file
      const templateContent = fs.readFileSync(templateFile, 'utf8');
      const template = Handlebars.compile(templateContent);

      for (const testCase of emptyFieldTestCases) {
        console.log(`\n--- Test Case: ${testCase.name} ---`);
        console.log(`Description: ${testCase.description}`);

        results.totalTests++;

        try {
          // Convert data to template format
          const templateData = convertToTemplateData(testCase.data);
          
          // Render template
          const html = template(templateData);
          
          // Check for issues
          const issues = checkEmptyFieldIssues(html, templateName, testCase.name);
          
          results.templateResults[templateName].testCases[testCase.name] = {
            issues: issues,
            passed: issues.length === 0,
            html: html
          };

          results.templateResults[templateName].totalIssues += issues.length;

          if (issues.length === 0) {
            console.log('✅ PASSED - No empty field issues found');
            results.passedTests++;
          } else {
            console.log(`❌ FAILED - Found ${issues.length} issues:`);
            issues.forEach(issue => console.log(`   • ${issue}`));
            results.failedTests++;
          }

        } catch (error) {
          console.log(`❌ ERROR - Template rendering failed: ${error.message}`);
          results.failedTests++;
          results.templateResults[templateName].testCases[testCase.name] = {
            issues: [`Template rendering error: ${error.message}`],
            passed: false,
            error: error.message
          };
        }
      }

    } catch (error) {
      console.log(`❌ ERROR - Could not load template: ${error.message}`);
      results.templateResults[templateName].error = error.message;
    }
  }

  // Generate summary
  console.log('\n' + '='.repeat(80));
  console.log('TEST SUMMARY');
  console.log('='.repeat(80));
  console.log(`Total Tests: ${results.totalTests}`);
  console.log(`Passed: ${results.passedTests}`);
  console.log(`Failed: ${results.failedTests}`);
  console.log(`Success Rate: ${((results.passedTests / results.totalTests) * 100).toFixed(1)}%`);

  console.log('\nIssues by Template:');
  Object.entries(results.templateResults).forEach(([templateName, templateResult]) => {
    if (templateResult.error) {
      console.log(`  ${templateName}: ERROR - ${templateResult.error}`);
    } else {
      console.log(`  ${templateName}: ${templateResult.totalIssues} total issues`);
    }
  });

  // Save detailed results to file
  const detailedResults = {
    timestamp: new Date().toISOString(),
    summary: {
      totalTests: results.totalTests,
      passedTests: results.passedTests,
      failedTests: results.failedTests,
      successRate: ((results.passedTests / results.totalTests) * 100).toFixed(1) + '%'
    },
    templateResults: results.templateResults
  };

  fs.writeFileSync('EMPTY_FIELD_TEST_RESULTS.json', JSON.stringify(detailedResults, null, 2));
  console.log('\n📄 Detailed results saved to EMPTY_FIELD_TEST_RESULTS.json');

  return results;
}

// Export for use in other files
module.exports = {
  runEmptyFieldTests,
  emptyFieldTestCases,
  convertToTemplateData,
  checkEmptyFieldIssues
};

// Run tests if this file is executed directly
if (require.main === module) {
  runEmptyFieldTests().catch(error => {
    console.error('Test execution failed:', error);
    process.exit(1);
  });
}