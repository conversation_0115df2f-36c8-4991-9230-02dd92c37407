# Generate Resume Edge Function

This Supabase Edge Function handles the async generation of job-specific resumes using AI. It processes resume and job information, generates tailored content, and updates the database with the results.

## Overview

The function implements the architecture specified in the job-specific resume builder plan, providing:
- Async resume generation using Google's Gemini AI
- Database updates with generation status and results
- Error handling and status tracking
- Support for multiple input formats (files, manual data, job descriptions, images)

## API Endpoint

```
POST /functions/v1/generate-resume
```

## Request Body

```typescript
interface GenerateResumeRequest {
  resumeId: string;           // UUID of the resume record in database
  resumeInput: ResumeInput;   // Resume data (file or manual)
  jobInput: JobInput;         // Job information (description or image)
}

interface ResumeInput {
  file?: {
    buffer: ArrayBuffer;
    mimeType: string;
  };
  manual?: {
    fullName: string;
    professionalTitle: string;
    professionalSummary: string;
    mostRecentJob: {
      title: string;
      company: string;
      achievements: string;
    };
    skills: string;
  };
}

interface JobInput {
  description?: string;
  image?: {
    buffer: ArrayBuffer;
    mimeType: string;
  };
}
```

## Response

### Success Response
```json
{
  "success": true,
  "resumeId": "uuid-here",
  "matchScore": 87,
  "atsScore": 92,
  "message": "Resume generated successfully"
}
```

### Error Response
```json
{
  "success": false,
  "error": "Error message here"
}
```

## Environment Variables

Copy `.env.example` to `.env` and configure:

```bash
# Supabase Configuration
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key-here

# Google AI Configuration
GOOGLE_AI_API_KEY=your-google-ai-api-key-here
```

## Database Schema

The function updates the `job_specific_resumes` table with the following structure:

```sql
CREATE TABLE job_specific_resumes (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  job_posting_id UUID REFERENCES job_postings(id) ON DELETE SET NULL,
  original_resume_id UUID REFERENCES resumes(id) ON DELETE SET NULL,
  tailored_content JSONB NOT NULL,
  match_score INTEGER,
  ats_score INTEGER,
  status VARCHAR(50) DEFAULT 'pending',
  error_message TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## Status Flow

1. **pending** - Initial state when resume generation is requested
2. **processing** - Function is actively generating the resume
3. **completed** - Resume generation completed successfully
4. **failed** - Resume generation failed with error

## Supported File Types

### Resume Files
- PDF (`application/pdf`)
- DOCX (`application/vnd.openxmlformats-officedocument.wordprocessingml.document`)
- Plain text (`text/plain`)
- Images (`image/png`, `image/jpeg`, `image/jpg`)

### Job Posting Images
- PNG (`image/png`)
- JPEG (`image/jpeg`)
- JPG (`image/jpg`)

## Deployment

### Prerequisites
- Supabase CLI installed
- Deno runtime (for local development)

### Deploy to Supabase

1. Navigate to your Supabase project directory:
```bash
cd web
```

2. Deploy the function:
```bash
supabase functions deploy generate-resume
```

3. Set environment variables:
```bash
supabase secrets set GOOGLE_AI_API_KEY=your-api-key-here
```

### Local Development

1. Start the local Supabase environment:
```bash
supabase start
```

2. Serve the function locally:
```bash
supabase functions serve generate-resume
```

3. Test the function:
```bash
curl -X POST http://localhost:54321/functions/v1/generate-resume \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-anon-key" \
  -d '{
    "resumeId": "test-uuid",
    "resumeInput": {
      "manual": {
        "fullName": "John Doe",
        "professionalTitle": "Software Engineer",
        "professionalSummary": "Experienced developer...",
        "mostRecentJob": {
          "title": "Senior Developer",
          "company": "Tech Corp",
          "achievements": "Built scalable applications..."
        },
        "skills": "JavaScript, Python, React"
      }
    },
    "jobInput": {
      "description": "We are looking for a Senior Software Engineer..."
    }
  }'
```

## Error Handling

The function includes comprehensive error handling:

- **Input validation**: Validates required parameters and file types
- **AI generation errors**: Handles AI API failures gracefully
- **Database errors**: Logs database update failures
- **Status tracking**: Updates resume status appropriately

## Performance Considerations

- **Timeout**: Function has a 30-second timeout limit
- **Memory**: Optimized for large file processing
- **Concurrency**: Handles multiple concurrent requests
- **Rate limiting**: Respects Google AI API rate limits

## Security

- **CORS**: Configured for cross-origin requests
- **Authentication**: Uses Supabase service role key
- **Data validation**: Validates all input data
- **RLS**: Row Level Security policies applied

## Monitoring

Monitor the function using:
- Supabase Dashboard logs
- Database status tracking
- Error reporting in database

## Integration

This Edge Function integrates with:
- Job-specific resume builder frontend
- Database tables for resume management
- Google AI for content generation
- Supabase auth for user management

## Next Steps

1. Deploy the database migration: `create_job_resume_builder_tables.sql`
2. Configure environment variables in Supabase
3. Test the function with your frontend application
4. Monitor performance and adjust as needed