import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';
import puppeteer from 'https://deno.land/x/puppeteer@16.2.0/mod.ts';

interface ResumeData {
  id: string;
  user_id: string;
  html: string;
  structured_data: any;
  created_at: string;
  data?: any; // For checking resume type
  tokens_deducted?: boolean; // Track if tokens have been deducted for this resume
}

/**
 * Token costs for different resume types
 */
const RESUME_TOKEN_COSTS = {
  JOB_SPECIFIC: 20, // AI-generated resume tailored to job posting
  MANUAL: 15,       // Manual resume created from scratch
} as const;

// CORS headers for browser requests
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type, x-supabase-auth',
  'Access-Control-Allow-Methods': 'POST, GET, OPTIONS',
};

/**
 * Check if a resume is job-specific (AI-generated) or manual
 * Uses the buildMethod field in the data column to determine resume type
 */
function getResumeType(resumeData: any): 'job-specific' | 'manual' {
  // Use the buildMethod field from the data column
  const buildMethod = resumeData?.data?.buildMethod;

  // Default to 'manual' for backward compatibility with existing resumes
  return buildMethod === 'job-specific' ? 'job-specific' : 'manual';
}

/**
 * Check if user has sufficient tokens for manual resume download
 * Only check tokens for manual resumes (15 tokens) and only if tokens haven't been deducted yet
 * Job-specific resumes are charged during generation, not download
 */
async function checkManualResumeTokens(supabase: any, resumeData: ResumeData): Promise<{ hasTokens: boolean; error?: string }> {
  try {
    const resumeType = getResumeType(resumeData);

    // Only check tokens for manual resumes during download
    if (resumeType !== 'manual') {
      console.log(`Skipping token check for ${resumeType} resume download`);
      return { hasTokens: true };
    }

    // If tokens have already been deducted for this resume, allow download without further checks
    if (resumeData.tokens_deducted) {
      console.log(`Tokens already deducted for resume ${resumeData.id}, allowing download`);
      return { hasTokens: true };
    }

    const userId = resumeData.user_id;
    const tokenCost = RESUME_TOKEN_COSTS.MANUAL;

    // Get current token balance
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('tokens')
      .eq('id', userId)
      .single();

    if (profileError || !profile) {
      console.error('Error fetching user profile for token check:', profileError);
      return { hasTokens: false, error: 'Failed to check token balance' };
    }

    const currentTokens = (profile.tokens as number | null) ?? 0;

    if (currentTokens < tokenCost) {
      return {
        hasTokens: false,
        error: `Token Anda tidak cukup untuk mengunduh CV`
      };
    }

    return { hasTokens: true };

  } catch (error) {
    console.error('Unexpected error during token check:', error);
    return { hasTokens: false, error: 'Failed to check token balance' };
  }
}

/**
 * Deduct tokens from user's account for manual resume download
 * Only deduct tokens for manual resumes (15 tokens) and only if not already deducted
 * Job-specific resumes are charged during generation, not download
 */
async function deductManualResumeTokens(supabase: any, resumeData: ResumeData): Promise<boolean> {
  try {
    const resumeType = getResumeType(resumeData);

    // Only deduct tokens for manual resumes during download
    if (resumeType !== 'manual') {
      console.log(`Skipping token deduction for ${resumeType} resume download`);
      return true;
    }

    // If tokens have already been deducted for this resume, skip deduction
    if (resumeData.tokens_deducted) {
      console.log(`Tokens already deducted for resume ${resumeData.id}, skipping token deduction`);
      return true;
    }

    const userId = resumeData.user_id;
    const resumeId = resumeData.id;
    const tokenCost = RESUME_TOKEN_COSTS.MANUAL;

    // Get current token balance
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('tokens')
      .eq('id', userId)
      .single();

    if (profileError || !profile) {
      console.error('Error fetching user profile for token deduction:', profileError);
      return false;
    }

    const currentTokens = (profile.tokens as number | null) ?? 0;
    const newTokens = Math.max(currentTokens - tokenCost, 0);

    // Use a transaction to atomically update both token balance and resume tokens_deducted status
    // This prevents race conditions where multiple downloads might occur simultaneously
    const { error: transactionError } = await supabase.rpc('deduct_resume_tokens', {
      p_user_id: userId,
      p_resume_id: resumeId,
      p_token_cost: tokenCost,
      p_new_token_balance: newTokens
    });

    if (transactionError) {
      // If the stored procedure doesn't exist, fall back to individual updates
      console.log('Stored procedure not available, using individual updates');
      
      // Update token balance
      const { error: updateTokenError } = await supabase
        .from('profiles')
        .update({ tokens: newTokens })
        .eq('id', userId);

      if (updateTokenError) {
        console.error('Error updating token balance:', updateTokenError);
        return false;
      }

      // Mark tokens as deducted for this resume
      const { error: updateResumeError } = await supabase
        .from('resumes')
        .update({ tokens_deducted: true })
        .eq('id', resumeId)
        .eq('user_id', userId); // Extra security check

      if (updateResumeError) {
        console.error('Error updating resume tokens_deducted status:', updateResumeError);
        // Even if this fails, we've already deducted tokens, so continue
      }
    }

    console.log(`Successfully deducted ${tokenCost} tokens from user ${userId} for manual resume ${resumeId}. New balance: ${newTokens}`);
    return true;

  } catch (error) {
    console.error('Unexpected error during token deduction:', error);
    return false;
  }
}

/**
 * Create Supabase client for edge functions
 */
function createSupabaseClient() {
  const supabaseUrl = Deno.env.get("SUPABASE_URL");
  const supabaseServiceKey = Deno.env.get("SUPABASE_SERVICE_ROLE_KEY");

  if (!supabaseUrl || !supabaseServiceKey) {
    throw new Error("Missing Supabase configuration");
  }

  return createClient(supabaseUrl, supabaseServiceKey);
}

/**
 * Check if a PDF exists in Supabase storage for the given resume ID
 */
async function checkPdfExists(supabase: any, resumeId: string): Promise<boolean> {
  try {
    const filePath = `${resumeId}.pdf`;
    const { data, error } = await supabase.storage
      .from('generated-resumes')
      .list('', { search: filePath });

    if (error) {
      console.error('Error checking PDF existence:', error);
      return false;
    }

    return data && data.length > 0;
  } catch (error) {
    console.error('Error checking PDF existence:', error);
    return false;
  }
}

/**
 * Download PDF from Supabase storage
 */
async function downloadPdfFromStorage(supabase: any, resumeId: string): Promise<Uint8Array | null> {
  try {
    const filePath = `${resumeId}.pdf`;
    const { data, error } = await supabase.storage
      .from('generated-resumes')
      .download(filePath);

    if (error) {
      console.error('Error downloading PDF from storage:', error);
      return null;
    }

    return new Uint8Array(await data.arrayBuffer());
  } catch (error) {
    console.error('Error downloading PDF from storage:', error);
    return null;
  }
}

/**
 * Upload PDF to Supabase storage
 */
async function uploadPdfToStorage(supabase: any, resumeId: string, pdfBuffer: Uint8Array): Promise<boolean> {
  try {
    const filePath = `${resumeId}.pdf`;
    const { error } = await supabase.storage
      .from('generated-resumes')
      .upload(filePath, pdfBuffer, {
        contentType: 'application/pdf',
        upsert: true, // Replace if exists
      });

    if (error) {
      console.error('Error uploading PDF to storage:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error uploading PDF to storage:', error);
    return false;
  }
}

/**
 * Generate PDF from resume data using Puppeteer
 */
async function generatePdf(resume: ResumeData): Promise<Uint8Array> {
  const browserlessToken = Deno.env.get("BROWSERLESS_IO_TOKEN");

  if (!browserlessToken) {
    throw new Error("Missing browserless.io token");
  }

  const browser = await puppeteer.connect({
    browserWSEndpoint: `wss://production-sfo.browserless.io?token=${browserlessToken}`
  });

  const page = await browser.newPage();

  try {
    // Set content to the page
    await page.setContent(resume.html);

    // Wait for all fonts to be loaded
    await page.evaluateHandle('document.fonts.ready');

    // Set page size to A4
    await page.setViewport({
      width: 794, // A4 width in pixels (72 dpi)
      height: 1123, // A4 height in pixels (72 dpi)
      deviceScaleFactor: 2, // Higher scale for better quality
    });

    console.log('Generating PDF...');

    // Generate PDF using Chrome DevTools Protocol directly to avoid ReadableStream compatibility issues in Deno
    const client = await page.target().createCDPSession();
    const { data } = await client.send('Page.printToPDF', {
      printBackground: true,
      paperWidth: 8.27,   // A4 width in inches
      paperHeight: 11.69, // A4 height in inches
      marginTop: 0,
      marginBottom: 0,
      marginLeft: 0,
      marginRight: 0,
      pageRanges: '',
    });

    // Convert the returned base64 string to Uint8Array
    const pdf = Uint8Array.from(atob(data), (c) => c.charCodeAt(0));

    console.log('PDF generated.');

    return pdf;
  } finally {
    // Always close the browser
    await browser.close();
  }
}

serve(async (req: Request) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    // Extract resume ID from request body
    const { resumeId } = await req.json();

    if (!resumeId) {
      return new Response(
        JSON.stringify({ error: 'Resume ID is required' }),
        {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      );
    }

    // Get access token from request header
    const accessToken = req.headers.get('x-supabase-auth') || req.headers.get('authorization')?.replace('Bearer ', '');

    if (!accessToken) {
      return new Response(
        JSON.stringify({ error: 'Authentication required' }),
        {
          status: 401,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      );
    }

    // Create Supabase client
    const supabase = createSupabaseClient();

    // Get user with the provided token
    const { data: { user }, error: userError } = await supabase.auth.getUser(accessToken);

    if (userError || !user) {
      console.error('Error getting user with token:', userError);
      return new Response(
        JSON.stringify({ error: 'Invalid authentication token' }),
        {
          status: 401,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      );
    }

    // Get the resume from database
    const { data: resume, error: fetchError } = await supabase
      .from('resumes')
      .select('id, user_id, html, structured_data, created_at, data, tokens_deducted')
      .eq('id', resumeId)
      .eq('user_id', user.id) // Ensure user can only access their own resumes
      .single();

    if (fetchError || !resume) {
      console.error('Error fetching resume:', fetchError);
      return new Response(
        JSON.stringify({ error: 'Resume not found or access denied' }),
        {
          status: 404,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      );
    }

    // Check token balance before proceeding with download (for manual resumes)
    const tokenCheck = await checkManualResumeTokens(supabase, resume);
    if (!tokenCheck.hasTokens) {
      return new Response(
        JSON.stringify({ error: tokenCheck.error }),
        {
          status: 402, // Payment Required
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      );
    }

    // Check if PDF already exists in storage
    const pdfExists = await checkPdfExists(supabase, resumeId);
    let pdfBuffer: Uint8Array;

    try {
      if (pdfExists) {
        // Download existing PDF from storage
        console.log(`PDF exists for resume ${resumeId}, downloading from storage`);
        const existingPdf = await downloadPdfFromStorage(supabase, resumeId);

        if (existingPdf) {
          pdfBuffer = existingPdf;
        } else {
          // If download fails, fall back to generating new PDF
          console.log(`Failed to download existing PDF for resume ${resumeId}, generating new one`);
          pdfBuffer = await generatePdf(resume);
          // Try to upload the newly generated PDF
          await uploadPdfToStorage(supabase, resumeId, pdfBuffer);
        }
      } else {
        // Generate new PDF
        console.log(`PDF does not exist for resume ${resumeId}, generating new one`);
        pdfBuffer = await generatePdf(resume);
        // Upload to storage for future use
        const uploadSuccess = await uploadPdfToStorage(supabase, resumeId, pdfBuffer);
        if (!uploadSuccess) {
          console.warn(`Failed to upload PDF to storage for resume ${resumeId}`);
        }
      }
    } catch (pdfError) {
      console.error('PDF generation/retrieval error:', pdfError);
      return new Response(
        JSON.stringify({
          error: 'Failed to generate PDF. Please try again later.'
        }),
        {
          status: 500,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      );
    }

    // Deduct tokens for manual resume download (only for manual resumes)
    try {
      await deductManualResumeTokens(supabase, resume);
    } catch (tokenError) {
      console.error('Token deduction failed, but PDF download was successful:', tokenError);
      // Don't fail the entire operation if token deduction fails
    }

    // Return PDF as response
    return new Response(pdfBuffer, {
      status: 200,
      headers: {
        ...corsHeaders,
        'Content-Type': 'application/pdf',
        'Content-Disposition': `attachment; filename="CV_Gigsta_${resumeId}.pdf"`,
        'Cache-Control': 'no-cache, no-store, must-revalidate',
      },
    });

  } catch (error) {
    console.error('Error downloading resume:', error);

    return new Response(
      JSON.stringify({
        success: false,
        error: 'Failed to download resume. Please try again.'
      }),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );
  }
});