/// <reference types="https://deno.land/x/types/index.d.ts" />
declare const Deno: any;

import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { GoogleGenerativeAI } from "https://esm.sh/@google/generative-ai@0.17.1"

// Types for letter generation
interface StructuredLetterData {
  metadata: {
    generatedAt: string;
    lastModified: string;
    templateId: string;
    language: string;
  };
  header: {
    date: string;
  };
  subject: {
    prefix: string;
    position: string;
  };
  recipient: {
    salutation: string;
    title: string;
    company?: string;
    address?: string[];
  };
  body: {
    opening: string;
    paragraphs: string[];
    closing: string;
  };
  signature: {
    farewell: string;
    name: string;
  };
  attachments?: string[];
}

interface ResumeInput {
  file?: {
    buffer?: string; // base64 encoded
    extractedText?: string;
    mimeType: string;
  };
  manual?: {
    fullName: string;
    professionalTitle: string;
    professionalSummary: string;
    mostRecentJob: {
      title: string;
      company: string;
      achievements: string;
    };
    skills: string;
  };
}

interface JobInput {
  description?: string;
  image?: {
    buffer: string; // base64 encoded
    mimeType: string;
  };
}

interface GenerateLetterRequest {
  letterId: string;
  resumeInput: ResumeInput;
  jobInput: JobInput;
  templateId: string;
}

/**
 * Converts a base64 file to a generative part for the AI model
 */
function fileToGenerativePart(base64Data: string, mimeType: string) {
  return {
    inlineData: {
      data: base64Data,
      mimeType
    }
  };
}

/**
 * Creates the structured prompt for AI generation
 */
function createStructuredPrompt(
  date: string,
  templateId: string,
  language: 'id' | 'en',
  jobDescription?: string,
): string {
  const isIndonesian = language === 'id';
  
  const baseStructure = isIndonesian ? {
    opening: 'Dengan hormat,',
    farewell: 'Hormat saya,',
    subjectPrefix: 'Perihal: Lamaran Pekerjaan sebagai',
    salutation: 'Yth.',
    recipientTitle: 'Bapak/Ibu Bagian Sumber Daya Manusia',
    closing: 'Atas perhatian dan waktu yang Bapak/Ibu berikan, saya ucapkan terima kasih.',
    attachments: ['Curriculum Vitae', 'Portofolio']
  } : {
    opening: 'Dear Sir/Madam,',
    farewell: 'Sincerely,',
    subjectPrefix: 'Subject: Job Application for',
    salutation: 'Dear',
    recipientTitle: 'Hiring Manager',
    closing: 'Thank you for your time and consideration.',
    attachments: ['Resume', 'Portfolio']
  };

  return `
You are a professional job application letter writer. Generate a structured JSON object for a formal job application letter ${isIndonesian ? 'in Bahasa Indonesia' : 'in English'}.

First, analyze the resume and job description thoroughly to identify:
1. Skills EXPLICITLY mentioned in the resume that match the job requirements
2. Skills IMPLICITLY suggested by the resume
3. Related skills in the resume that could TRANSFER to the job requirements
4. Education, certifications, and relevant experience from the resume
5. Company name and position title from the job information

Generate a JSON object with this EXACT structure:
{
  "metadata": {
    "generatedAt": "${new Date().toISOString()}",
    "lastModified": "${new Date().toISOString()}",
    "templateId": "${templateId}",
    "language": "${language}"
  },
  "header": {
    "date": "${date}"
  },
  "subject": {
    "prefix": "${baseStructure.subjectPrefix}",
    "position": "[Extract exact position title from job description]"
  },
  "recipient": {
    "salutation": "${baseStructure.salutation}",
    "title": "${baseStructure.recipientTitle}",
    "company": "[Extract company name if available]",
    "address": "[Extract company address as array if available, or omit if not found]"
  },
  "body": {
    "opening": "${baseStructure.opening}",
    "paragraphs": [
      "[First paragraph: State purpose and position applied for clearly]",
      "[Second paragraph: Highlight relevant education, skills, and experiences that match job requirements]",
      "[Third paragraph: Show enthusiasm, mention company specifically if possible, and express desire to contribute]"
    ],
    "closing": "${baseStructure.closing}"
  },
  "signature": {
    "farewell": "${baseStructure.farewell}",
    "name": "[Extract candidate name from resume]",
  }
}

Job Information: ${jobDescription || '[A job posting image is provided. Analyze it to identify job requirements, responsibilities, qualifications, company name, and position title]'}

CRITICAL REQUIREMENTS:
1. The ENTIRE letter character count (including spaces) should be around 1300-1600 characters, not more, not less. Make it concise, but still formal and complete
2. Extract the EXACT position title and company name from the job information
3. NEVER mention lack of experience or use negative phrases
4. Focus ONLY on candidate strengths and relevant experience
5. Generate exactly 3 substantial paragraphs for the body. DO NOT include additional paragraphs
6. Use ${isIndonesian ? 'formal Indonesian business correspondence' : 'professional English business'} style
7. If company address is not clearly specified, omit the address field entirely
8. Extract candidate name accurately from the resume

Return ONLY the JSON object without any markdown formatting, code blocks, or additional text.
  `;
}

/**
 * Generates structured letter data using the Gemini AI model
 */
async function generateStructuredLetterData(
  resumeInput: ResumeInput,
  jobInput: JobInput,
  googleAIKey: string,
  templateId: string = 'plain-text',
  language: 'id' | 'en' = 'id'
): Promise<StructuredLetterData> {
  // Initialize Google AI client
  const genAI = new GoogleGenerativeAI(googleAIKey);
  const model = genAI.getGenerativeModel({ model: "gemini-2.5-flash" });

  // Validate inputs
  if (!resumeInput.file && !resumeInput.manual) {
    throw new Error('Either resume file or manual resume data must be provided');
  }

  if (!jobInput.description && !jobInput.image) {
    throw new Error('Either job description text or job posting image must be provided');
  }

  // Build the prompt parts
  const parts: any[] = [];

  // Add resume information
  if (resumeInput.file) {
    // Validate file type
    const supportedTypes = [
      'application/pdf',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'text/plain',
      'image/png',
      'image/jpeg',
      'image/jpg'
    ];

    console.log('Resume Mime Type:', resumeInput.file.mimeType)

    if (!supportedTypes.includes(resumeInput.file.mimeType)) {
      throw new Error('Unsupported resume file format. Please use PDF, DOCX, PNG, JPG, or JPEG files.');
    }

    if (resumeInput.file.mimeType === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') {
      // For DOCX files, use the pre-processed extracted text
      if (resumeInput.file.extractedText) {
        parts.push({ text: `Current Resume Content:\n${resumeInput.file.extractedText}` });
      } else {
        throw new Error('DOCX file must include extractedText property');
      }
    } else if (['image/png', 'image/jpeg', 'image/jpg'].includes(resumeInput.file.mimeType)) {
      // For image formats, use the file buffer
      if (!resumeInput.file.buffer) {
        throw new Error('Image files must include buffer property');
      }
      parts.push({ text: 'Current Resume (as image):' });
      parts.push(fileToGenerativePart(resumeInput.file.buffer, resumeInput.file.mimeType));
    } else {
      // For PDF and TXT files, use the file buffer
      if (!resumeInput.file.buffer) {
        throw new Error('PDF and TXT files must include buffer property');
      }
      parts.push({ text: 'Current Resume:' });
      parts.push(fileToGenerativePart(resumeInput.file.buffer, resumeInput.file.mimeType));
    }
  } else if (resumeInput.manual) {
    // Use manual input data
    const manualData = resumeInput.manual;
    parts.push({
      text: `Current Resume Information:
Full Name: ${manualData.fullName}
Professional Title: ${manualData.professionalTitle}
Professional Summary: ${manualData.professionalSummary}
Most Recent Job:
  - Title: ${manualData.mostRecentJob.title}
  - Company: ${manualData.mostRecentJob.company}
  - Achievements: ${manualData.mostRecentJob.achievements}
Skills: ${manualData.skills}`
    });
  }

  // Add job information
  if (jobInput.description) {
    parts.push({ text: `Job Description:\n${jobInput.description}` });
  }

  if (jobInput.image) {
    // Validate image type
    if (!jobInput.image.mimeType.startsWith('image/')) {
      throw new Error('Unsupported job image format. Only image formats are accepted.');
    }

    parts.push({ text: 'Job Posting Image:' });
    parts.push(fileToGenerativePart(jobInput.image.buffer, jobInput.image.mimeType));
  }

  // Format current date
  const date = new Date().toLocaleDateString(language === 'id' ? 'id-ID' : 'en-US', {
    day: 'numeric',
    month: 'long',
    year: 'numeric',
    timeZone: language === 'id' ? 'Asia/Jakarta' : 'UTC',
  });

  // Create the structured prompt
  const prompt = createStructuredPrompt(date, templateId, language, jobInput.description);
  parts.push({ text: prompt });

  // Generate the content using Google AI
  const result = await model.generateContent(parts);
  const response = await result.response;
  const responseText = response.text();

  // Parse the JSON response
  let parsedResponse: StructuredLetterData;
  try {
    // Try to extract JSON from the response
    const jsonMatch = responseText.match(/\{[\s\S]*\}/);
    if (jsonMatch) {
      parsedResponse = JSON.parse(jsonMatch[0]);
    } else {
      throw new Error('No JSON found in response');
    }
  } catch (parseError) {
    console.error('Failed to parse AI response as JSON:', parseError);
    console.error('Response text:', responseText);

    // Fallback: create basic structured data
    const fallbackStructuredData: StructuredLetterData = {
      metadata: {
        generatedAt: new Date().toISOString(),
        lastModified: new Date().toISOString(),
        templateId,
        language
      },
      header: {
        date
      },
      subject: {
        prefix: language === 'id' ? 'Perihal: Lamaran Pekerjaan sebagai' : 'Subject: Job Application for',
        position: 'Position'
      },
      recipient: {
        salutation: language === 'id' ? 'Yth.' : 'Dear',
        title: language === 'id' ? 'Bapak/Ibu Bagian Sumber Daya Manusia' : 'Hiring Manager'
      },
      body: {
        opening: language === 'id' ? 'Dengan hormat,' : 'Dear Sir/Madam,',
        paragraphs: [
          'First paragraph content here.',
          'Second paragraph content here.',
          'Third paragraph content here.'
        ],
        closing: language === 'id' ? 'Atas perhatian dan waktu yang Bapak/Ibu berikan, saya ucapkan terima kasih.' : 'Thank you for your time and consideration.'
      },
      signature: {
        farewell: language === 'id' ? 'Hormat saya,' : 'Sincerely,',
        name: 'Candidate Name'
      }
    };

    return fallbackStructuredData;
  }

  // Validate the parsed response structure
  if (!parsedResponse.metadata || typeof parsedResponse.metadata !== 'object') {
    console.warn('Invalid response format: metadata is missing or not an object');
  }

  if (!parsedResponse.body || !parsedResponse.body.paragraphs || !Array.isArray(parsedResponse.body.paragraphs)) {
    console.warn('Invalid response format: body paragraphs are missing or not an array');
  }

  return parsedResponse;
}

/**
 * Simple Template Engine for Deno
 * Replaces Handlebars functionality with basic string interpolation
 */
interface TemplateData {
  date: string;
  subject: string;
  recipientLines: string[];
  opening: string;
  paragraphs: string[];
  closing: string;
  farewell: string;
  signatureName: string;
  additionalInfo?: string;
}

/**
 * Convert StructuredLetterData to template data format
 */
function convertToTemplateData(data: StructuredLetterData): TemplateData {
  const recipientLines: string[] = [];
  
  // Build recipient lines
  if (data.recipient.salutation && data.recipient.title) {
    recipientLines.push(`${data.recipient.salutation} ${data.recipient.title}`);
  }
  if (data.recipient.company) {
    recipientLines.push(data.recipient.company);
  }
  if (data.recipient.address) {
    recipientLines.push(...data.recipient.address);
  }

  // Build subject line
  const subject = `${data.subject.prefix} ${data.subject.position}`;

  return {
    date: data.header.date,
    subject,
    recipientLines,
    opening: data.body.opening,
    paragraphs: data.body.paragraphs,
    closing: data.body.closing,
    farewell: data.signature.farewell,
    signatureName: data.signature.name,
    additionalInfo: data.attachments?.join(', ')
  };
}

/**
 * Main Edge Function handler
 */
serve(async (req: any) => {
  const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  }

  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    console.log('=== Generate Letter Edge Function Started ===')
    console.log('Request method:', req.method)
    console.log('Request URL:', req.url)

    // Get environment variables
    const supabaseUrl = Deno.env.get('SUPABASE_URL') ?? ''
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    const supabaseAnonKey = Deno.env.get('NEXT_PUBLIC_SUPABASE_ANON_KEY') ?? ''
    const googleAIKey = Deno.env.get('GOOGLE_AI_API_KEY') ?? ''

    console.log('Environment variables check:')
    console.log('- SUPABASE_URL:', supabaseUrl ? 'SET' : 'MISSING')
    console.log('- SUPABASE_SERVICE_ROLE_KEY:', supabaseServiceKey ? 'SET' : 'MISSING')
    console.log('- SUPABASE_ANON_KEY:', supabaseAnonKey ? 'SET' : 'MISSING')
    console.log('- GOOGLE_AI_API_KEY:', googleAIKey ? 'SET' : 'MISSING')

    if (!supabaseUrl || !supabaseServiceKey || !supabaseAnonKey || !googleAIKey) {
      const missingVars: string[] = []
      if (!supabaseUrl) missingVars.push('SUPABASE_URL')
      if (!supabaseServiceKey) missingVars.push('SUPABASE_SERVICE_ROLE_KEY')
      if (!supabaseAnonKey) missingVars.push('SUPABASE_ANON_KEY')
      if (!googleAIKey) missingVars.push('GOOGLE_AI_API_KEY')

      const errorMsg = `Missing required environment variables: ${missingVars.join(', ')}`
      console.error('Environment error:', errorMsg)
      throw new Error(errorMsg)
    }

    // Get authorization header
    const authHeader = req.headers.get('Authorization')
    console.log('Authorization header:', authHeader ? 'PROVIDED' : 'MISSING')

    if (!authHeader) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Missing authorization header',
          timestamp: new Date().toISOString()
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 401,
        }
      )
    }

    // Create Supabase client with user session
    console.log('Creating Supabase client for user authentication...')
    const supabaseUser = createClient(supabaseUrl, supabaseAnonKey, {
      global: {
        headers: {
          Authorization: authHeader
        }
      }
    })

    // Verify user authentication
    const { data: { user }, error: userError } = await supabaseUser.auth.getUser()

    if (userError || !user) {
      console.error('User authentication failed:', userError)
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Authentication failed',
          timestamp: new Date().toISOString()
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 401,
        }
      )
    }

    console.log('User authenticated successfully:', user.id)

    // Create Supabase service client for database operations
    console.log('Creating Supabase service client...')
    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Parse request body
    console.log('Parsing request body...')
    let requestBody: GenerateLetterRequest
    try {
      const rawBody = await req.text()
      console.log('Raw body length:', rawBody.length)

      if (!rawBody || rawBody.trim() === '') {
        console.error('Request body is empty')
        throw new Error('Request body is empty')
      }

      requestBody = JSON.parse(rawBody)
      console.log('Request body parsed successfully')
      console.log('Request body keys:', Object.keys(requestBody))
    } catch (parseError) {
      console.error('Failed to parse request body:', parseError)
      throw new Error(`Failed to parse request body: ${(parseError as any).message}`)
    }

    const { letterId, resumeInput, jobInput, templateId } = requestBody
    console.log('Request parameters:')
    console.log('- letterId:', letterId ? 'PROVIDED' : 'MISSING')
    console.log('- resumeInput:', resumeInput ? 'PROVIDED' : 'MISSING')
    console.log('- jobInput:', jobInput ? 'PROVIDED' : 'MISSING')
    console.log('- templateId:', templateId ? 'PROVIDED' : 'MISSING')

    if (!letterId || !resumeInput || !jobInput) {
      const missingParams: string[] = []
      if (!letterId) missingParams.push('letterId')
      if (!resumeInput) missingParams.push('resumeInput')
      if (!jobInput) missingParams.push('jobInput')

      const errorMsg = `Missing required parameters: ${missingParams.join(', ')}`
      console.error('Parameter validation error:', errorMsg)
      throw new Error(errorMsg)
    }

    // Verify that the letter belongs to the authenticated user
    console.log('Verifying letter ownership...')
    const { data: letterData, error: letterCheckError } = await supabase
      .from('letters')
      .select('user_id, status')
      .eq('id', letterId)
      .single()

    if (letterCheckError || !letterData) {
      console.error('Letter not found:', letterCheckError)
      throw new Error('Letter not found or access denied')
    }

    if (letterData.user_id !== user.id) {
      console.error('Letter ownership mismatch:', {
        letterUserId: letterData.user_id,
        authenticatedUserId: user.id
      })
      throw new Error('Access denied: Letter does not belong to authenticated user')
    }

    console.log('Letter ownership verified successfully')

    // Update status to 'processing'
    console.log('Updating letter status to processing...')
    const { error: updateStatusError } = await supabase
      .from('letters')
      .update({
        status: 'processing',
        updated_at: new Date().toISOString()
      })
      .eq('id', letterId)

    if (updateStatusError) {
      console.error('Error updating letter status to processing:', updateStatusError)
      throw new Error(`Failed to update letter status: ${updateStatusError.message}`)
    }

    try {
      // Generate the letter
      console.log('Generating structured letter data...')
      const structuredData = await generateStructuredLetterData(
        resumeInput, 
        jobInput, 
        googleAIKey, 
        templateId || 'plain-text',
        'id' // Default to Indonesian
      )

      console.log('Letter generation completed successfully')
      console.log('Generated data keys:', Object.keys(structuredData))

      // Update the database with the generated content
      console.log('Updating letter with generated content...')
      const { error: updateError } = await supabase
        .from('letters')
        .update({
          structured_data: structuredData,
          status: 'done',
          updated_at: new Date().toISOString()
        })
        .eq('id', letterId)

      if (updateError) {
        console.error('Error updating letter with generated content:', updateError)
        throw new Error(`Database update failed: ${updateError.message}`)
      }

      console.log('Letter generation process completed successfully')
      return new Response(
        JSON.stringify({
          success: true,
          letterId,
          message: 'Letter generated successfully',
          structuredData,
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 200,
        }
      )

    } catch (generationError) {
      console.error('Letter generation failed:', generationError)

      // Get the original data and add error message to it
      console.log('Updating letter with error status...')
      try {
        // Update status to 'error' with error message
        await supabase
          .from('letters')
          .update({
            status: 'error',
            updated_at: new Date().toISOString()
          })
          .eq('id', letterId)

        console.log('Letter error status updated successfully')
      } catch (errorUpdateError) {
        console.error('Failed to update letter error status:', errorUpdateError)
      }

      throw generationError
    }

  } catch (error) {
    console.error('=== Edge Function Error ===')
    console.error('Error details:', {
      name: (error as any).name,
      message: (error as any).message,
      stack: (error as any).stack
    })

    return new Response(
      JSON.stringify({
        success: false,
        error: (error as any).message || 'Unknown error occurred',
        errorType: (error as any).name || 'UnknownError',
        timestamp: new Date().toISOString()
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500,
      }
    )
  }
})