/**
 * Visual tests for letter templates
 * Tests template rendering, HTML structure, and placeholder replacements
 */

import {
  fillLetterTemplate,
  getAvailableLetterTemplateIds,
  isLetterTemplateAvailable
} from '@/utils/letter-template-engine';

import {
  applicationLetterTemplates,
  getTemplateById,
  LetterTemplate
} from '@/utils/letter-templates/applicationLetterTemplates';

import {
  StructuredLetterData,
  convertToLetterTemplateData,
  createDefaultStructuredLetterData
} from '@/types/letter-structured';

import {
  createSampleStructuredLetterData,
  templateTestUtils,
  errorTestUtils
} from './utils/test-helpers';

// Mock filesystem for template loading
jest.mock('fs', () => ({
  existsSync: jest.fn(() => true),
  readFileSync: jest.fn((path: string) => {
    if (path.includes('plain-text.hbs')) {
      return `
<!DOCTYPE html>
<html lang="{{language}}">
<head>
    <meta charset="UTF-8">
    <title>{{subject}}</title>
</head>
<body>
    <div class="letter">
        <div class="header">
            <div class="date">{{date}}</div>
        </div>
        <div class="subject">{{subject}}</div>
        <div class="recipient">
            {{#each recipientLines}}
            <p>{{this}}</p>
            {{/each}}
        </div>
        <div class="body">
            <p>{{opening}}</p>
            {{#each paragraphs}}
            <p>{{this}}</p>
            {{/each}}
            <p>{{closing}}</p>
        </div>
        <div class="signature">
            <p>{{farewell}}</p>
            <p>{{signatureName}}</p>
            {{#if additionalInfo}}
            <p>{{additionalInfo}}</p>
            {{/if}}
        </div>
    </div>
</body>
</html>`;
    }
    
    // Default template for other template IDs
    return `
<!DOCTYPE html>
<html>
<head><title>{{subject}}</title></head>
<body>
    <h1>{{subject}}</h1>
    <div class="date">{{date}}</div>
    <div class="recipient">{{#each recipientLines}}<p>{{this}}</p>{{/each}}</div>
    <div class="content">
        <p>{{opening}}</p>
        {{#each paragraphs}}<p>{{this}}</p>{{/each}}
        <p>{{closing}}</p>
    </div>
    <div class="signature">
        <p>{{farewell}}</p>
        <p>{{signatureName}}</p>
    </div>
</body>
</html>`;
  })
}));

jest.mock('handlebars', () => ({
  compile: jest.fn((template: string) => {
    return (data: any) => {
      let rendered = template;
      
      // Replace simple placeholders
      rendered = rendered.replace(/\{\{(\w+)\}\}/g, (match, key) => {
        return data[key] || match;
      });
      
      // Handle each loops for arrays
      rendered = rendered.replace(/\{\{#each\s+(\w+)\}\}([\s\S]*?)\{\{\/each\}\}/g, (match, arrayName, content) => {
        const array = data[arrayName];
        if (Array.isArray(array)) {
          return array.map(item => content.replace(/\{\{this\}\}/g, item)).join('');
        }
        return '';
      });
      
      // Handle if conditions
      rendered = rendered.replace(/\{\{#if\s+(\w+)\}\}([\s\S]*?)\{\{\/if\}\}/g, (match, condition, content) => {
        return data[condition] ? content : '';
      });
      
      return rendered;
    };
  }),
  registerHelper: jest.fn()
}));

describe('Letter Templates Visual Tests', () => {
  const sampleData = createSampleStructuredLetterData();
  
  describe('Template Availability', () => {
    it('should have all expected templates available', () => {
      const availableIds = getAvailableLetterTemplateIds();
      const expectedTemplates = [
        'plain-text',
        'classic-blue',
        'professional-classic',
        'minimalist-sidebar',
        'minimalist-border-frame',
        'minimalist-accent',
        'minimalist-circular-accents'
      ];

      expectedTemplates.forEach(templateId => {
        expect(availableIds).toContain(templateId);
        expect(isLetterTemplateAvailable(templateId)).toBe(true);
      });
    });

    it('should return false for non-existent templates', () => {
      const fakeTemplateIds = [
        'non-existent-template',
        'fake-template',
        'invalid-template-id'
      ];

      fakeTemplateIds.forEach(templateId => {
        expect(isLetterTemplateAvailable(templateId)).toBe(false);
      });
    });
  });

  describe('Individual Template Rendering', () => {
    applicationLetterTemplates.forEach(template => {
      describe(`Template: ${template.name} (${template.id})`, () => {
        it('should render successfully with complete data', () => {
          const html = fillLetterTemplate(template, sampleData);
          
          expect(html).toBeDefined();
          expect(typeof html).toBe('string');
          expect(html.length).toBeGreaterThan(100);
          
          const structure = templateTestUtils.validateHtmlStructure(html);
          expect(structure.hasDoctype).toBe(true);
          expect(structure.hasHtmlTag).toBe(true);
          expect(structure.hasHeadTag).toBe(true);
          expect(structure.hasBodyTag).toBe(true);
          expect(structure.isEmpty).toBe(false);
          expect(structure.isValidLength).toBe(true);
        });

        it('should contain all required content placeholders', () => {
          const html = fillLetterTemplate(template, sampleData);
          const templateData = convertToLetterTemplateData(sampleData);
          const replacements = templateTestUtils.checkPlaceholderReplacements(html, templateData);

          expect(replacements.hasDate).toBe(true);
          expect(replacements.hasSubject).toBe(true);
          expect(replacements.hasSignatureName).toBe(true);
          expect(replacements.hasFarewell).toBe(true);
          expect(replacements.hasOpening).toBe(true);
          expect(replacements.hasParagraphs).toBe(true);
          expect(replacements.hasRecipientInfo).toBe(true);
        });

        it('should handle missing optional fields gracefully', () => {
          const incompleteData = createSampleStructuredLetterData({
            signature: {
              farewell: 'Sincerely,',
              name: 'Test User'
              // Missing additionalInfo
            },
            recipient: {
              salutation: 'Dear',
              title: 'Hiring Manager'
              // Missing company and address
            }
          });

          expect(() => {
            const html = fillLetterTemplate(template, incompleteData);
            expect(html).toBeDefined();
            expect(html.length).toBeGreaterThan(50);
          }).not.toThrow();
        });

        it('should preserve template-specific styling and structure', () => {
          const html = fillLetterTemplate(template, sampleData);
          
          // Check for basic HTML structure
          expect(html).toMatch(/<html[^>]*>/);
          expect(html).toMatch(/<head>/);
          expect(html).toMatch(/<body>/);
          expect(html).toMatch(/<\/html>/);
          
          // Template should have some styling or structure
          const hasStructure = html.includes('<div') || 
                              html.includes('<section') || 
                              html.includes('<style') ||
                              html.includes('class=');
          expect(hasStructure).toBe(true);
        });

        it('should render different languages correctly', () => {
          // Test Indonesian
          const indonesianData = createSampleStructuredLetterData({
            metadata: { ...sampleData.metadata, language: 'id' }
          });
          const indonesianHtml = fillLetterTemplate(template, indonesianData);
          
          // Test English  
          const englishData = createSampleStructuredLetterData({
            metadata: { ...sampleData.metadata, language: 'en' },
            body: {
              opening: 'Dear Sir/Madam,',
              paragraphs: ['English paragraph content'],
              closing: 'Thank you for your consideration.'
            },
            signature: {
              farewell: 'Sincerely,',
              name: 'John Doe'
            }
          });
          const englishHtml = fillLetterTemplate(template, englishData);

          expect(indonesianHtml).toBeDefined();
          expect(englishHtml).toBeDefined();
          expect(indonesianHtml).not.toBe(englishHtml);
        });

        it('should handle special characters and encoding correctly', () => {
          const specialCharData = createSampleStructuredLetterData({
            signature: {
              farewell: 'Hormat saya,',
              name: 'André José García-López' // Special characters
            },
            recipient: {
              salutation: 'Yth.',
              title: 'Bapak/Ibu HRD',
              company: 'PT Teknologi Inovasi & Solusi'
            },
            body: {
              opening: 'Dengan hormat,',
              paragraphs: [
                'Saya tertarik dengan posisi "Software Engineer" yang membutuhkan keahlian C++ & JavaScript.'
              ],
              closing: 'Terima kasih atas perhatian Bapak/Ibu.'
            }
          });

          const html = fillLetterTemplate(template, specialCharData);
          
          expect(html).toContain('André José García-López');
          expect(html).toContain('PT Teknologi Inovasi & Solusi');
          expect(html).toContain('"Software Engineer"');
          expect(html).toContain('C++ & JavaScript');
        });
      });
    });
  });

  describe('Template Comparison and Consistency', () => {
    it('should render consistently across multiple calls', () => {
      const template = getTemplateById('plain-text');
      if (!template) throw new Error('Plain text template not found');

      const html1 = fillLetterTemplate(template, sampleData);
      const html2 = fillLetterTemplate(template, sampleData);
      const html3 = fillLetterTemplate(template, sampleData);

      expect(html1).toBe(html2);
      expect(html2).toBe(html3);
    });

    it('should render different templates with different output', () => {
      const templates = applicationLetterTemplates.slice(0, 3); // Test first 3 templates
      const renderedHtmls = templates.map(template => 
        fillLetterTemplate(template, sampleData)
      );

      // Each template should produce different HTML
      for (let i = 0; i < renderedHtmls.length; i++) {
        for (let j = i + 1; j < renderedHtmls.length; j++) {
          expect(renderedHtmls[i]).not.toBe(renderedHtmls[j]);
        }
      }
    });

    it('should maintain consistent content across different templates', () => {
      const templates = applicationLetterTemplates.slice(0, 2);
      const renderedHtmls = templates.map(template => 
        fillLetterTemplate(template, sampleData)
      );

      // All templates should contain the core content
      renderedHtmls.forEach(html => {
        const textContent = templateTestUtils.extractTextContent(html);
        
        expect(textContent).toContain(sampleData.signature.name);
        expect(textContent).toContain(sampleData.body.opening);
        expect(textContent).toContain(sampleData.signature.farewell);
        sampleData.body.paragraphs.forEach(paragraph => {
          expect(textContent).toContain(paragraph);
        });
      });
    });
  });

  describe('Template Data Variations', () => {
    const template = getTemplateById('plain-text');
    if (!template) throw new Error('Plain text template not found');

    it('should handle minimal required data', () => {
      const minimalData = createDefaultStructuredLetterData('plain-text', 'id');
      minimalData.subject.position = 'Test Position';
      minimalData.signature.name = 'Test User';
      minimalData.body.paragraphs = ['Test paragraph'];

      const html = fillLetterTemplate(template, minimalData);
      
      expect(html).toBeDefined();
      expect(html).toContain('Test Position');
      expect(html).toContain('Test User');
      expect(html).toContain('Test paragraph');
    });

    it('should handle multiple paragraphs correctly', () => {
      const multiParagraphData = createSampleStructuredLetterData({
        body: {
          opening: 'Dear Hiring Manager,',
          paragraphs: [
            'First paragraph with introduction.',
            'Second paragraph with experience details.',
            'Third paragraph with skills and qualifications.',
            'Fourth paragraph with enthusiasm and closing.'
          ],
          closing: 'Thank you for your consideration.'
        }
      });

      const html = fillLetterTemplate(template, multiParagraphData);
      const textContent = templateTestUtils.extractTextContent(html);

      multiParagraphData.body.paragraphs.forEach(paragraph => {
        expect(textContent).toContain(paragraph);
      });
    });

    it('should handle multiple recipient lines', () => {
      const multiRecipientData = createSampleStructuredLetterData({
        recipient: {
          salutation: 'Yth.',
          title: 'Bapak/Ibu Manager HRD',
          company: 'PT Teknologi Canggih Indonesia',
          address: [
            'Gedung Plaza Indonesia, Lantai 25',
            'Jl. M.H. Thamrin Kav. 28-30',
            'Jakarta Pusat 10350',
            'DKI Jakarta'
          ]
        }
      });

      const html = fillLetterTemplate(template, multiRecipientData);
      const textContent = templateTestUtils.extractTextContent(html);

      expect(textContent).toContain('Bapak/Ibu Manager HRD');
      expect(textContent).toContain('PT Teknologi Canggih Indonesia');
      expect(textContent).toContain('Gedung Plaza Indonesia');
      expect(textContent).toContain('Jakarta Pusat 10350');
      expect(textContent).toContain('DKI Jakarta');
    });

    it('should handle empty optional arrays gracefully', () => {
      const emptyArrayData = createSampleStructuredLetterData({
        attachments: [], // Empty attachments
        recipient: {
          salutation: 'Dear',
          title: 'Hiring Manager'
          // No address array
        }
      });

      expect(() => {
        const html = fillLetterTemplate(template, emptyArrayData);
        expect(html).toBeDefined();
      }).not.toThrow();
    });
  });

  describe('Error Handling in Templates', () => {
    const template = getTemplateById('plain-text');
    if (!template) throw new Error('Plain text template not found');

    it('should handle malformed data gracefully', () => {
      const malformedData = {
        ...createSampleStructuredLetterData(),
        body: {
          opening: 'Dear Sir/Madam,',
          paragraphs: null, // Invalid: should be array
          closing: 'Thank you.'
        }
      } as any;

      // The template engine should handle this or throw a descriptive error
      expect(() => {
        fillLetterTemplate(template, malformedData);
      }).toThrow();
    });

    it('should provide meaningful error messages for validation failures', () => {
      const invalidData = {
        ...createSampleStructuredLetterData(),
        signature: {
          farewell: '',
          name: '' // Missing required name
        }
      };

      expect(() => {
        fillLetterTemplate(template, invalidData);
      }).toThrow(/validation failed/i);
    });
  });

  describe('Performance and Memory', () => {
    const template = getTemplateById('plain-text');
    if (!template) throw new Error('Plain text template not found');

    it('should render templates efficiently', () => {
      const startTime = performance.now();
      
      for (let i = 0; i < 10; i++) {
        fillLetterTemplate(template, sampleData);
      }
      
      const endTime = performance.now();
      const averageTime = (endTime - startTime) / 10;

      expect(averageTime).toBeLessThan(50); // Average should be under 50ms
    });

    it('should not create memory leaks with repeated renders', () => {
      const initialMemory = process.memoryUsage().heapUsed;
      
      // Render templates multiple times
      for (let i = 0; i < 100; i++) {
        fillLetterTemplate(template, sampleData);
      }
      
      // Force garbage collection if available
      if (global.gc) {
        global.gc();
      }
      
      const finalMemory = process.memoryUsage().heapUsed;
      const memoryIncrease = finalMemory - initialMemory;
      
      // Memory increase should be reasonable (less than 10MB)
      expect(memoryIncrease).toBeLessThan(10 * 1024 * 1024);
    });
  });

  describe('HTML Validation and Structure', () => {
    applicationLetterTemplates.forEach(template => {
      it(`should produce valid HTML structure for ${template.name}`, () => {
        const html = fillLetterTemplate(template, sampleData);
        
        // Basic HTML validation
        expect(html).toMatch(/<!DOCTYPE html>/i);
        expect(html).toMatch(/<html[^>]*>/);
        expect(html).toMatch(/<head>/);
        expect(html).toMatch(/<meta[^>]+charset/i);
        expect(html).toMatch(/<title>/);
        expect(html).toMatch(/<body>/);
        expect(html).toMatch(/<\/body>/);
        expect(html).toMatch(/<\/html>/);
        
        // Check for balanced tags
        const openTags = (html.match(/<[^\/][^>]*>/g) || []).length;
        const closeTags = (html.match(/<\/[^>]*>/g) || []).length;
        const selfClosingTags = (html.match(/<[^>]*\/>/g) || []).length;
        
        // Should have roughly balanced tags (considering self-closing tags)
        expect(Math.abs(openTags - closeTags - selfClosingTags)).toBeLessThan(5);
      });

      it(`should have proper content structure for ${template.name}`, () => {
        const html = fillLetterTemplate(template, sampleData);
        
        // Should have identifiable sections
        const hasDateSection = html.includes(sampleData.header.date);
        const hasSubjectSection = html.includes(sampleData.subject.position);
        const hasBodySection = sampleData.body.paragraphs.some(p => html.includes(p));
        const hasSignatureSection = html.includes(sampleData.signature.name);
        
        expect(hasDateSection).toBe(true);
        expect(hasSubjectSection).toBe(true);
        expect(hasBodySection).toBe(true);
        expect(hasSignatureSection).toBe(true);
      });
    });
  });

  describe('Template Metadata Consistency', () => {
    it('should have consistent template metadata', () => {
      applicationLetterTemplates.forEach(template => {
        expect(template.id).toBeDefined();
        expect(template.name).toBeDefined();
        expect(template.previewDescription).toBeDefined();
        expect(template.templateHtml).toBeDefined();
        expect(template.previewImagePath).toBeDefined();
        expect(typeof template.isPremium).toBe('boolean');
        expect(typeof template.recommended).toBe('boolean');
        expect(typeof template.tokenCost).toBe('number');
        expect(template.fontFamily).toBeDefined();
        
        // ID should be URL-safe
        expect(template.id).toMatch(/^[a-z0-9-]+$/);
        
        // Token cost should be reasonable
        expect(template.tokenCost).toBeGreaterThanOrEqual(0);
        expect(template.tokenCost).toBeLessThan(100);
        
        // Preview image path should be valid
        expect(template.previewImagePath).toMatch(/\.(svg|png|jpg|jpeg)$/i);
      });
    });

    it('should have unique template IDs', () => {
      const ids = applicationLetterTemplates.map(t => t.id);
      const uniqueIds = Array.from(new Set(ids));
      
      expect(ids.length).toBe(uniqueIds.length);
    });

    it('should have at least one free template', () => {
      const freeTemplates = applicationLetterTemplates.filter(t => !t.isPremium);
      expect(freeTemplates.length).toBeGreaterThan(0);
    });

    it('should have at least one premium template', () => {
      const premiumTemplates = applicationLetterTemplates.filter(t => t.isPremium);
      expect(premiumTemplates.length).toBeGreaterThan(0);
    });
  });
});