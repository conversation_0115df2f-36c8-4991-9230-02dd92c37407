/**
 * Test utilities for letter generation testing
 */

import {
  StructuredLetterData,
  LetterTemplateData,
  createDefaultStructuredLetterData,
  convertToLetterTemplateData
} from '@/types/letter-structured';
import { LetterTemplate } from '@/utils/letter-templates/applicationLetterTemplates';

// Jest types
declare global {
  var jest: any;
}

/**
 * Sample structured letter data for testing
 */
export function createSampleStructuredLetterData(
  overrides?: Partial<StructuredLetterData>
): StructuredLetterData {
  const defaultData = createDefaultStructuredLetterData('plain-text', 'id');
  
  const sampleData: StructuredLetterData = {
    ...defaultData,
    subject: {
      prefix: 'Perihal: <PERSON><PERSON>',
      position: 'Full Stack Developer'
    },
    recipient: {
      salutation: 'Yth.',
      title: 'Bapak/Ibu Bagian Sumber Daya Manusia',
      company: 'PT Teknologi Indonesia',
      address: ['Jl. Sudirman No. 123', 'Jakarta Selatan 12190']
    },
    body: {
      opening: 'Dengan hormat,',
      paragraphs: [
        'Berdasarkan informasi lowongan pekerjaan yang tersedia di website perusahaan, saya bermaksud mengajukan diri untuk posisi Full Stack Developer di PT Teknologi Indonesia. Dengan latar belakang pendidikan Teknik Informatika dan pengalaman kerja selama 3 tahun, saya yakin dapat memberikan kontribusi yang optimal bagi perusahaan.',
        'Selama berkarir sebagai developer, saya telah menguasai berbagai teknologi seperti React, Node.js, PostgreSQL, dan AWS. Pengalaman saya meliputi pengembangan aplikasi web full-stack, optimasi database, dan deployment ke cloud infrastructure. Saya juga memiliki kemampuan bekerja dalam tim yang baik dan dapat beradaptasi dengan teknologi baru.',
        'Saya sangat tertarik untuk bergabung dengan PT Teknologi Indonesia karena reputasi perusahaan yang excellent dalam bidang teknologi dan inovasi. Saya siap untuk berkontribusi dalam pengembangan produk-produk teknologi terdepan dan siap menghadapi tantangan yang ada.'
      ],
      closing: 'Atas perhatian dan waktu yang Bapak/Ibu berikan, saya ucapkan terima kasih. Saya berharap dapat diberikan kesempatan untuk interview lebih lanjut.'
    },
    signature: {
      farewell: 'Hormat saya,',
      name: 'John Doe',
      additionalInfo: 'Email: <EMAIL> | Phone: +62 812 3456 7890'
    },
    attachments: ['Curriculum Vitae', 'Portfolio', 'Sertifikat']
  };

  return {
    ...sampleData,
    ...overrides
  };
}

/**
 * Create sample structured letter data with minimal fields (for testing validation)
 */
export function createMinimalStructuredLetterData(): Partial<StructuredLetterData> {
  return {
    metadata: {
      generatedAt: new Date().toISOString(),
      lastModified: new Date().toISOString(),
      templateId: 'plain-text',
      language: 'id'
    },
    header: {
      date: '29 Januari 2024'
    },
    subject: {
      prefix: 'Perihal: Lamaran Pekerjaan sebagai',
      position: 'Developer'
    },
    signature: {
      farewell: 'Hormat saya,',
      name: 'Test User'
    }
  };
}

/**
 * Create sample structured letter data with missing required fields (for error testing)
 */
export function createInvalidStructuredLetterData(): Partial<StructuredLetterData> {
  return {
    metadata: {
      generatedAt: new Date().toISOString(),
      lastModified: new Date().toISOString(),
      templateId: 'plain-text',
      language: 'id'
    },
    // Missing required fields like subject, signature, etc.
    header: {
      date: '29 Januari 2024'
    }
  };
}

/**
 * Create sample template data for testing
 */
export function createSampleTemplateData(overrides?: Partial<LetterTemplateData>): LetterTemplateData {
  const structuredData = createSampleStructuredLetterData();
  const templateData = convertToLetterTemplateData(structuredData);
  
  return {
    ...templateData,
    ...overrides
  };
}

/**
 * Mock API response generators
 */
export const mockApiResponses = {
  /**
   * Mock successful structured letter generation response
   */
  generateStructuredLetterSuccess: (data?: Partial<StructuredLetterData>) => ({
    success: true,
    data: {
      structuredData: createSampleStructuredLetterData(data),
      design: '<html><body>Mock HTML content</body></html>',
      templateId: 'plain-text',
      letterId: 'test-letter-123',
      generationMode: 'structured'
    }
  }),

  /**
   * Mock failed structured letter generation response
   */
  generateStructuredLetterError: (errorMessage = 'Generation failed') => ({
    success: false,
    error: errorMessage
  }),

  /**
   * Mock streaming response data
   */
  streamingResponse: (templateId = 'plain-text') => ({
    success: true,
    data: {
      streaming: {
        enabled: true,
        unified: true,
        structured: true,
        endpoint: '/api/edge/generate-letter-unified',
        data: {
          resumeData: 'Mock resume content',
          resumeMimeType: 'text/plain',
          jobDescription: 'Mock job description',
          templateId,
          userId: 'test-user-123',
          useStructuredGeneration: true
        }
      },
      generationMode: 'structured-streaming'
    }
  })
};

/**
 * Template testing utilities
 */
export const templateTestUtils = {
  /**
   * Create a mock letter template for testing
   */
  createMockTemplate: (id = 'test-template'): LetterTemplate => ({
    id,
    name: `Test Template ${id}`,
    previewDescription: 'Test template for unit testing',
    templateHtml: () => '<html><body>Mock template content</body></html>',
    previewImagePath: `/svgs/${id}.svg`,
    isPremium: false,
    recommended: false,
    tokenCost: 0,
    fontFamily: 'Arial, sans-serif'
  }),

  /**
   * Validate HTML output structure
   */
  validateHtmlStructure: (html: string) => {
    const checks = {
      hasDoctype: html.includes('<!DOCTYPE html>'),
      hasHtmlTag: html.includes('<html'),
      hasHeadTag: html.includes('<head>'),
      hasBodyTag: html.includes('<body>'),
      hasMetaCharset: html.includes('charset="UTF-8"'),
      hasTitle: html.includes('<title>'),
      hasStyles: html.includes('<style>') || html.includes('.css'),
      isEmpty: html.trim().length === 0,
      isValidLength: html.length > 100 && html.length < 50000
    };

    return checks;
  },

  /**
   * Extract text content from HTML (for testing placeholder replacements)
   */
  extractTextContent: (html: string): string => {
    // Simple HTML tag removal for testing (not production-ready)
    return html
      .replace(/<[^>]*>/g, ' ')
      .replace(/\s+/g, ' ')
      .trim();
  },

  /**
   * Check if template contains required placeholders
   */
  checkPlaceholderReplacements: (html: string, templateData: LetterTemplateData) => {
    const textContent = templateTestUtils.extractTextContent(html);
    
    return {
      hasDate: textContent.includes(templateData.date),
      hasSubject: textContent.includes(templateData.subject),
      hasSignatureName: textContent.includes(templateData.signatureName),
      hasFarewell: textContent.includes(templateData.farewell),
      hasOpening: textContent.includes(templateData.opening),
      hasParagraphs: templateData.paragraphs.every(p => textContent.includes(p)),
      hasRecipientInfo: templateData.recipientLines.some(line => textContent.includes(line))
    };
  }
};

/**
 * File testing utilities
 */
export const fileTestUtils = {
  /**
   * Create mock resume file buffer for testing
   */
  createMockResumeBuffer: (type: 'pdf' | 'docx' | 'text' = 'text'): ArrayBuffer => {
    const content = type === 'text'
      ? 'John Doe\nSoftware Developer\n\nExperience:\n- 3 years as Full Stack Developer\n- Skills: React, Node.js, PostgreSQL'
      : `Mock ${type.toUpperCase()} file content`;
    
    return new TextEncoder().encode(content).buffer as ArrayBuffer;
  },

  /**
   * Create mock job image buffer for testing
   */
  createMockJobImageBuffer: (): ArrayBuffer => {
    const mockImageData = 'Mock image data representing job posting';
    return new TextEncoder().encode(mockImageData).buffer as ArrayBuffer;
  },

  /**
   * Get MIME type for file extension
   */
  getMimeType: (extension: string): string => {
    const mimeTypes: Record<string, string> = {
      'pdf': 'application/pdf',
      'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'txt': 'text/plain',
      'png': 'image/png',
      'jpg': 'image/jpeg',
      'jpeg': 'image/jpeg'
    };
    
    return mimeTypes[extension.toLowerCase()] || 'application/octet-stream';
  }
};

/**
 * Async testing utilities
 */
export const asyncTestUtils = {
  /**
   * Wait for a specified amount of time (for testing async operations)
   */
  wait: (ms: number): Promise<void> => {
    return new Promise(resolve => setTimeout(resolve, ms));
  },

  /**
   * Wait for a condition to be true (with timeout)
   */
  waitFor: async (
    condition: () => boolean | Promise<boolean>,
    timeout = 5000,
    interval = 100
  ): Promise<void> => {
    const start = Date.now();
    
    while (Date.now() - start < timeout) {
      if (await condition()) {
        return;
      }
      await asyncTestUtils.wait(interval);
    }
    
    throw new Error(`Condition not met within ${timeout}ms`);
  }
};

/**
 * Error testing utilities
 */
export const errorTestUtils = {
  /**
   * Create test errors for different scenarios
   */
  createTestError: (type: 'validation' | 'template' | 'network' | 'ai', message?: string) => {
    const defaultMessages = {
      validation: 'Validation failed: missing required fields',
      template: 'Template rendering failed: invalid template syntax',
      network: 'Network error: failed to connect to API',
      ai: 'AI generation failed: API quota exceeded'
    };

    const error = new Error(message || defaultMessages[type]);
    error.name = `${type.charAt(0).toUpperCase() + type.slice(1)}Error`;
    return error;
  },

  /**
   * Mock console methods for testing error logging
   */
  mockConsole: () => {
    const originalConsole = { ...console };
    const mockMethods = {
      log: jest.fn(),
      warn: jest.fn(),
      error: jest.fn(),
      info: jest.fn()
    };

    Object.assign(console, mockMethods);

    return {
      restore: () => Object.assign(console, originalConsole),
      mocks: mockMethods
    };
  }
};

/**
 * Performance testing utilities
 */
export const performanceTestUtils = {
  /**
   * Measure execution time of a function
   */
  measureTime: async <T>(fn: () => Promise<T> | T): Promise<{ result: T; duration: number }> => {
    const start = performance.now();
    const result = await fn();
    const duration = performance.now() - start;
    
    return { result, duration };
  },

  /**
   * Test memory usage (simplified for Node.js environment)
   */
  measureMemory: <T>(fn: () => T): { result: T; memoryUsed: number } => {
    const memBefore = process.memoryUsage().heapUsed;
    const result = fn();
    const memAfter = process.memoryUsage().heapUsed;
    
    return {
      result,
      memoryUsed: memAfter - memBefore
    };
  }
};