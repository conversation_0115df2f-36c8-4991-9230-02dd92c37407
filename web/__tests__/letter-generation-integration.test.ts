/**
 * Integration tests for end-to-end letter generation flow
 */

import { NextRequest, NextResponse } from 'next/server';
import { POST } from '@/app/api/generate-application-letter/route';
import {
  generateStructuredLetterData,
  ResumeFileInput,
  JobImageInput,
  StructuredLetterGenerationOptions
} from '@/utils/ai-generators/structuredLetterGenerator';
import {
  fillLetterTemplate
} from '@/utils/letter-template-engine';
import { getTemplateById } from '@/utils/letter-templates/applicationLetterTemplates';
import {
  createSampleStructuredLetterData,
  mockApiResponses,
  fileTestUtils,
  asyncTestUtils,
  templateTestUtils
} from './utils/test-helpers';

// Mock external dependencies
jest.mock('@/lib/supabase-server', () => ({
  createClient: jest.fn(() => ({
    auth: {
      getUser: jest.fn().mockResolvedValue({
        data: { user: { id: 'test-user-123' } },
        error: null
      })
    },
    from: jest.fn(() => ({
      select: jest.fn(() => ({
        eq: jest.fn(() => ({
          single: jest.fn().mockResolvedValue({
            data: {
              resume_file_name: 'test-resume.pdf',
              tokens: 100
            },
            error: null
          })
        }))
      })),
      insert: jest.fn(() => ({
        select: jest.fn(() => ({
          single: jest.fn().mockResolvedValue({
            data: { id: 'test-letter-123' },
            error: null
          })
        }))
      })),
      update: jest.fn(() => ({
        eq: jest.fn(() => ({
          select: jest.fn(() => ({
            single: jest.fn().mockResolvedValue({
              data: { id: 'test-letter-123' },
              error: null
            })
          }))
        }))
      }))
    })),
    storage: {
      from: jest.fn(() => ({
        download: jest.fn().mockResolvedValue({
          data: new Blob(['Mock resume content']),
          error: null
        })
      }))
    }
  }))
}));

jest.mock('@/utils/ai-generators/structuredLetterGenerator', () => ({
  generateStructuredLetterData: jest.fn(),
  validateAndFixStructuredData: jest.fn(data => data)
}));

jest.mock('@/utils/letter-template-engine', () => ({
  fillLetterTemplate: jest.fn(),
  getAvailableLetterTemplateIds: jest.fn(() => ['plain-text', 'classic-blue']),
  isLetterTemplateAvailable: jest.fn(() => true),
  testLetterTemplate: jest.fn(() => ({ success: true, htmlLength: 1000 }))
}));

jest.mock('@/utils/letter-templates/applicationLetterTemplates', () => ({
  getTemplateById: jest.fn(() => ({
    id: 'plain-text',
    name: 'Plain Text',
    tokenCost: 10,
    templateHtml: () => '<html><body>Mock template</body></html>'
  }))
}));

jest.mock('@/utils/errorMonitoring', () => ({
  captureApiError: jest.fn()
}));

jest.mock('@/lib/mixpanel-server', () => ({
  trackApiUsage: jest.fn()
}));

describe('Letter Generation Integration Tests', () => {
  let mockGenerateStructuredLetterData: jest.MockedFunction<typeof generateStructuredLetterData>;
  let mockFillLetterTemplate: jest.MockedFunction<typeof fillLetterTemplate>;
  let mockGetTemplateById: jest.MockedFunction<typeof getTemplateById>;

  beforeEach(() => {
    jest.clearAllMocks();
    
    mockGenerateStructuredLetterData = generateStructuredLetterData as jest.MockedFunction<typeof generateStructuredLetterData>;
    mockFillLetterTemplate = fillLetterTemplate as jest.MockedFunction<typeof fillLetterTemplate>;
    mockGetTemplateById = getTemplateById as jest.MockedFunction<typeof getTemplateById>;

    // Setup default mocks
    mockGenerateStructuredLetterData.mockResolvedValue(createSampleStructuredLetterData());
    mockFillLetterTemplate.mockReturnValue('<html><body>Generated HTML content</body></html>');
    mockGetTemplateById.mockReturnValue({
      id: 'plain-text',
      name: 'Plain Text',
      previewDescription: 'Plain text template',
      templateHtml: () => '<html><body>Mock template</body></html>',
      previewImagePath: '/images/plain-text.png',
      isPremium: false,
      recommended: false,
      tokenCost: 10,
      fontFamily: 'Arial, sans-serif'
    });
  });

  describe('End-to-End Generation Flow', () => {
    it('should complete full structured generation flow successfully', async () => {
      // Create mock form data
      const formData = new FormData();
      formData.append('jobDescription', 'Software Developer position at Tech Company');
      formData.append('templateId', 'plain-text');
      formData.append('useStructuredGeneration', 'true');
      formData.append('unauthenticatedResumeFile', new Blob(['Mock resume content'], { type: 'application/pdf' }));
      formData.append('unauthenticatedResumeFileName', 'resume.pdf');

      // Create mock request
      const request = new NextRequest('http://localhost:3000/api/generate-application-letter', {
        method: 'POST',
        body: formData
      });

      // Execute the API route
      const response = await POST(request);
      const result = await response.json();

      // Verify the flow
      expect(response.status).toBe(200);
      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data.structuredData).toBeDefined();
      expect(result.data.design).toBeDefined();
      expect(result.data.templateId).toBe('plain-text');
      expect(result.data.generationMode).toBe('structured');

      // Verify that the AI generator was called
      expect(mockGenerateStructuredLetterData).toHaveBeenCalledWith(
        expect.objectContaining({
          buffer: expect.any(ArrayBuffer),
          mimeType: 'application/pdf'
        }),
        'Software Developer position at Tech Company',
        undefined,
        expect.objectContaining({
          templateId: 'plain-text',
          language: 'id',
          extractCompanyFromJob: true,
          includeAttachments: true
        })
      );

      // Verify template rendering was called
      expect(mockFillLetterTemplate).toHaveBeenCalled();
    });

    it('should handle authenticated user flow with token deduction', async () => {
      const formData = new FormData();
      formData.append('jobDescription', 'Developer position');
      formData.append('templateId', 'classic-blue');
      formData.append('useStructuredGeneration', 'true');

      const request = new NextRequest('http://localhost:3000/api/generate-application-letter', {
        method: 'POST',
        body: formData,
        headers: {
          'x-supabase-auth': 'mock-auth-token'
        }
      });

      // Mock premium template
      mockGetTemplateById.mockReturnValue({
        id: 'classic-blue',
        name: 'Classic Blue',
        previewDescription: 'Premium blue template',
        templateHtml: () => '<html><body>Premium template</body></html>',
        previewImagePath: '/images/classic-blue.png',
        isPremium: true,
        recommended: false,
        tokenCost: 15,
        fontFamily: 'Merriweather, serif'
      });

      const response = await POST(request);
      const result = await response.json();

      expect(response.status).toBe(200);
      expect(result.success).toBe(true);
      expect(result.data.price).toBe(15000); // Token cost converted to IDR
      expect(result.data.currency).toBe('IDR');
    });

    it('should handle job image input correctly', async () => {
      const jobImageBlob = new Blob(['Mock job image'], { type: 'image/png' });
      
      const formData = new FormData();
      formData.append('jobDescription', 'Frontend Developer');
      formData.append('jobImage', jobImageBlob);
      formData.append('templateId', 'plain-text');
      formData.append('useStructuredGeneration', 'true');
      formData.append('unauthenticatedResumeFile', new Blob(['Resume'], { type: 'text/plain' }));
      formData.append('unauthenticatedResumeFileName', 'resume.txt');

      const request = new NextRequest('http://localhost:3000/api/generate-application-letter', {
        method: 'POST',
        body: formData
      });

      const response = await POST(request);
      const result = await response.json();

      expect(response.status).toBe(200);
      expect(result.success).toBe(true);

      // Verify job image was passed to AI generator
      expect(mockGenerateStructuredLetterData).toHaveBeenCalledWith(
        expect.any(Object),
        'Frontend Developer',
        expect.objectContaining({
          buffer: expect.any(ArrayBuffer),
          mimeType: 'image/png'
        }),
        expect.any(Object)
      );
    });

    it('should handle unified streaming mode', async () => {
      const formData = new FormData();
      formData.append('jobDescription', 'Backend Developer');
      formData.append('templateId', 'plain-text');
      formData.append('useUnifiedStreaming', 'true');
      formData.append('unauthenticatedResumeFile', new Blob(['Resume content'], { type: 'application/pdf' }));
      formData.append('unauthenticatedResumeFileName', 'resume.pdf');

      const request = new NextRequest('http://localhost:3000/api/generate-application-letter', {
        method: 'POST',
        body: formData
      });

      const response = await POST(request);
      const result = await response.json();

      expect(response.status).toBe(200);
      expect(result.success).toBe(true);
      expect(result.data.streaming).toBeDefined();
      expect(result.data.streaming.enabled).toBe(true);
      expect(result.data.streaming.unified).toBe(true);
      expect(result.data.streaming.structured).toBe(true);
      expect(result.data.streaming.endpoint).toBe('/api/edge/generate-letter-unified');
      expect(result.data.generationMode).toBe('structured-streaming');
    });
  });

  describe('Error Handling Integration', () => {
    it('should handle AI generation errors gracefully', async () => {
      // Mock AI generation failure
      mockGenerateStructuredLetterData.mockRejectedValue(new Error('AI service unavailable'));

      const formData = new FormData();
      formData.append('jobDescription', 'Test job');
      formData.append('templateId', 'plain-text');
      formData.append('useStructuredGeneration', 'true');
      formData.append('unauthenticatedResumeFile', new Blob(['Resume'], { type: 'text/plain' }));
      formData.append('unauthenticatedResumeFileName', 'resume.txt');

      const request = new NextRequest('http://localhost:3000/api/generate-application-letter', {
        method: 'POST',
        body: formData
      });

      const response = await POST(request);
      const result = await response.json();

      expect(response.status).toBe(500);
      expect(result.success).toBe(false);
      expect(result.error).toContain('Gagal membuat surat lamaran');
    });

    it('should handle template rendering errors', async () => {
      // Mock template rendering failure
      mockFillLetterTemplate.mockImplementation(() => {
        throw new Error('Template rendering failed');
      });

      const formData = new FormData();
      formData.append('jobDescription', 'Test job');
      formData.append('templateId', 'plain-text');
      formData.append('useStructuredGeneration', 'true');
      formData.append('unauthenticatedResumeFile', new Blob(['Resume'], { type: 'text/plain' }));
      formData.append('unauthenticatedResumeFileName', 'resume.txt');

      const request = new NextRequest('http://localhost:3000/api/generate-application-letter', {
        method: 'POST',
        body: formData
      });

      const response = await POST(request);
      const result = await response.json();

      expect(response.status).toBe(500);
      expect(result.success).toBe(false);
      expect(result.error).toContain('Gagal membuat desain surat');
    });

    it('should handle missing template errors', async () => {
      // Mock template not found
      mockGetTemplateById.mockReturnValue(undefined);

      const formData = new FormData();
      formData.append('jobDescription', 'Test job');
      formData.append('templateId', 'non-existent-template');
      formData.append('useStructuredGeneration', 'true');
      formData.append('unauthenticatedResumeFile', new Blob(['Resume'], { type: 'text/plain' }));
      formData.append('unauthenticatedResumeFileName', 'resume.txt');

      const request = new NextRequest('http://localhost:3000/api/generate-application-letter', {
        method: 'POST',
        body: formData
      });

      const response = await POST(request);
      const result = await response.json();

      expect(response.status).toBe(404);
      expect(result.success).toBe(false);
      expect(result.error).toBe('Template tidak ditemukan');
    });

    it('should handle insufficient tokens for premium templates', async () => {
      // Mock user with insufficient tokens
      const mockClient = {
        auth: {
          getUser: jest.fn().mockResolvedValue({
            data: { user: { id: 'test-user-123' } },
            error: null
          })
        },
        from: jest.fn(() => ({
          select: jest.fn(() => ({
            eq: jest.fn(() => ({
              single: jest.fn().mockResolvedValue({
                data: {
                  resume_file_name: 'test-resume.pdf',
                  tokens: 5 // Insufficient tokens
                },
                error: null
              })
            }))
          }))
        })),
        storage: {
          from: jest.fn(() => ({
            download: jest.fn().mockResolvedValue({
              data: new Blob(['Mock resume content']),
              error: null
            })
          }))
        }
      };

      // Mock premium template requiring more tokens
      mockGetTemplateById.mockReturnValue({
        id: 'premium-template',
        name: 'Premium Template',
        previewDescription: 'Premium template',
        templateHtml: () => '<html><body>Premium template</body></html>',
        previewImagePath: '/images/premium-template.png',
        isPremium: true,
        recommended: false,
        tokenCost: 15, // More than user has
        fontFamily: 'Merriweather, serif'
      });

      const formData = new FormData();
      formData.append('jobDescription', 'Test job');
      formData.append('templateId', 'premium-template');
      formData.append('useStructuredGeneration', 'true');

      const request = new NextRequest('http://localhost:3000/api/generate-application-letter', {
        method: 'POST',
        body: formData,
        headers: {
          'x-supabase-auth': 'mock-auth-token'
        }
      });

      const response = await POST(request);
      const result = await response.json();

      expect(response.status).toBe(402);
      expect(result.success).toBe(false);
      expect(result.error).toContain('Token Anda tidak cukup');
    });

    it('should handle invalid file formats', async () => {
      const formData = new FormData();
      formData.append('jobDescription', 'Test job');
      formData.append('templateId', 'plain-text');
      formData.append('useStructuredGeneration', 'true');
      formData.append('unauthenticatedResumeFile', new Blob(['Resume'], { type: 'application/unknown' }));
      formData.append('unauthenticatedResumeFileName', 'resume.unknown');

      const request = new NextRequest('http://localhost:3000/api/generate-application-letter', {
        method: 'POST',
        body: formData
      });

      const response = await POST(request);
      const result = await response.json();

      expect(response.status).toBe(500);
      expect(result.success).toBe(false);
    });
  });

  describe('API Route Validation', () => {
    it('should require job description or job image', async () => {
      const formData = new FormData();
      formData.append('templateId', 'plain-text');
      formData.append('useStructuredGeneration', 'true');
      formData.append('unauthenticatedResumeFile', new Blob(['Resume'], { type: 'text/plain' }));
      formData.append('unauthenticatedResumeFileName', 'resume.txt');
      // Missing job description and job image

      const request = new NextRequest('http://localhost:3000/api/generate-application-letter', {
        method: 'POST',
        body: formData
      });

      const response = await POST(request);
      const result = await response.json();

      expect(response.status).toBe(400);
      expect(result.error).toContain('Deskripsi pekerjaan dan gambar lowongan diperlukan');
    });

    it('should handle edit operations with existing letter ID', async () => {
      const formData = new FormData();
      formData.append('jobDescription', 'Updated job description');
      formData.append('templateId', 'plain-text');
      formData.append('useStructuredGeneration', 'true');
      formData.append('existingLetterId', 'existing-letter-123');
      formData.append('editedLetterText', 'Updated content');
      formData.append('unauthenticatedResumeFile', new Blob(['Resume'], { type: 'text/plain' }));
      formData.append('unauthenticatedResumeFileName', 'resume.txt');

      const request = new NextRequest('http://localhost:3000/api/generate-application-letter', {
        method: 'POST',
        body: formData
      });

      const response = await POST(request);
      const result = await response.json();

      expect(response.status).toBe(200);
      expect(result.success).toBe(true);
      expect(result.data.letterId).toBeDefined();
    });
  });

  describe('Streaming Response Handling', () => {
    it('should prepare unified streaming data correctly', async () => {
      const formData = new FormData();
      formData.append('jobDescription', 'Full Stack Developer');
      formData.append('templateId', 'classic-blue');
      formData.append('useUnifiedStreaming', 'true');
      formData.append('unauthenticatedResumeFile', new Blob(['Resume content'], { type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' }));
      formData.append('unauthenticatedResumeFileName', 'resume.docx');

      const request = new NextRequest('http://localhost:3000/api/generate-application-letter', {
        method: 'POST',
        body: formData
      });

      const response = await POST(request);
      const result = await response.json();

      expect(response.status).toBe(200);
      expect(result.success).toBe(true);
      expect(result.data.streaming).toBeDefined();
      expect(result.data.streaming.data).toBeDefined();
      expect(result.data.streaming.data.resumeMimeType).toBe('text/plain'); // Should be converted from DOCX
      expect(result.data.streaming.data.useStructuredGeneration).toBe(true);
    });

    it('should handle job image in streaming mode', async () => {
      const jobImageBlob = new Blob(['Mock job image'], { type: 'image/jpeg' });
      
      const formData = new FormData();
      formData.append('jobDescription', 'React Developer');
      formData.append('jobImage', jobImageBlob);
      formData.append('templateId', 'plain-text');
      formData.append('useUnifiedStreaming', 'true');
      formData.append('unauthenticatedResumeFile', new Blob(['Resume'], { type: 'application/pdf' }));
      formData.append('unauthenticatedResumeFileName', 'resume.pdf');

      const request = new NextRequest('http://localhost:3000/api/generate-application-letter', {
        method: 'POST',
        body: formData
      });

      const response = await POST(request);
      const result = await response.json();

      expect(response.status).toBe(200);
      expect(result.success).toBe(true);
      expect(result.data.streaming.data.jobImage).toBeDefined();
      expect(typeof result.data.streaming.data.jobImage).toBe('string'); // Base64 encoded
    });
  });

  describe('Performance Integration', () => {
    it('should complete generation within acceptable time limits', async () => {
      const formData = new FormData();
      formData.append('jobDescription', 'Software Engineer');
      formData.append('templateId', 'plain-text');
      formData.append('useStructuredGeneration', 'true');
      formData.append('unauthenticatedResumeFile', new Blob(['Resume content'], { type: 'text/plain' }));
      formData.append('unauthenticatedResumeFileName', 'resume.txt');

      const request = new NextRequest('http://localhost:3000/api/generate-application-letter', {
        method: 'POST',
        body: formData
      });

      const startTime = performance.now();
      const response = await POST(request);
      const endTime = performance.now();

      const duration = endTime - startTime;

      expect(response.status).toBe(200);
      expect(duration).toBeLessThan(5000); // Should complete within 5 seconds
    });

    it('should handle concurrent requests efficiently', async () => {
      const createRequest = () => {
        const formData = new FormData();
        formData.append('jobDescription', 'Developer');
        formData.append('templateId', 'plain-text');
        formData.append('useStructuredGeneration', 'true');
        formData.append('unauthenticatedResumeFile', new Blob(['Resume'], { type: 'text/plain' }));
        formData.append('unauthenticatedResumeFileName', 'resume.txt');

        return new NextRequest('http://localhost:3000/api/generate-application-letter', {
          method: 'POST',
          body: formData
        });
      };

      const requests = Array.from({ length: 3 }, () => createRequest());
      const startTime = performance.now();
      
      const responses = await Promise.all(
        requests.map(request => POST(request))
      );
      
      const endTime = performance.now();
      const duration = endTime - startTime;

      // All requests should succeed
      responses.forEach(response => {
        expect(response.status).toBe(200);
      });

      // Should handle 3 concurrent requests within reasonable time
      expect(duration).toBeLessThan(10000);
    });
  });

  describe('Data Validation Integration', () => {
    it('should validate and fix generated structured data', async () => {
      // Mock AI generation returning incomplete data
      mockGenerateStructuredLetterData.mockResolvedValue({
        ...createSampleStructuredLetterData(),
        body: {
          opening: 'Dear Sir/Madam,',
          paragraphs: [], // Empty paragraphs - should be fixed
          closing: 'Thank you.'
        },
        subject: {
          prefix: 'Application for',
          position: '' // Empty position - should be fixed
        }
      } as any);

      const formData = new FormData();
      formData.append('jobDescription', 'Test Developer');
      formData.append('templateId', 'plain-text');
      formData.append('useStructuredGeneration', 'true');
      formData.append('unauthenticatedResumeFile', new Blob(['Resume'], { type: 'text/plain' }));
      formData.append('unauthenticatedResumeFileName', 'resume.txt');

      const request = new NextRequest('http://localhost:3000/api/generate-application-letter', {
        method: 'POST',
        body: formData
      });

      const response = await POST(request);
      const result = await response.json();

      expect(response.status).toBe(200);
      expect(result.success).toBe(true);
      // The system should still succeed by fixing the data or providing warnings
    });
  });
});