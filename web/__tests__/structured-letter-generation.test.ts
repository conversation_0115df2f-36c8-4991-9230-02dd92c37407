/**
 * Tests for structured letter generation implementation
 */

import {
  StructuredLetterData,
  LetterTemplateData,
  validateStructuredLetterData,
  validateLetterTemplateData,
  convertToLetterTemplateData,
  createDefaultStructuredLetterData,
  updateLetterSection,
  mergeStructuredLetterData
} from '@/types/letter-structured';

import {
  fillLetterTemplate,
  getAvailableLetterTemplateIds,
  isLetterTemplateAvailable,
  testLetterTemplate
} from '@/utils/letter-template-engine';

import {
  validateAndFixStructuredData
} from '@/utils/ai-generators/structuredLetterGenerator';

import { getTemplateById } from '@/utils/letter-templates/applicationLetterTemplates';

import {
  createSampleStructuredLetterData,
  createMinimalStructuredLetterData,
  createInvalidStructuredLetterData,
  createSampleTemplateData,
  templateTestUtils,
  errorTestUtils
} from './utils/test-helpers';

// Mock external dependencies
jest.mock('fs', () => ({
  existsSync: jest.fn(() => true),
  readFileSync: jest.fn(() => '<html><body>{{signatureName}}</body></html>')
}));

jest.mock('handlebars', () => ({
  compile: jest.fn(() => (data: any) => `<html><body>${data.signatureName}</body></html>`),
  registerHelper: jest.fn()
}));

describe('Structured Letter Data Validation', () => {
  describe('validateStructuredLetterData', () => {
    it('should validate complete structured data successfully', () => {
      const validData = createSampleStructuredLetterData();
      const result = validateStructuredLetterData(validData);

      expect(result.isValid).toBe(true);
      expect(result.missingFields).toEqual([]);
    });

    it('should identify missing required fields', () => {
      const invalidData = createInvalidStructuredLetterData() as StructuredLetterData;
      const result = validateStructuredLetterData(invalidData);

      expect(result.isValid).toBe(false);
      expect(result.missingFields).toContain('subject');
      expect(result.missingFields).toContain('recipient');
      expect(result.missingFields).toContain('body');
      expect(result.missingFields).toContain('signature');
    });

    it('should provide warnings for recommended but optional fields', () => {
      const dataWithoutOptionalFields = createSampleStructuredLetterData({
        subject: {
          prefix: '',
          position: 'Developer'
        },
        recipient: {
          salutation: 'Yth.',
          title: 'HR Manager'
          // Missing company field
        }
      });

      const result = validateStructuredLetterData(dataWithoutOptionalFields);
      
      expect(result.isValid).toBe(true);
      expect(result.warnings).toBeDefined();
      expect(result.warnings).toContain('subject.prefix is recommended');
      expect(result.warnings).toContain('recipient.company is recommended');
    });

    it('should validate metadata fields', () => {
      const dataWithoutMetadata = createSampleStructuredLetterData();
      delete (dataWithoutMetadata as any).metadata;

      const result = validateStructuredLetterData(dataWithoutMetadata);

      expect(result.isValid).toBe(false);
      expect(result.missingFields).toContain('metadata');
    });

    it('should validate body paragraphs', () => {
      const dataWithoutParagraphs = createSampleStructuredLetterData({
        body: {
          opening: 'Dear Sir/Madam,',
          paragraphs: [], // Empty paragraphs
          closing: 'Thank you.'
        }
      });

      const result = validateStructuredLetterData(dataWithoutParagraphs);

      expect(result.isValid).toBe(false);
      expect(result.missingFields).toContain('body.paragraphs');
    });
  });

  describe('validateLetterTemplateData', () => {
    it('should validate complete template data successfully', () => {
      const validTemplateData = createSampleTemplateData();
      const result = validateLetterTemplateData(validTemplateData);

      expect(result.isValid).toBe(true);
      expect(result.missingFields).toEqual([]);
    });

    it('should identify missing required template fields', () => {
      const incompleteTemplateData: Partial<LetterTemplateData> = {
        date: '29 Januari 2024',
        // Missing other required fields
      };

      const result = validateLetterTemplateData(incompleteTemplateData as LetterTemplateData);

      expect(result.isValid).toBe(false);
      expect(result.missingFields).toContain('subject');
      expect(result.missingFields).toContain('opening');
      expect(result.missingFields).toContain('farewell');
      expect(result.missingFields).toContain('signatureName');
    });

    it('should provide appropriate warnings', () => {
      const templateDataWithoutOptionals = createSampleTemplateData({
        closing: '', // Missing closing
        attachments: [] // Empty attachments
      });

      const result = validateLetterTemplateData(templateDataWithoutOptionals);

      expect(result.isValid).toBe(true);
      expect(result.warnings).toBeDefined();
      expect(result.warnings).toContain('closing paragraph is recommended');
      expect(result.warnings).toContain('attachments list is recommended');
    });
  });
});

describe('Data Conversion Functions', () => {
  describe('convertToLetterTemplateData', () => {
    it('should convert structured data to template data format', () => {
      const structuredData = createSampleStructuredLetterData();
      const templateData = convertToLetterTemplateData(structuredData);

      expect(templateData.date).toBe(structuredData.header.date);
      expect(templateData.subject).toBe(`${structuredData.subject.prefix} ${structuredData.subject.position}`);
      expect(templateData.signatureName).toBe(structuredData.signature.name);
      expect(templateData.farewell).toBe(structuredData.signature.farewell);
      expect(templateData.opening).toBe(structuredData.body.opening);
      expect(templateData.paragraphs).toEqual(structuredData.body.paragraphs);
      expect(templateData.closing).toBe(structuredData.body.closing);
      expect(templateData.templateId).toBe(structuredData.metadata.templateId);
      expect(templateData.language).toBe(structuredData.metadata.language);
    });

    it('should build recipient lines correctly', () => {
      const structuredData = createSampleStructuredLetterData({
        recipient: {
          salutation: 'Yth.',
          title: 'HR Manager',
          company: 'PT Test Company',
          address: ['Jl. Test No. 123', 'Jakarta 12345']
        }
      });

      const templateData = convertToLetterTemplateData(structuredData);

      expect(templateData.recipientLines).toEqual([
        'Yth. HR Manager',
        'PT Test Company',
        'Jl. Test No. 123',
        'Jakarta 12345'
      ]);
    });

    it('should handle missing optional fields gracefully', () => {
      const structuredData = createSampleStructuredLetterData({
        recipient: {
          salutation: 'Dear',
          title: 'Hiring Manager'
          // No company or address
        },
        signature: {
          farewell: 'Sincerely,',
          name: 'John Doe'
          // No additional info
        }
      });

      const templateData = convertToLetterTemplateData(structuredData);

      expect(templateData.recipientLines).toEqual(['Dear Hiring Manager']);
      expect(templateData.additionalInfo).toBeUndefined();
    });

    it('should use formatted date when available', () => {
      const structuredData = createSampleStructuredLetterData({
        header: {
          date: '2024-01-29',
          formattedDate: '29 January 2024'
        }
      });

      const templateData = convertToLetterTemplateData(structuredData);

      expect(templateData.date).toBe('29 January 2024');
    });
  });
});

describe('Default Data Creation', () => {
  describe('createDefaultStructuredLetterData', () => {
    it('should create valid default data for Indonesian', () => {
      const defaultData = createDefaultStructuredLetterData('plain-text', 'id');

      expect(defaultData.metadata.templateId).toBe('plain-text');
      expect(defaultData.metadata.language).toBe('id');
      expect(defaultData.subject.prefix).toBe('Perihal: Lamaran Pekerjaan sebagai');
      expect(defaultData.recipient.salutation).toBe('Yth.');
      expect(defaultData.body.opening).toBe('Dengan hormat,');
      expect(defaultData.signature.farewell).toBe('Hormat saya,');
      expect(defaultData.attachments).toEqual(['Curriculum Vitae', 'Portofolio']);

      const validation = validateStructuredLetterData(defaultData);
      expect(validation.isValid).toBe(false); // Should be false due to empty required fields like position and name
      expect(validation.missingFields).not.toContain('metadata');
    });

    it('should create valid default data for English', () => {
      const defaultData = createDefaultStructuredLetterData('professional-classic', 'en');

      expect(defaultData.metadata.templateId).toBe('professional-classic');
      expect(defaultData.metadata.language).toBe('en');
      expect(defaultData.subject.prefix).toBe('Subject: Job Application for');
      expect(defaultData.recipient.salutation).toBe('Dear');
      expect(defaultData.body.opening).toBe('Dear Sir/Madam,');
      expect(defaultData.signature.farewell).toBe('Sincerely,');
      expect(defaultData.attachments).toEqual(['Resume', 'Portfolio']);
    });
  });
});

describe('Data Manipulation Functions', () => {
  describe('updateLetterSection', () => {
    it('should update specific section and modify timestamp', () => {
      const originalData = createSampleStructuredLetterData();
      const originalTimestamp = originalData.metadata.lastModified;

      // Wait a bit to ensure timestamp difference
      const updatedData = updateLetterSection(originalData, 'subject', {
        position: 'Senior Developer'
      });

      expect(updatedData.subject.position).toBe('Senior Developer');
      expect(updatedData.subject.prefix).toBe(originalData.subject.prefix);
      expect(updatedData.metadata.lastModified).not.toBe(originalTimestamp);
      
      // Other sections should remain unchanged
      expect(updatedData.body).toEqual(originalData.body);
      expect(updatedData.signature).toEqual(originalData.signature);
    });

    it('should preserve other properties when updating', () => {
      const originalData = createSampleStructuredLetterData();
      
      const updatedData = updateLetterSection(originalData, 'signature', {
        name: 'Jane Smith'
      });

      expect(updatedData.signature.name).toBe('Jane Smith');
      expect(updatedData.signature.farewell).toBe(originalData.signature.farewell);
      expect(updatedData.signature.additionalInfo).toBe(originalData.signature.additionalInfo);
    });
  });

  describe('mergeStructuredLetterData', () => {
    it('should merge partial updates into base data', () => {
      const baseData = createSampleStructuredLetterData();
      const updates: Partial<StructuredLetterData> = {
        subject: {
          prefix: 'RE: Application for',
          position: 'Lead Developer'
        },
        signature: {
          farewell: 'Best regards,',
          name: 'Jane Doe',
          additionalInfo: 'Phone: +1234567890'
        }
      };

      const mergedData = mergeStructuredLetterData(baseData, updates);

      expect(mergedData.subject).toEqual(updates.subject);
      expect(mergedData.signature).toEqual(updates.signature);
      expect(mergedData.body).toEqual(baseData.body); // Unchanged
      expect(mergedData.recipient).toEqual(baseData.recipient); // Unchanged
      expect(mergedData.metadata.lastModified).not.toBe(baseData.metadata.lastModified);
    });

    it('should handle paragraph updates correctly', () => {
      const baseData = createSampleStructuredLetterData();
      const newParagraphs = ['New first paragraph', 'New second paragraph'];
      
      const updates: Partial<StructuredLetterData> = {
        body: {
          opening: baseData.body.opening,
          paragraphs: newParagraphs,
          closing: baseData.body.closing
        }
      };

      const mergedData = mergeStructuredLetterData(baseData, updates);

      expect(mergedData.body.paragraphs).toEqual(newParagraphs);
      expect(mergedData.body.opening).toBe(baseData.body.opening); // Should preserve
      expect(mergedData.body.closing).toBe(baseData.body.closing); // Should preserve
    });
  });
});

describe('Template Engine Integration', () => {
  describe('fillLetterTemplate', () => {
    it('should render template with structured data successfully', () => {
      const template = getTemplateById('plain-text');
      const structuredData = createSampleStructuredLetterData();

      if (!template) {
        throw new Error('Template not found');
      }

      const html = fillLetterTemplate(template, structuredData);

      expect(html).toBeDefined();
      expect(html).toContain(structuredData.signature.name);
      expect(typeof html).toBe('string');
      expect(html.length).toBeGreaterThan(0);
    });

    it('should throw error for invalid structured data', () => {
      const template = getTemplateById('plain-text');
      const invalidData = createInvalidStructuredLetterData() as StructuredLetterData;

      if (!template) {
        throw new Error('Template not found');
      }

      expect(() => {
        fillLetterTemplate(template, invalidData);
      }).toThrow(/Template data validation failed/);
    });

    it('should validate generated HTML content', () => {
      const template = getTemplateById('plain-text');
      const structuredData = createSampleStructuredLetterData();

      if (!template) {
        throw new Error('Template not found');
      }

      const html = fillLetterTemplate(template, structuredData);
      const checks = templateTestUtils.validateHtmlStructure(html);

      expect(checks.isEmpty).toBe(false);
      expect(checks.isValidLength).toBe(true);
    });
  });

  describe('getAvailableLetterTemplateIds', () => {
    it('should return array of available template IDs', () => {
      const templateIds = getAvailableLetterTemplateIds();

      expect(Array.isArray(templateIds)).toBe(true);
      expect(templateIds.length).toBeGreaterThan(0);
      expect(templateIds).toContain('plain-text');
    });

    it('should return unique template IDs', () => {
      const templateIds = getAvailableLetterTemplateIds();
      const uniqueIds = Array.from(new Set(templateIds));

      expect(templateIds.length).toBe(uniqueIds.length);
    });
  });

  describe('isLetterTemplateAvailable', () => {
    it('should return true for existing templates', () => {
      expect(isLetterTemplateAvailable('plain-text')).toBe(true);
    });

    it('should return false for non-existing templates', () => {
      expect(isLetterTemplateAvailable('non-existent-template')).toBe(false);
    });
  });

  describe('testLetterTemplate', () => {
    it('should test template compilation successfully', () => {
      const template = getTemplateById('plain-text');
      const sampleData = createSampleStructuredLetterData();

      if (!template) {
        throw new Error('Template not found');
      }

      const result = testLetterTemplate(template, sampleData);

      expect(result.success).toBe(true);
      expect(result.error).toBeUndefined();
      expect(result.htmlLength).toBeGreaterThan(0);
    });

    it('should handle template compilation errors', () => {
      const invalidTemplate = templateTestUtils.createMockTemplate('invalid-template');
      invalidTemplate.templateHtml = () => {
        throw new Error('Template compilation failed');
      };

      const sampleData = createSampleStructuredLetterData();
      const result = testLetterTemplate(invalidTemplate, sampleData);

      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.error).toContain('Template compilation failed');
    });
  });
});

describe('Data Validation and Fixing', () => {
  describe('validateAndFixStructuredData', () => {
    it('should fix empty paragraphs', () => {
      const dataWithEmptyParagraphs = createSampleStructuredLetterData({
        body: {
          opening: 'Dear Sir/Madam,',
          paragraphs: [],
          closing: 'Thank you.'
        }
      });

      const fixedData = validateAndFixStructuredData(dataWithEmptyParagraphs);

      expect(fixedData.body.paragraphs).toHaveLength(1);
      expect(fixedData.body.paragraphs[0]).toBe('[Paragraph content needs to be generated]');
    });

    it('should fix empty position', () => {
      const dataWithEmptyPosition = createSampleStructuredLetterData({
        subject: {
          prefix: 'Subject: Application for',
          position: ''
        }
      });

      const fixedData = validateAndFixStructuredData(dataWithEmptyPosition);

      expect(fixedData.subject.position).toBe('[Position Title]');
    });

    it('should fix empty candidate name', () => {
      const dataWithEmptyName = createSampleStructuredLetterData({
        signature: {
          farewell: 'Sincerely,',
          name: '',
          additionalInfo: 'Contact info'
        }
      });

      const fixedData = validateAndFixStructuredData(dataWithEmptyName);

      expect(fixedData.signature.name).toBe('[Candidate Name]');
    });

    it('should clean up placeholder text in company field', () => {
      const dataWithPlaceholderCompany = createSampleStructuredLetterData({
        recipient: {
          salutation: 'Dear',
          title: 'Hiring Manager',
          company: '[Extract company name if available]'
        }
      });

      const fixedData = validateAndFixStructuredData(dataWithPlaceholderCompany);

      expect(fixedData.recipient.company).toBeUndefined();
    });

    it('should clean up invalid address fields', () => {
      const dataWithInvalidAddress = createSampleStructuredLetterData({
        recipient: {
          salutation: 'Dear',
          title: 'Hiring Manager',
          company: 'Test Company',
          address: ['[Extract company address as array if available]', 'Valid Address Line']
        }
      });

      const fixedData = validateAndFixStructuredData(dataWithInvalidAddress);

      expect(fixedData.recipient.address).toEqual(['Valid Address Line']);
    });

    it('should remove address field if all entries are invalid', () => {
      const dataWithAllInvalidAddresses = createSampleStructuredLetterData({
        recipient: {
          salutation: 'Dear',
          title: 'Hiring Manager',
          company: 'Test Company',
          address: ['[Extract address]', '[Company address here]']
        }
      });

      const fixedData = validateAndFixStructuredData(dataWithAllInvalidAddresses);

      expect(fixedData.recipient.address).toBeUndefined();
    });
  });
});

describe('Error Handling', () => {
  it('should handle template not found errors', () => {
    const mockConsole = errorTestUtils.mockConsole();
    
    try {
      const nonExistentTemplate = templateTestUtils.createMockTemplate('non-existent');
      const structuredData = createSampleStructuredLetterData();

      // Mock template that throws error
      nonExistentTemplate.templateHtml = () => {
        throw errorTestUtils.createTestError('template', 'Template not found');
      };

      expect(() => {
        fillLetterTemplate(nonExistentTemplate, structuredData);
      }).toThrow();

      expect(mockConsole.mocks.error).toHaveBeenCalled();
    } finally {
      mockConsole.restore();
    }
  });

  it('should handle validation errors gracefully', () => {
    const template = getTemplateById('plain-text');
    
    if (!template) {
      throw new Error('Template not found');
    }

    // Create data that will fail validation
    const invalidData = {
      ...createSampleStructuredLetterData(),
      signature: {
        farewell: '',
        name: '' // Missing required field
      }
    };

    expect(() => {
      fillLetterTemplate(template, invalidData);
    }).toThrow(/Template data validation failed/);
  });
});

describe('Performance', () => {
  it('should render template within reasonable time', async () => {
    const template = getTemplateById('plain-text');
    const structuredData = createSampleStructuredLetterData();

    if (!template) {
      throw new Error('Template not found');
    }

    const startTime = performance.now();
    fillLetterTemplate(template, structuredData);
    const endTime = performance.now();

    const duration = endTime - startTime;
    expect(duration).toBeLessThan(1000); // Should render within 1 second
  });

  it('should handle multiple template renders efficiently', () => {
    const template = getTemplateById('plain-text');
    const structuredData = createSampleStructuredLetterData();

    if (!template) {
      throw new Error('Template not found');
    }

    const startTime = performance.now();
    
    for (let i = 0; i < 10; i++) {
      fillLetterTemplate(template, structuredData);
    }
    
    const endTime = performance.now();
    const averageTime = (endTime - startTime) / 10;

    expect(averageTime).toBeLessThan(100); // Each render should be under 100ms on average
  });
});