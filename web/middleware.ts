import { createServerClient } from '@supabase/ssr';
import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

/**
 * Middleware untuk autentikasi dan proteksi rute
 * Disederhanakan untuk pengelolaan cookie yang lebih efisien
 */
export async function middleware(request: NextRequest) {
  // Buat response awal
  let response = NextResponse.next({
    request,
  });

  // Buat Supabase client
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    { 
      cookies: {
        getAll() {
          return request.cookies.getAll();
        },
        setAll(cookiesToSet) {
          cookiesToSet.forEach(({ name, value, options }) => request.cookies.set(name, value))
          response = NextResponse.next({
            request,
          })
          cookiesToSet.forEach(({ name, value, options }) =>
            response.cookies.set(name, value, options)
          )
        },
      }
    }
  );

  // Do not run code between createServerClient and
  // supabase.auth.getUser(). A simple mistake could make it very hard to debug
  // issues with users being randomly logged out.

  // IMPORTANT: DO NOT REMOVE auth.getUser()

  // Dapatkan user (lebih aman daripada getSession untuk middleware)
  const { data: { user } } = await supabase.auth.getUser();

  // Verifikasi rute yang memerlukan autentikasi
  const protectedRoutes: string[] = ['/profile', '/buy-tokens'];
  const isProtectedRoute = protectedRoutes.some(route => request.nextUrl.pathname.startsWith(route));
  const isAuthRoute = ['/login', '/register'].includes(request.nextUrl.pathname);
  
  // Logika redirect berdasarkan autentikasi
  if (isProtectedRoute && !user) {
    // Pengguna mencoba mengakses rute terproteksi tanpa login
    const redirectUrl = new URL(`/register?redirect=${request.nextUrl.pathname}`, request.url);
    return NextResponse.redirect(redirectUrl);
  } else if (user && isAuthRoute) {
    // Pengguna sudah login tapi mencoba akses halaman login/register
    return NextResponse.redirect(new URL('/', request.url));
  }

  // IMPORTANT: You *must* return the response object as it is.
  // If you're creating a new response object with NextResponse.next() make sure to:
  // 1. Pass the request in it, like so:
  //    const myNewResponse = NextResponse.next({ request })
  // 2. Copy over the cookies, like so:
  //    myNewResponse.cookies.setAll(response.cookies.getAll())
  // 3. Change the myNewResponse object to fit your needs, but avoid changing
  //    the cookies!
  // 4. Finally:
  //    return myNewResponse
  // If this is not done, you may be causing the browser and server to go out
  // of sync and terminate the user's session prematurely!

  return response;
}

// Apply middleware to specific paths
export const config = {
  matcher: [
    '/application-letter/:path*',
    '/email-application/:path*',
    '/job-match/:path*',
    '/upload-resume/:path*',
    '/login',
    '/register',
    '/register-success',
    '/forgot-password',
    '/reset-password',
    '/profile',
    '/buy-tokens'
  ],
};
