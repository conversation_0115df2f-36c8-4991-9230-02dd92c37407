const fs = require('fs');
const path = require('path');
const Handlebars = require('handlebars');

// Load and register helpers
const helpers = require('./utils/handlebars-helpers/index.js');
Object.keys(helpers).forEach(name => {
  Handlebars.registerHelper(name, helpers[name]);
});

// Load templates
const templates = {
  'clean-professional': fs.readFileSync(path.join(__dirname, 'utils/handlebars-templates/clean-professional.hbs'), 'utf8'),
  'modern-clean': fs.readFileSync(path.join(__dirname, 'utils/handlebars-templates/modern-clean.hbs'), 'utf8'),
  'classic-professional': fs.readFileSync(path.join(__dirname, 'utils/handlebars-templates/classic-professional.hbs'), 'utf8')
};

// Compile templates
const compiledTemplates = {};
Object.keys(templates).forEach(name => {
  compiledTemplates[name] = Handlebars.compile(templates[name]);
});

// Specific test for the template logic issues
const problemData = {
  name: "Test User",
  jobTitle: "Developer",
  email: "<EMAIL>",
  experiences: [
    {
      jobTitle: "Developer",
      company: "Test Corp",
      startDate: "2022",
      endDate: "Present",
      responsibilities: ["", "   ", "\t", "\n"] // Array exists but all elements are empty/whitespace
    }
  ],
  projects: [
    {
      title: "Test Project",
      description: "A project",
      achievements: ["", "   "] // Same issue with achievements
    }
  ],
  skillCategories: [
    {
      category: "",
      skills: "   " // Empty category with whitespace skills
    },
    {
      category: "Valid Category",
      skills: "" // Valid category but empty skills
    }
  ]
};

console.log('=== TEMPLATE LOGIC ANALYSIS ===\n');

Object.keys(compiledTemplates).forEach(templateName => {
  console.log(`\n--- Analyzing ${templateName} Template ---`);
  
  const html = compiledTemplates[templateName](problemData);
  
  // Extract and analyze the problematic sections
  
  // 1. Check responsibilities section
  const responsibilitiesMatch = html.match(/<ul[^>]*>\s*((?:<li[^>]*>.*?<\/li>\s*)*)\s*<\/ul>/gs);
  if (responsibilitiesMatch) {
    responsibilitiesMatch.forEach((match, i) => {
      const liElements = match.match(/<li[^>]*>(.*?)<\/li>/gs);
      if (liElements) {
        const emptyLis = liElements.filter(li => {
          const content = li.replace(/<\/?li[^>]*>/g, '').trim();
          return content === '';
        });
        if (emptyLis.length > 0) {
          console.log(`❌ Found ${emptyLis.length} empty <li> elements in UL ${i + 1}`);
        }
      } else if (match.match(/<ul[^>]*>\s*<\/ul>/)) {
        console.log(`⚠️  Found completely empty <ul> element`);
      }
    });
  }
  
  // 2. Check skills section
  const skillsMatch = html.match(/<div class="skill-category"[^>]*>(.*?)<\/div>/gs);
  if (skillsMatch) {
    skillsMatch.forEach((match, i) => {
      const content = match.replace(/<div[^>]*class="skill-category"[^>]*>|<\/div>/g, '').trim();
      if (!content || content.match(/^\s*$/)) {
        console.log(`⚠️  Found empty skill category div ${i + 1}`);
      }
    });
  }
  
  // 3. Overall template size and structure analysis
  const htmlWithoutCSS = html.replace(/<style>[\s\S]*?<\/style>/g, '');
  const totalElements = (htmlWithoutCSS.match(/<[^\/][^>]*>/g) || []).length;
  const emptyElements = (htmlWithoutCSS.match(/<[^\/][^>]*>\s*<\/[^>]+>/g) || []).length;
  
  console.log(`📊 Template Stats:`);
  console.log(`   - Total HTML elements: ${totalElements}`);
  console.log(`   - Empty elements: ${emptyElements}`);
  console.log(`   - Empty element ratio: ${((emptyElements / totalElements) * 100).toFixed(1)}%`);
  
  // Check for the specific pattern that causes issues
  if (html.includes('<ul>') && html.includes('</ul>')) {
    const hasValidListItems = html.match(/<li[^>]*>[^<\s]+.*?<\/li>/);
    const hasEmptyLists = html.match(/<ul[^>]*>\s*<\/ul>/);
    
    if (hasEmptyLists && !hasValidListItems) {
      console.log(`❌ CRITICAL: Template generates empty <ul> without any valid content`);
    }
  }
});

// Test the specific template patterns that might cause issues
console.log('\n=== TEMPLATE PATTERN ANALYSIS ===\n');

// Examine the actual template patterns for responsibilities
Object.keys(templates).forEach(templateName => {
  console.log(`\n--- ${templateName} Template Patterns ---`);
  
  const template = templates[templateName];
  
  // Check for responsibilities handling pattern
  const responsibilitiesPattern = template.match(/\{\{#if responsibilities\}\}[\s\S]*?\{\{\/if\}\}/);
  if (responsibilitiesPattern) {
    console.log('Responsibilities pattern found:');
    console.log(responsibilitiesPattern[0].substring(0, 200) + '...');
    
    // Check if it uses hasContent helper
    if (responsibilitiesPattern[0].includes('hasContent')) {
      console.log('✅ Uses hasContent helper for filtering');
    } else {
      console.log('⚠️  Does not use hasContent helper - potential empty elements');
    }
  }
  
  // Check skills pattern
  const skillsPattern = template.match(/\{\{#if skill.*?\}\}[\s\S]*?\{\{\/if\}\}/);
  if (skillsPattern) {
    if (skillsPattern[0].includes('hasContent')) {
      console.log('✅ Skills section uses hasContent helper');
    } else {
      console.log('⚠️  Skills section missing hasContent validation');
    }
  }
});