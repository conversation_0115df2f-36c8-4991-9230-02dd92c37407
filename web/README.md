# Gigsta Web Application Documentation

## Overview

Gigsta is an AI-powered web platform that helps job seekers create professional application materials and analyze job opportunities. Built with Next.js, the platform provides intelligent tools for generating personalized application letters, email applications, and job compatibility analysis.

## 🚀 Features

#### Core Features
1. **Email Application Generator** (`/email-application`)
   - AI-powered email application letters
   - Personalized based on CV/resume and job description
   - Support for text input or job image upload
   - Token cost: 5 tokens per generation

2. **Application Letter Generator** (`/application-letter`)
   - Professional application letter documents
   - Multiple premium templates available
   - PDF download functionality
   - AI-generated content based on resume and job requirements
   - Token cost: 10 tokens (plain text), 15 tokens (premium templates)

3. **Job Matching Analysis** (`/job-match`)
   - CV/resume analysis against job descriptions
   - Skills gap identification
   - Improvement recommendations
   - Compatibility scoring
   - Free feature (no token cost)

#### User Management
- **Authentication**: Supabase Auth with email/password and OAuth (Google, Facebook)
- **User Profiles**: Token balance management, resume storage
- **Token System**: Purchase-based model with three packages:
  - Eksplorasi: 20 tokens (Rp10,000)
  - Siap Melamar: 40+5 bonus tokens = 45 total (Rp20,000)
  - <PERSON><PERSON><PERSON>: 100+20 bonus tokens = 120 total (Rp50,000)

#### Payment Integration
- **Payment Provider**: Midtrans (migrated from Doku)
- **Token Purchasing**: Secure payment processing with webhook notifications
- **Purchase History**: Track token purchases and usage

## 🏗️ Technical Architecture

### Frontend Stack
- **Framework**: Next.js 15.3.1 with React 19.1.0
- **Styling**: Tailwind CSS 3.3.0
- **UI Components**: Custom components with React Icons
- **Forms**: React Hook Form 7.49.2
- **State Management**: SWR 2.3.3 for data fetching

## 🎨 Design Theme & Style Guide

### Color Palette
The application uses a modern, professional color scheme optimized for accessibility and user experience:

#### Primary Colors
- **Primary Blue**: `#3B82F6` (Blue-500) - Main brand color for buttons, links, and accents
- **Secondary Green**: `#10B981` (Emerald-500) - Success states, secondary actions
- **Dark Gray**: `#1F2937` (Gray-800) - Text, footer background
- **Light Gray**: `#F9FAFB` (Gray-50) - Background, subtle sections

#### Extended Color System
- **Background Gradient**: Linear gradient from `#F9FAFB` to `#FFFFFF`
- **Text Colors**: Black (`#000000`) for primary text, various gray shades for secondary text
- **Status Colors**:
  - Success: Green variants (`#10B981`, `#34D399`, `#86EFAC`)
  - Error: Red variants (`#EF4444`, `#F87171`)
  - Warning: Yellow variants (`#F59E0B`, `#FCD34D`)
  - Info: Blue variants (`#3B82F6`, `#60A5FA`)

### Typography
- **Primary Font**: Inter (Google Fonts) - Clean, modern sans-serif for body text
- **Brand Font**: Nunito (Google Fonts, weight 700) - Used for logo and brand elements
- **Template Fonts**:
  - IBM Plex Sans - Modern templates
  - Times New Roman - Classic/formal templates
  - Arial - Minimalist templates

#### Typography Scale
- **Headings**: `text-3xl` (30px), `text-2xl` (24px), `text-xl` (20px), `text-lg` (18px)
- **Body Text**: `text-base` (16px), `text-sm` (14px)
- **Small Text**: `text-xs` (12px)
- **Font Weights**: Regular (400), Medium (500), Semibold (600), Bold (700), Extrabold (800)

### Component Design System

#### Buttons
```css
/* Primary Button */
.btn-primary {
  @apply bg-primary hover:bg-blue-700 text-white font-bold py-2 px-4 rounded transition-colors;
}

/* Secondary Button */
.btn-secondary {
  @apply bg-secondary hover:bg-green-700 text-white font-bold py-2 px-4 rounded transition-colors;
}
```

#### Form Elements
```css
/* Input Fields */
.input-field {
  @apply border border-gray-300 rounded-md p-2 w-full focus:outline-none focus:ring-2 focus:ring-primary;
}
```

#### Cards & Containers
```css
/* Standard Card */
.card {
  @apply bg-white shadow-md rounded-lg p-6;
}
```

### UI Patterns & Components

#### Navigation
- **Fixed Header**: White background with subtle shadow (`shadow-md`)
- **Logo**: SVG logo with invert filter, paired with Nunito font brand name
- **Navigation Links**: Hover states with primary color, active states with border indicators
- **Mobile Menu**: Collapsible with slide-in animation, left border accent for active items

#### Banners & Promotions
- **Sticky Banners**: Gradient backgrounds (`from-purple-600 via-pink-600 to-red-600`)
- **Animated Elements**: Spinning star icons (`animate-spin`), pulsing effects (`animate-pulse`)
- **Token Bonuses**: Prominent display with emoji and gradient backgrounds

#### Modals & Dialogs
- **Overlay**: Semi-transparent black background (`bg-black bg-opacity-50`)
- **Modal Container**: White background, rounded corners (`rounded-lg`), shadow (`shadow-xl`)
- **Headers**: Border bottom separation (`border-b border-gray-200`)

#### Loading States
- **Spinner Animation**: Custom SVG with `animate-spin` class
- **Loading Text**: Paired with spinner icons for context
- **Progress Indicators**: Subtle gray backgrounds during content loading

#### Status & Feedback
- **Toast Notifications**:
  - Success: Green background with green border-left
  - Error: Red background with red border-left
  - Info: Blue background with blue border-left
- **Status Cards**: Color-coded backgrounds and icons for payment states
- **Form Validation**: Focus rings with primary color, error states with red accents

### Layout & Spacing

#### Grid System
- **Container**: `max-w-7xl mx-auto` for main content areas
- **Responsive Padding**: `px-4 sm:px-6 lg:px-8` for consistent spacing
- **Grid Layouts**: CSS Grid and Flexbox for responsive layouts

#### Spacing Scale
- **Micro Spacing**: `space-x-1` (4px), `space-x-2` (8px)
- **Small Spacing**: `space-x-3` (12px), `space-x-4` (16px)
- **Medium Spacing**: `space-x-6` (24px), `space-x-8` (32px)
- **Large Spacing**: `py-12` (48px), `py-16` (64px)

### Responsive Design

#### Breakpoints
- **Mobile**: Default (< 640px)
- **Small**: `sm:` (≥ 640px)
- **Large**: `lg:` (≥ 1024px)

#### Mobile-First Approach
- Touch-friendly button sizes (minimum 44px)
- Optimized form inputs for mobile keyboards
- Collapsible navigation for smaller screens
- Responsive typography scaling

### Animation & Interactions

#### Transitions
- **Standard**: `transition-colors` for color changes
- **Duration**: 300ms for most interactions
- **Easing**: Default ease-in-out for smooth animations

#### Hover States
- **Buttons**: Color darkening on hover
- **Links**: Color change to primary blue
- **Cards**: Subtle shadow increase (where applicable)

#### Loading Animations
- **Spin**: `animate-spin` for loading spinners
- **Pulse**: `animate-pulse` for attention-grabbing elements

### Accessibility Features
- **Focus Indicators**: Visible focus rings with primary color
- **Color Contrast**: WCAG compliant color combinations
- **Touch Targets**: Minimum 44px for interactive elements
- **Screen Reader**: Semantic HTML and ARIA labels where needed

### Brand Identity
- **Logo**: Minimalist SVG design with invert filter for dark backgrounds
- **Brand Colors**: Professional blue and green combination
- **Voice**: Modern, approachable, professional
- **Visual Style**: Clean, minimal, focused on usability

### Backend Stack
- **API Routes**: Next.js API routes
- **Database**: Supabase PostgreSQL
- **Authentication**: Supabase Auth
- **File Storage**: Supabase Storage for resume uploads
- **Analytics**: Mixpanel integration
- **Error Monitoring**: Rollbar

### AI Integration
- **Primary AI**: Google Gemini 2.0 Flash
- **Secondary AI**: OpenAI GPT-4o-mini (for templating)
- **Document Processing**: Support for PDF and DOCX files
- **Image Processing**: Job poster analysis capabilities

### Infrastructure
- **Hosting**: Netlify (web app)
- **Edge Functions**: Netlify Edge Functions
- **CDN**: Netlify CDN
- **Environment**: Production and development configurations

## 📁 Project Structure

```
web/
├── app/                          # Next.js App Router
│   ├── api/                      # API routes
│   │   ├── generate-application-letter/
│   │   ├── generate-email-application/
│   │   ├── generate-job-matching/
│   │   └── payment/              # Payment processing
│   ├── application-letter/       # Application letter page
│   ├── email-application/        # Email application page
│   ├── job-match/               # Job matching page
│   ├── auth/                    # Authentication pages
│   ├── buy-tokens/              # Token purchase
│   └── profile/                 # User profile
├── components/                   # Reusable UI components
│   ├── Navbar.tsx
│   ├── Footer.tsx
│   ├── Toast.tsx
│   └── ...
├── hooks/                       # Custom React hooks
│   ├── useAuth.ts
│   ├── useForm.ts
│   └── useResume.ts
├── lib/                         # Utility libraries
│   ├── supabase.ts
│   ├── mixpanel.ts
│   └── rollbar.ts
├── utils/                       # Utility functions
│   ├── ai-generators/           # AI generation logic
│   ├── letter-templates/        # Application letter templates
│   └── hooks/                   # Additional hooks
├── types/                       # TypeScript type definitions
└── public/                      # Static assets
```

## 🔧 Development Setup

### Prerequisites
- Node.js 18+ 
- npm or yarn
- Supabase account
- Google AI API key
- OpenAI API key

### Environment Variables
Create `.env.local` in the web directory:

```bash
# Supabase
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# AI APIs
GOOGLE_AI_API_KEY=your_google_ai_key
OPENAI_API_KEY=your_openai_key

# Payment (Midtrans)
MIDTRANS_SERVER_KEY=your_server_key
MIDTRANS_CLIENT_KEY=your_client_key
MIDTRANS_IS_PRODUCTION=false

# Analytics
NEXT_PUBLIC_MIXPANEL_PROJECT_TOKEN=your_mixpanel_token
ROLLBAR_ACCESS_TOKEN=your_rollbar_token

# Other
NEXT_PUBLIC_SITE_URL=http://localhost:3000
```

### Installation

```bash
cd web
npm install
npm run dev
```

The application will be available at `http://localhost:3000`.

## 🗄️ Database Schema

### Key Tables
- **profiles**: User profiles with token balances and resume storage
- **purchases**: Token purchase history and payment tracking
- **referrals**: Referral program data (planned)
- **referral_codes**: Referral codes (planned)

## 🔄 API Endpoints

### Core APIs
- `POST /api/generate-application-letter` - Generate application letters
- `POST /api/generate-email-application` - Generate email applications  
- `POST /api/generate-job-matching` - Analyze job compatibility
- `POST /api/payment/create` - Create payment sessions
- `POST /api/payment/webhook` - Handle payment notifications

## 🎯 Key Features in Detail

### AI Content Generation
- **Resume Processing**: Extracts text from PDF/DOCX files
- **Job Analysis**: Processes job descriptions and images
- **Personalization**: Tailors content to specific job requirements
- **Template System**: Multiple professional templates available

### Token Economy
- **Fair Usage**: Token-based system prevents abuse
- **Flexible Packages**: Multiple pricing tiers
- **Bonus System**: Package bonus tokens and planned referral rewards
- **Purchase Tracking**: Complete transaction history

### User Experience
- **Responsive Design**: Mobile-first approach
- **Real-time Updates**: Live generation progress
- **Error Handling**: Comprehensive error management
- **Analytics**: User behavior tracking

## 📊 Analytics & Monitoring

### Mixpanel Events
- User registration and authentication
- Feature usage (letter generation, job matching)
- Payment events and conversions
- Error tracking and performance metrics

### Error Monitoring
- Rollbar integration for error tracking
- API performance monitoring
- User experience issue detection

## 🚀 Deployment

### Netlify Configuration
- Automatic deployments from Git
- Edge functions for API routes
- Environment variable management
- CDN optimization

### Production Considerations
- Environment-specific configurations
- Database migrations
- Payment webhook setup
- Analytics configuration

## 📈 Future Roadmap

### Planned Features
1. **Referral Program**: User referral system with token rewards
2. **Advanced Templates**: More design options for application letters
3. **Interview Preparation**: AI-powered interview question generation
4. **Career Insights**: Industry-specific career advice
5. **Enhanced Analytics**: Advanced user behavior tracking

### Technical Improvements
- Performance optimizations
- Enhanced AI models
- Better caching strategies
- Improved user interface

## 🤝 Contributing

### Development Guidelines
1. Follow TypeScript best practices
2. Use Tailwind CSS for styling
3. Implement proper error handling
4. Add analytics tracking for new features
5. Write comprehensive tests

### Code Style
- Use ESLint configuration
- Follow Next.js conventions
- Implement responsive design
- Maintain accessibility standards

## 📞 Support

For technical issues or questions:
- Check existing documentation
- Review error logs in Rollbar
- Contact development team

## 🔐 Security & Privacy

### Data Protection
- **Resume Security**: Secure file upload and storage via Supabase
- **Payment Security**: PCI-compliant payment processing
- **User Privacy**: GDPR-compliant data handling
- **API Security**: Rate limiting and authentication

### Authentication Security
- **Multi-provider OAuth**: Google and Facebook integration
- **Session Management**: Secure session handling via Supabase
- **Password Security**: Bcrypt hashing and secure storage
- **Account Recovery**: Secure password reset flow

## 🧪 Testing Strategy

### Testing Approach
- **Unit Tests**: Component and utility function testing
- **Integration Tests**: API endpoint testing
- **E2E Tests**: Critical user flow validation
- **Performance Tests**: Load testing for AI generation

### Quality Assurance
- **Code Reviews**: Mandatory peer reviews
- **Automated Testing**: CI/CD pipeline integration
- **Error Monitoring**: Real-time error tracking
- **Performance Monitoring**: Response time tracking

## 📱 Mobile Responsiveness

### Design Principles
- **Mobile-First**: Optimized for mobile devices
- **Progressive Enhancement**: Enhanced experience on larger screens
- **Touch-Friendly**: Optimized touch targets and interactions
- **Fast Loading**: Optimized images and lazy loading

### Responsive Features
- **Adaptive Navigation**: Collapsible mobile menu
- **Flexible Layouts**: Grid and flexbox layouts
- **Optimized Forms**: Mobile-friendly form inputs
- **Touch Gestures**: Swipe and tap interactions

## 🌐 Internationalization

### Language Support
- **Primary Language**: Indonesian (Bahasa Indonesia)
- **Content Localization**: UI text and messages
- **Date/Time Formatting**: Local timezone support
- **Currency Display**: Indonesian Rupiah (IDR)

### Localization Features
- **Dynamic Content**: AI-generated content in Indonesian
- **Cultural Adaptation**: Local business communication styles
- **Regional Preferences**: Indonesian job market conventions

## 🔧 Troubleshooting

### Common Issues

#### Payment Issues
- **Failed Payments**: Check Midtrans webhook configuration
- **Token Not Added**: Verify webhook processing logs
- **Payment Status**: Use purchase status polling API

#### AI Generation Issues
- **Timeout Errors**: Implement streaming for long requests
- **Invalid Responses**: Check AI model configuration
- **Rate Limits**: Monitor API usage quotas

#### File Upload Issues
- **Large Files**: Implement file size validation
- **Unsupported Formats**: Validate file types
- **Upload Failures**: Check Supabase storage configuration

### Debug Tools
- **Browser DevTools**: Network and console debugging
- **Supabase Dashboard**: Database and auth monitoring
- **Mixpanel**: User behavior analysis
- **Rollbar**: Error tracking and debugging

## 📊 Performance Metrics

### Key Performance Indicators
- **Response Times**: API endpoint performance
- **User Engagement**: Feature usage statistics
- **Conversion Rates**: Registration to purchase conversion
- **Error Rates**: Application stability metrics

### Optimization Strategies
- **Caching**: Redis caching for frequent requests
- **CDN**: Static asset optimization
- **Database**: Query optimization and indexing
- **AI Models**: Response time optimization

## 🔄 Migration Guides

### Payment Migration (Doku → Midtrans)
See `DOKU_MIGRATION.md` for detailed migration steps and considerations.

### Database Migrations
- **Schema Updates**: Use Supabase migration tools
- **Data Migration**: Backup before major changes
- **Version Control**: Track schema changes in Git

## 📋 Changelog

### Recent Updates
- **v1.0.0**: Initial release with core features
- **v1.1.0**: Added job matching functionality
- **v1.2.0**: Implemented token-based pricing
- **v1.3.0**: Payment system migration to Midtrans

### Upcoming Features
- **Referral Program**: User referral system implementation
- **Enhanced Templates**: Additional application letter designs
- **Performance Improvements**: Faster AI generation

## 📝 Documentation Scope

This documentation covers the **Gigsta Web Application** built with Next.js. It includes:

- ✅ **Web Application Features** - AI-powered job application tools
- ✅ **Frontend & Backend Architecture** - Next.js, React, Supabase
- ✅ **API Documentation** - REST endpoints and integrations
- ✅ **Development Setup** - Local development environment
- ✅ **Deployment Guide** - Netlify hosting configuration
- ✅ **Security & Performance** - Best practices and monitoring

For other components of the Gigsta ecosystem, please refer to their respective documentation.

---

**Gigsta Web Application** - AI-powered tools for job seekers to create professional application materials.

*Last updated: December 2024*
