const fs = require('fs');
const path = require('path');
const Handlebars = require('handlebars');

// Load and register helpers
const helpers = require('./utils/handlebars-helpers/index.js');
Object.keys(helpers).forEach(name => {
  Handlebars.registerHelper(name, helpers[name]);
});

// Load templates
const templates = {
  'clean-professional': fs.readFileSync(path.join(__dirname, 'utils/handlebars-templates/clean-professional.hbs'), 'utf8'),
  'modern-clean': fs.readFileSync(path.join(__dirname, 'utils/handlebars-templates/modern-clean.hbs'), 'utf8'),
  'classic-professional': fs.readFileSync(path.join(__dirname, 'utils/handlebars-templates/classic-professional.hbs'), 'utf8')
};

// Compile templates
const compiledTemplates = {};
Object.keys(templates).forEach(name => {
  compiledTemplates[name] = Handlebars.compile(templates[name]);
});

// Test Case 1: Completely empty responsibilities array with whitespace strings
const testCase1 = {
  name: "<PERSON>",
  jobTitle: "Developer",
  email: "<EMAIL>",
  experiences: [
    {
      jobTitle: "Developer",
      company: "Tech Corp",
      startDate: "2022",
      endDate: "Present",
      responsibilities: ["", "   ", "  \n  ", "\t"]  // Only empty/whitespace strings
    }
  ]
};

// Test Case 2: Mixed contact fields (testing the original separator issue)
const testCase2 = {
  name: "Jane Smith",
  jobTitle: "Designer",
  email: "<EMAIL>",
  phone: "", // Empty string
  linkedin: null, // Null
  website: undefined, // Undefined  
  github: "   ", // Whitespace only
  location: "Jakarta"
};

// Test Case 3: All contact fields empty except name
const testCase3 = {
  name: "Empty Contact",
  jobTitle: "Test",
  email: "",
  phone: null,
  linkedin: undefined,
  website: "",
  github: "   ",
  location: ""
};

// Test Case 4: Skills with empty categories and values
const testCase4 = {
  name: "Skill Test",
  jobTitle: "Tester",
  skillCategories: [
    {
      category: "",
      skills: "JavaScript, Python"
    },
    {
      category: "Tools",
      skills: ""
    },
    {
      category: "   ",
      skills: "   "
    }
  ]
};

const testCases = [
  { name: "Empty Responsibilities Only", data: testCase1 },
  { name: "Mixed Contact Fields", data: testCase2 },
  { name: "All Empty Contact", data: testCase3 },
  { name: "Empty Skills", data: testCase4 }
];

console.log('=== EXTREME EDGE CASE TESTING ===\n');

testCases.forEach((testCase, index) => {
  console.log(`\n=== TEST CASE ${index + 1}: ${testCase.name} ===`);
  
  Object.keys(compiledTemplates).forEach(templateName => {
    console.log(`\n--- ${templateName} Template ---`);
    
    try {
      const html = compiledTemplates[templateName](testCase.data);
      
      // Detailed analysis
      const issues = [];
      
      // Check for empty list items (more thorough)
      const emptyLi = html.match(/<li>\s*<\/li>/g);
      if (emptyLi) {
        issues.push(`${emptyLi.length} completely empty <li> elements`);
      }
      
      const whitespaceLi = html.match(/<li>\s+<\/li>/g);
      if (whitespaceLi) {
        issues.push(`${whitespaceLi.length} whitespace-only <li> elements`);
      }
      
      // Check contact info section specifically
      const contactMatch = html.match(/<div class="contact-info"[^>]*>(.*?)<\/div>/s);
      if (contactMatch) {
        const contactContent = contactMatch[1];
        
        // Check for trailing separators
        if (contactContent.includes('• ') || contactContent.includes('| ')) {
          const trailingBullet = contactContent.match(/•\s*$/);
          const trailingPipe = contactContent.match(/\|\s*$/);
          if (trailingBullet) issues.push('Trailing bullet separator (•) found');
          if (trailingPipe) issues.push('Trailing pipe separator (|) found');
          
          // Check for double separators
          const doubleBullet = contactContent.match(/•\s*•/);
          const doublePipe = contactContent.match(/\|\s*\|/);
          if (doubleBullet) issues.push('Double bullet separators found');
          if (doublePipe) issues.push('Double pipe separators found');
        }
        
        console.log(`Contact Output: "${contactContent.trim()}"`);
      }
      
      // Check for empty sections that should be hidden
      const emptyUL = html.match(/<ul[^>]*>\s*<\/ul>/g);
      if (emptyUL) {
        issues.push(`${emptyUL.length} empty <ul> elements`);
      }
      
      // Check for skills sections with empty content
      const skillsMatch = html.match(/<div class="skill-category"[^>]*>\s*<\/div>/g);
      if (skillsMatch) {
        issues.push(`${skillsMatch.length} empty skill category divs`);
      }
      
      if (issues.length === 0) {
        console.log('✅ PASSED - No issues detected');
      } else {
        console.log('❌ ISSUES FOUND:');
        issues.forEach(issue => console.log(`  - ${issue}`));
      }
      
    } catch (error) {
      console.log(`❌ TEMPLATE ERROR: ${error.message}`);
    }
  });
});

// Test the helpers with edge cases
console.log('\n=== HELPER FUNCTION EDGE CASE TESTS ===\n');

console.log('Testing hasContent with various inputs:');
const testInputs = ["test", "", "   ", "\n", "\t", null, undefined, 0, false];
testInputs.forEach(input => {
  console.log(`hasContent(${JSON.stringify(input)}): ${helpers.hasContent(input)}`);
});

console.log('\nTesting filterEmpty with extreme arrays:');
const extremeArrays = [
  [],
  [""],
  ["   "],
  [null, undefined, "", "   "],
  ["valid", "", "   ", null, "another", "\t"],
  [0, false, "", "valid"] // Mixed types
];

extremeArrays.forEach((arr, i) => {
  console.log(`Array ${i + 1} ${JSON.stringify(arr)} → ${JSON.stringify(helpers.filterEmpty(arr))}`);
});

console.log('\nTesting contact helpers with all empty values:');
console.log(`contactArray(all empty): ${JSON.stringify(helpers.contactArray("", null, undefined, "   ", "", ""))}`);
console.log(`joinContact(all empty): "${helpers.joinContact("", null, undefined, "   ", "", "", " • ")}"`);