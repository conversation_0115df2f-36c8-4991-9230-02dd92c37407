import { NextResponse } from 'next/server';

/**
 * Applies cache prevention headers to a NextResponse object.
 * Prevents caching in both Netlify and Next.js
 * 
 * @param response - The NextResponse object to apply headers to
 * @returns The same response with cache prevention headers applied
 */
export function preventCaching(response: NextResponse): NextResponse {
  // Netlify-specific cache prevention
  response.headers.set("Netlify-Vary", "cookie");
  
  // Standard cache prevention headers (works for Next.js and browsers)
  response.headers.set('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate');
  response.headers.set('Pragma', 'no-cache');
  response.headers.set('Expires', '0');
  
  // For dynamic content that should never be cached
  response.headers.set('Surrogate-Control', 'no-store');
  
  return response;
}
