import { createClient } from "@/lib/supabase";

/**
 * Download resume as PDF using Supabase Edge Function
 */
export async function downloadResumePDF(resumeId: string): Promise<Blob> {
  const supabase = createClient();

  // Get the current session
  const { data: { session }, error: sessionError } = await supabase.auth.getSession();
  
  if (sessionError || !session) {
    throw new Error('Authentication required');
  }

  // Use fetch directly for binary responses
  // The supabase.functions.invoke() doesn't handle binary data well
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
  
  if (!supabaseUrl || !supabaseAnonKey) {
    throw new Error('Supabase configuration missing');
  }

  const response = await fetch(`${supabaseUrl}/functions/v1/download-resume`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${session.access_token}`,
    },
    body: JSON.stringify({
      resumeId
    })
  });

  if (!response.ok) {
    // Try to parse error message from JSON response
    let errorMessage = 'Failed to download resume';
    try {
      const contentType = response.headers.get('Content-Type');
      if (contentType && contentType.includes('application/json')) {
        const errorData = await response.json();
        errorMessage = errorData.error || errorMessage;
      } else {
        errorMessage = response.statusText || `Server error (${response.status})`;
      }
    } catch {
      errorMessage = response.statusText || `Server error (${response.status})`;
    }
    
    throw new Error(errorMessage);
  }

  // Get the response as a blob
  const blob = await response.blob();

  return blob;
}

/**
 * Download letter as PDF using Supabase Edge Function
 */
export async function downloadLetterPDF(letterId: string): Promise<Blob> {
  const supabase = createClient();

  // Get the current session
  const { data: { session }, error: sessionError } = await supabase.auth.getSession();
  
  if (sessionError || !session) {
    throw new Error('Authentication required');
  }

  // Use fetch directly for binary responses
  // The supabase.functions.invoke() doesn't handle binary data well
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
  
  if (!supabaseUrl || !supabaseAnonKey) {
    throw new Error('Supabase configuration missing');
  }

  const response = await fetch(`${supabaseUrl}/functions/v1/download-letter`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${session.access_token}`,
    },
    body: JSON.stringify({
      letterId
    })
  });

  if (!response.ok) {
    // Try to parse error message from JSON response
    let errorMessage = 'Failed to download letter';
    try {
      const contentType = response.headers.get('Content-Type');
      if (contentType && contentType.includes('application/json')) {
        const errorData = await response.json();
        errorMessage = errorData.error || errorMessage;
      } else {
        errorMessage = response.statusText || `Server error (${response.status})`;
      }
    } catch {
      errorMessage = response.statusText || `Server error (${response.status})`;
    }
    
    throw new Error(errorMessage);
  }

  // Get the response as a blob
  const blob = await response.blob();

  return blob;
}