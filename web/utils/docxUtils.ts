import mammoth from 'mammoth';

/**
 * Extracts text from a DOCX file buffer
 * @param buffer - The file buffer containing DOCX data
 * @returns Promise with the extracted text
 */
export async function extractTextFromDocx(buffer: ArrayBuffer): Promise<string> {
  try {
    // Convert ArrayBuffer to Buffer for mammoth
    const nodeBuffer = Buffer.from(buffer);
    
    // Use the correct API call with arrayBuffer
    const result = await mammoth.extractRawText({
      buffer: nodeBuffer
    });
    
    return result.value || '';
  } catch (error) {
    console.error('Error extracting text from DOCX:', error);
    throw new Error('Failed to extract text from DOCX file');
  }
}