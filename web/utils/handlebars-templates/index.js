// Entry point for Handlebars template compilation
// This file serves as the webpack entry point and exports all compiled templates

const path = require('path');

// Template exports will be populated by webpack compilation
module.exports = {
  // Placeholder - actual templates will be compiled by webpack
  templates: {},
  
  // Helper function to get template by ID
  getTemplate: function(templateId) {
    return this.templates[templateId];
  },
  
  // Helper function to render template with data
  renderTemplate: function(templateId, data) {
    const template = this.getTemplate(templateId);
    if (!template) {
      throw new Error(`Template not found: ${templateId}`);
    }
    return template(data);
  }
};