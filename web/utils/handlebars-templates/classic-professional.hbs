<style>
    .resume-container * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }

    .resume-container {
        font-family: Arial, sans-serif;
        font-size: 10pt;
        line-height: 1.4;
        color: #000000;
        background-color: white;
        width: 210mm;
        min-height: 297mm;
        margin: 0 auto;
        padding: 10mm 15mm;
        box-sizing: border-box;
        overflow: visible;
    }

    /* Header Section */
    .resume-container .header {
        text-align: center;
        margin-bottom: 20px;
        border-bottom: 1px solid #000000;
        padding-bottom: 15px;
    }

    .resume-container .name {
        font-size: 20pt;
        font-weight: bold;
        color: #000000;
        text-transform: uppercase;
    }

    .resume-container .title {
        font-size: 12pt;
        color: #000000;
        margin-bottom: 6px;
        text-transform: none;
    }

    .resume-container .contact-info {
        font-size: 9pt;
        color: #000000;
        line-height: 1.3;
    }

    /* Section Styling */
    .resume-container .section {
        margin-bottom: 20px;
    }

    .resume-container .section-title {
        font-size: 11pt;
        font-weight: bold;
        color: #000000;
        text-transform: uppercase;
        margin-bottom: 10px;
        border-bottom: 1px solid #000000;
        padding-bottom: 2px;
    }

    /* Experience and Education */
    .resume-container .experience-item,
    .resume-container .education-item {
        margin-bottom: 15px;
    }

    .resume-container .job-title,
    .resume-container .degree {
        font-size: 10pt;
        font-weight: bold;
        color: #000000;
        margin-bottom: 2px;
    }

    .resume-container .company,
    .resume-container .school {
        font-size: 10pt;
        color: #000000;
        font-weight: normal;
        margin-bottom: 2px;
    }

    .resume-container .date {
        font-size: 9pt;
        color: #000000;
        margin-bottom: 5px;
    }

    .resume-container .description {
        font-size: 10pt;
        color: #000000;
        margin-left: 0;
    }

    .resume-container .description ul {
        margin-left: 20px;
        margin-top: 3px;
    }

    .resume-container .description li {
        margin-bottom: 2px;
        list-style-type: disc;
    }

    /* Skills Section */
    .resume-container .skills-list {
        font-size: 10pt;
        color: #000000;
    }

    .resume-container .skill-category {
        margin-bottom: 8px;
        line-height: 1.4;
    }

    .resume-container .skill-category strong {
        font-weight: bold;
        color: #000000;
    }

    /* Summary */
    .resume-container .summary {
        font-size: 10pt;
        color: #000000;
        line-height: 1.4;
        text-align: left;
    }

    /* Certifications */
    .resume-container .certification-item {
        margin-bottom: 5px;
        font-size: 10pt;
        color: #000000;
    }

    /* Multi-page support */
    .resume-section {
        page-break-inside: avoid;
        break-inside: avoid;
    }

    .resume-container .experience-item,
    .resume-container .education-item,
    .resume-container .project-item {
        page-break-inside: avoid;
        break-inside: avoid;
        margin-bottom: 12px;
    }

    /* Print styles for A4 */
    @media print {
        .resume-container {
            width: 210mm !important;
            min-height: 297mm !important;
            margin: 0 !important;
            padding: 10mm 15mm !important;
            box-sizing: border-box !important;
        }

        .resume-section {
            page-break-inside: avoid !important;
            break-inside: avoid !important;
        }
    }

    /* PDF generation styles */
    @page {
        size: A4;
        margin: 0;
    }
</style>
<div class="resume-container">
    <!-- Header -->
    <div class="header">
        <div class="name">{{name}}</div>
        <div class="title">{{jobTitle}}</div>
        <div class="contact-info">
            {{joinContact email phone linkedin website github location ' | '}}
        </div>
    </div>

    <!-- Professional Summary -->
    {{#if summary}}
    <div class="section">
        <div class="section-title">Ringkasan Profesional</div>
        <div class="summary">
            {{summary}}
        </div>
    </div>
    {{/if}}

    <!-- Experience -->
    {{#if experiences}}
    <div class="section">
        <div class="section-title">Pengalaman Profesional</div>
        {{#each experiences}}
        <div class="experience-item">
            <div class="job-title">{{jobTitle}}</div>
            <div class="company">{{company}}</div>
            <div class="date">{{startDate}} - {{endDate}}</div>
            {{#if responsibilities}}
            <div class="description">
                <ul>
                    {{#each responsibilities}}
                    {{#if (hasContent this)}}<li>{{this}}</li>{{/if}}
                    {{/each}}
                </ul>
            </div>
            {{/if}}
        </div>
        {{/each}}
    </div>
    {{/if}}

    <!-- Education -->
    {{#if education}}
    <div class="section">
        <div class="section-title">Pendidikan</div>
        {{#each education}}
        <div class="education-item">
            <div class="degree">{{degree}}</div>
            <div class="school">{{institution}}</div>
            {{#if location}}<div class="school">{{location}}</div>{{/if}}
            <div class="date">{{graduationDate}}</div>
            {{#if gpa}}<div style="font-size: 9pt; color: #000000; margin-top: 2px;">IPK: {{gpa}}</div>{{/if}}
            {{#if relevantCoursework}}
            <div style="margin-top: 3px; font-size: 9pt; color: #000000;">
                <strong>Mata Kuliah Terkait:</strong> {{relevantCoursework}}
            </div>
            {{/if}}
            {{#if honors}}
            <div style="margin-top: 2px; font-size: 9pt; color: #000000;">
                <strong>Penghargaan:</strong> {{honors}}
            </div>
            {{/if}}
        </div>
        {{/each}}
    </div>
    {{/if}}

    <!-- Skills -->
    {{#if skills}}
    <div class="section">
        <div class="section-title">Keahlian</div>
        <div class="skills-list">
            {{#each skills}}
            {{#if (hasContent skills)}}
            <div class="skill-category">
                {{#if (hasContent category)}}<strong>{{category}}:</strong>{{/if}} {{skills}}
            </div>
            {{/if}}
            {{/each}}
        </div>
    </div>
    {{/if}}

    <!-- Projects -->
    {{#if projects}}
    <div class="section">
        <div class="section-title">Proyek Utama</div>
        {{#each projects}}
        <div class="project-item">
            <div class="project-title">{{title}}</div>
            {{#if link}}<div style="font-size: 9pt; color: #000000; margin-bottom: 2px;">{{link}}</div>{{/if}}
            {{#if description}}<div class="project-description">{{description}}</div>{{/if}}
            {{#if technologies}}<div class="project-tech">Teknologi: {{technologies}}</div>{{/if}}
            {{#if achievements}}
            <ul style="margin-left: 20px; margin-top: 3px; font-size: 10pt; color: #000000;">
                {{#each achievements}}
                {{#if (hasContent this)}}<li>{{this}}</li>{{/if}}
                {{/each}}
            </ul>
            {{/if}}
        </div>
        {{/each}}
    </div>
    {{/if}}

    <!-- Certifications -->
    {{#if certifications}}
    <div class="section">
        <div class="section-title">Sertifikasi</div>
        <div>
            {{#each certifications}}
            <div class="certification-item">
                {{name}} - {{issuer}} ({{date}})
                {{#if credentialId}}<div style="font-size: 9pt; color: #000000; margin-top: 1px;">ID: {{credentialId}}</div>{{/if}}
            </div>
            {{/each}}
        </div>
    </div>
    {{/if}}

    <!-- Languages -->
    {{#if languages}}
    <div class="section">
        <div class="section-title">Bahasa</div>
        <div>
            {{#each languages}}
            {{#if language}}
            <div class="certification-item">
                {{language}}{{#if proficiency}} - {{proficiency}}{{/if}}
            </div>
            {{/if}}
            {{/each}}
        </div>
    </div>
    {{/if}}

    <!-- Awards -->
    {{#if awards}}
    <div class="section">
        <div class="section-title">Penghargaan</div>
        <div>
            {{#each awards}}
            <div class="certification-item">
                <div style="font-weight: bold; color: #000000;">{{title}} - {{issuer}} ({{date}})</div>
                {{#if description}}<div style="font-size: 9pt; color: #000000; margin-top: 2px;">{{description}}</div>{{/if}}
            </div>
            {{/each}}
        </div>
    </div>
    {{/if}}
</div>