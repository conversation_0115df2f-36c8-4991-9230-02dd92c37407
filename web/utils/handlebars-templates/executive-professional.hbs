<style>
    .resume-container * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }

    .resume-container {
        font-family: 'Arial', sans-serif;
        font-size: 10pt;
        line-height: 1.3;
        color: #2c3e50;
        background-color: white;
        width: 210mm;
        height: 297mm;
        margin: 0 auto;
        padding: 12mm 15mm;
        page-break-inside: avoid;
        box-sizing: border-box;
        overflow: hidden;
    }

    /* Header Section */
    .resume-container .header {
        text-align: left;
        margin-bottom: 18px;
        padding-bottom: 12px;
        border-bottom: 3px solid #34495e;
    }

    .resume-container .name {
        font-size: 22pt;
        font-weight: bold;
        color: #2c3e50;
        margin-bottom: 4px;
        letter-spacing: 0.5px;
    }

    .resume-container .title {
        font-size: 13pt;
        color: #34495e;
        margin-bottom: 8px;
        font-weight: 500;
    }

    .resume-container .contact-info {
        font-size: 9pt;
        color: #5d6d7e;
        line-height: 1.4;
        display: flex;
        flex-wrap: wrap;
        gap: 12px;
    }

    .resume-container .contact-item {
        display: flex;
        align-items: center;
        gap: 4px;
    }

    /* Section Styling */
    .resume-container .section {
        margin-bottom: 16px;
    }

    .resume-container .section-title {
        font-size: 12pt;
        font-weight: bold;
        color: #2c3e50;
        text-transform: uppercase;
        margin-bottom: 8px;
        padding-bottom: 3px;
        border-bottom: 2px solid #bdc3c7;
        letter-spacing: 0.8px;
    }

    /* Experience and Education */
    .resume-container .experience-item,
    .resume-container .education-item {
        margin-bottom: 12px;
        padding-left: 0;
    }

    .resume-container .job-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 4px;
    }

    .resume-container .job-title,
    .resume-container .degree {
        font-size: 11pt;
        font-weight: bold;
        color: #2c3e50;
        margin-bottom: 2px;
    }

    .resume-container .company,
    .resume-container .school {
        font-size: 10pt;
        color: #34495e;
        font-weight: 600;
        margin-bottom: 2px;
    }

    .resume-container .date {
        font-size: 9pt;
        color: #7f8c8d;
        font-weight: normal;
        text-align: right;
        white-space: nowrap;
    }

    .resume-container .description {
        font-size: 10pt;
        color: #2c3e50;
        margin-top: 4px;
        line-height: 1.4;
    }

    .resume-container .description ul {
        margin-left: 16px;
        margin-top: 3px;
    }

    .resume-container .description li {
        margin-bottom: 2px;
        list-style-type: disc;
        text-align: justify;
    }

    /* Skills Section */
    .resume-container .skills-list {
        font-size: 10pt;
        color: #2c3e50;
        line-height: 1.5;
    }

    .resume-container .skill-category {
        margin-bottom: 6px;
        display: flex;
        align-items: flex-start;
        gap: 8px;
    }

    .resume-container .skill-category strong {
        font-weight: bold;
        color: #2c3e50;
        min-width: 80px;
        flex-shrink: 0;
    }

    .resume-container .skill-list {
        color: #34495e;
        flex: 1;
    }

    /* Summary */
    .resume-container .summary {
        font-size: 10pt;
        color: #2c3e50;
        line-height: 1.4;
        text-align: justify;
        margin-bottom: 4px;
    }

    /* Projects Section */
    .resume-container .project-item {
        margin-bottom: 10px;
    }

    .resume-container .project-title {
        font-weight: bold;
        color: #2c3e50;
        margin-bottom: 2px;
        font-size: 10pt;
    }

    .resume-container .project-description {
        color: #34495e;
        margin-bottom: 2px;
        font-size: 10pt;
    }

    .resume-container .project-tech {
        font-size: 9pt;
        color: #7f8c8d;
        font-style: italic;
    }

    /* Certifications */
    .resume-container .certification-item {
        margin-bottom: 4px;
        font-size: 10pt;
        color: #2c3e50;
    }

    /* Print styles for A4 */
    @media print {
        .resume-container {
            width: 210mm !important;
            height: 297mm !important;
            margin: 0 !important;
            padding: 12mm 15mm !important;
            page-break-inside: avoid !important;
            box-sizing: border-box !important;
        }
    }

    /* PDF generation styles */
    @page {
        size: A4;
        margin: 0;
    }
</style>
<div class="resume-container">
    <!-- Header -->
    <div class="header">
        <div class="name">{{name}}</div>
        <div class="title">{{jobTitle}}</div>
        <div class="contact-info">
            {{#if email}}<div class="contact-item">✉ {{email}}</div>{{/if}}
            {{#if phone}}<div class="contact-item">📞 {{phone}}</div>{{/if}}
            {{#if linkedin}}<div class="contact-item">🔗 {{linkedin}}</div>{{/if}}
            {{#if website}}<div class="contact-item">🌐 {{website}}</div>{{/if}}
            {{#if github}}<div class="contact-item">💻 {{github}}</div>{{/if}}
            {{#if location}}<div class="contact-item">📍 {{location}}</div>{{/if}}
        </div>
    </div>

    <!-- Professional Summary -->
    {{#if summary}}
    <div class="section">
        <div class="section-title">Ringkasan Profesional</div>
        <div class="summary">
            {{summary}}
        </div>
    </div>
    {{/if}}

    <!-- Experience -->
    {{#if experiences}}
    <div class="section">
        <div class="section-title">Pengalaman Profesional</div>
        {{#each experiences}}
        <div class="experience-item">
            <div class="job-header">
                <div>
                    <div class="job-title">{{jobTitle}}</div>
                    <div class="company">{{company}}</div>
                    {{#if location}}<div style="font-size: 9pt; color: #7f8c8d; margin-top: 1px;">{{location}}</div>{{/if}}
                </div>
                <div class="date">{{startDate}} - {{endDate}}</div>
            </div>
            {{#if responsibilities}}
            <div class="description">
                <ul>
                    {{#each responsibilities}}
                    {{#if (hasContent this)}}<li>{{this}}</li>{{/if}}
                    {{/each}}
                </ul>
            </div>
            {{/if}}
        </div>
        {{/each}}
    </div>
    {{/if}}

    <!-- Education -->
    {{#if education}}
    <div class="section">
        <div class="section-title">Pendidikan</div>
        {{#each education}}
        <div class="education-item">
            <div class="job-header">
                <div>
                    <div class="degree">{{degree}}</div>
                    <div class="school">{{institution}}</div>
                    {{#if location}}<div class="school">{{location}}</div>{{/if}}
                </div>
                <div class="date">{{graduationDate}}</div>
            </div>
            {{#if gpa}}<div style="font-size: 9pt; color: #7f8c8d; margin-top: 2px;">IPK: {{gpa}}</div>{{/if}}
            {{#if relevantCoursework}}
            <div style="margin-top: 3px; font-size: 9pt; color: #7f8c8d;">
                <strong>Mata Kuliah Terkait:</strong> {{relevantCoursework}}
            </div>
            {{/if}}
            {{#if honors}}
            <div style="margin-top: 2px; font-size: 9pt; color: #7f8c8d;">
                <strong>Penghargaan:</strong> {{honors}}
            </div>
            {{/if}}
        </div>
        {{/each}}
    </div>
    {{/if}}

    <!-- Skills -->
    {{#if skills}}
    <div class="section">
        <div class="section-title">Keahlian</div>
        <div class="skills-list">
            {{#each skills}}
            {{#if (hasContent skills)}}
            <div class="skill-category">
                {{#if (hasContent category)}}<strong>{{category}}:</strong>{{/if}}
                <div class="skill-list">{{skills}}</div>
            </div>
            {{/if}}
            {{/each}}
        </div>
    </div>
    {{/if}}

    <!-- Projects -->
    {{#if projects}}
    <div class="section">
        <div class="section-title">Proyek Utama</div>
        {{#each projects}}
        <div class="project-item">
            <div class="project-title">{{title}}</div>
            {{#if link}}<div style="font-size: 9pt; color: #3498db; margin-bottom: 2px;">{{link}}</div>{{/if}}
            {{#if description}}<div class="project-description">{{description}}</div>{{/if}}
            {{#if technologies}}<div class="project-tech">Teknologi: {{technologies}}</div>{{/if}}
            {{#if achievements}}
            <ul style="margin-left: 16px; margin-top: 3px; font-size: 10pt; color: #2c3e50;">
                {{#each achievements}}
                {{#if (hasContent this)}}<li>{{this}}</li>{{/if}}
                {{/each}}
            </ul>
            {{/if}}
        </div>
        {{/each}}
    </div>
    {{/if}}

    <!-- Certifications -->
    {{#if certifications}}
    <div class="section">
        <div class="section-title">Sertifikasi</div>
        <div>
            {{#each certifications}}
            <div class="certification-item">
                <strong>{{name}}</strong> - {{issuer}} ({{date}})
                {{#if credentialId}}<div style="font-size: 9pt; color: #7f8c8d; margin-top: 1px;">ID: {{credentialId}}</div>{{/if}}
            </div>
            {{/each}}
        </div>
    </div>
    {{/if}}

    <!-- Languages -->
    {{#if languages}}
    <div class="section">
        <div class="section-title">Bahasa</div>
        <div>
            {{#each languages}}
            {{#if language}}
            <div class="certification-item">
                {{language}}{{#if proficiency}} - {{proficiency}}{{/if}}
            </div>
            {{/if}}
            {{/each}}
        </div>
    </div>
    {{/if}}

    <!-- Awards -->
    {{#if awards}}
    <div class="section">
        <div class="section-title">Penghargaan</div>
        <div>
            {{#each awards}}
            <div class="certification-item">
                <div style="font-weight: bold; color: #2c3e50;">{{title}} - {{issuer}} ({{date}})</div>
                {{#if description}}<div style="font-size: 9pt; color: #7f8c8d; margin-top: 2px;">{{description}}</div>{{/if}}
            </div>
            {{/each}}
        </div>
    </div>
    {{/if}}
</div>
