<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Surat <PERSON><PERSON></title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=IBM+Plex+Sans:wght@400;500;600&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'IBM Plex Sans', sans-serif; }
        .a4-page { height: 297mm; width: 210mm; margin: 0 auto; overflow: hidden; }
        .letter-content { height: 100%; display: flex; flex-direction: column; font-size: 14px; line-height: 1.6; position: relative; }
        .content-body { flex-grow: 1; }
        .letter-spacing-wide { letter-spacing: 0.05em; }
        .space-y-5 > * + * { margin-top: 1.25rem; }
        .signature-section { position: absolute; bottom: 60px; left: 40px; right: 40px; }
        .bottom-decoration { position: absolute; bottom: 32px; right: 40px; }
        @media print { .a4-page { margin: 0; } }
    </style>
</head>
<body class="bg-white m-0 p-0">
    <div class="a4-page bg-white">
        <div class="letter-content">
            <!-- Minimal Header -->
            <div class="px-10 pt-8 pb-6">
                <div class="text-center mb-6">
                    <h1 class="text-xl font-medium text-gray-800 letter-spacing-wide">SURAT LAMARAN KERJA</h1>
                    <div class="flex justify-center items-center mt-3">
                        <div class="w-2 h-2 bg-yellow-400 rounded-full"></div>
                        <div class="w-8 h-px bg-yellow-300 mx-1"></div>
                        <div class="w-1 h-1 bg-yellow-200 rounded-full"></div>
                    </div>
                </div>
                
                <div class="flex justify-between items-center mb-6">
                    <div class="text-gray-700">
                        <p class="text-sm">{{subject}}</p>
                    </div>
                    <div class="text-right text-yellow-600">
                        <p class="font-medium">{{date}}</p>
                    </div>
                </div>
                
                <div class="text-gray-700">
                    {{#each recipientLines}}
                    <p{{#if @first}} class="font-medium"{{/if}}>{{this}}</p>
                    {{/each}}
                </div>
            </div>
            
            <!-- Content Body -->
            <div class="content-body px-10 pb-40">
                <div class="space-y-5 text-justify text-gray-700">
                    <div class="mb-5">
                        <div class="w-3 h-px bg-yellow-400 mb-3"></div>
                        <p>{{opening}}</p>
                    </div>
                    
                    {{#each paragraphs}}
                    <p>{{this}}</p>
                    {{/each}}
                    
                    {{#if closing}}
                    <p>{{closing}}</p>
                    {{/if}}
                </div>
            </div>
            
            <!-- Signature Section - Fixed at Bottom -->
            <div class="signature-section">
                <p class="font-medium text-gray-800 mb-16">{{farewell}}</p>
                <div class="flex items-end justify-between">
                    <div>
                        <p class="font-medium text-gray-800">{{signatureName}}</p>
                        <div class="w-24 h-px bg-yellow-400 mt-2"></div>
                        {{#if additionalInfo}}
                        <p class="text-gray-600 text-sm mt-2">{{additionalInfo}}</p>
                        {{/if}}
                    </div>
                </div>
            </div>

            <!-- Bottom Decoration - Separate from signature -->
            <div class="bottom-decoration">
                <div class="flex items-center">
                    <div class="w-1 h-1 bg-yellow-200 rounded-full"></div>
                    <div class="w-2 h-px bg-yellow-300 mx-1"></div>
                    <div class="w-1 h-1 bg-yellow-400 rounded-full"></div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>