<style>
    .resume-container * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }

    .resume-container {
        font-family: 'Arial', sans-serif;
        font-size: 10pt;
        line-height: 1.4;
        color: #333;
        margin: 0 auto;
        padding: 10mm 15mm;
        background-color: white;
        width: 210mm;
        height: 297mm;
        page-break-inside: avoid;
        box-sizing: border-box;
        overflow: hidden;
    }

    .resume-container .header {
        text-align: center;
        margin-bottom: 20px;
    }

    .resume-container .name {
        font-size: 20pt;
        font-weight: bold;
        color: #2c3e50;
    }

    .resume-container .title {
        font-size: 12pt;
        color: #7f8c8d;
        margin-bottom: 6px;
    }

    .resume-container .contact-info {
        font-size: 9pt;
        color: #666;
        line-height: 1.4;
    }

    .resume-container .contact-info span {
        margin: 0 2px;
    }

    .resume-container .section {
        margin-bottom: 18px;
    }

    .resume-container .section-title {
        font-size: 11pt;
        font-weight: bold;
        color: #2c3e50;
        text-transform: uppercase;
        border-bottom: 2px solid #bdc3c7;
        padding-bottom: 3px;
        margin-bottom: 12px;
    }

    .resume-container .experience-item, .resume-container .education-item {
        margin-bottom: 15px;
    }

    .resume-container .job-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 3px;
    }

    .resume-container .job-title, .resume-container .degree {
        font-weight: bold;
        font-size: 10pt;
        color: #34495e;
    }

    .resume-container .company, .resume-container .school {
        font-size: 10pt;
        color: #7f8c8d;
        margin-bottom: 5px;
    }

    .resume-container .date {
        font-size: 9pt;
        color: #95a5a6;
        font-weight: normal;
    }

    .resume-container .description {
        font-size: 10pt;
        margin-top: 5px;
        line-height: 1.4;
    }

    .resume-container .description ul {
        margin-left: 18px;
        margin-top: 5px;
    }

    .resume-container .description li {
        margin-bottom: 3px;
    }

    .resume-container .skills-list {
        line-height: 1.5;
    }

    .resume-container .skill-category {
        font-size: 10pt;
        margin-bottom: 8px;
    }

    .resume-container .skill-category strong {
        color: #2c3e50;
    }

    .resume-container .content-area {
        overflow: hidden;
    }

    /* Projects Section */
    .resume-container .project-item {
        margin-bottom: 10px;
    }

    .resume-container .project-title {
        font-weight: bold;
        color: #2c3e50;
        margin-bottom: 3px;
    }

    .resume-container .project-description {
        color: #34495e;
        margin-bottom: 2px;
    }

    .resume-container .project-tech {
        font-size: 9pt;
        color: #7f8c8d;
        font-style: italic;
    }

    .resume-container .project-url {
        font-size: 9pt;
        color: #3498db;
        text-decoration: underline;
    }

    /* Print styles for A4 */
    @media print {
        .resume-container {
            width: 210mm !important;
            height: 297mm !important;
            margin: 0 !important;
            padding: 10mm 15mm !important;
            page-break-inside: avoid !important;
            box-sizing: border-box !important;
        }
    }

    /* PDF generation styles */
    @page {
        size: A4;
        margin: 0;
    }
</style>
<div class="resume-container">
    <div class="header">
        <div class="name">{{name}}</div>
        <div class="title">{{jobTitle}}</div>
        <div class="contact-info">
            {{joinContact email phone linkedin website github location ' • '}}
        </div>
    </div>

    <div class="content-area">
        {{#if summary}}
        <div class="section">
            <div class="section-title">Ringkasan Profesional</div>
            <div class="description">
                {{summary}}
            </div>
        </div>
        {{/if}}

        {{#if experiences}}
        <div class="section">
            <div class="section-title">Pengalaman</div>
            {{#each experiences}}
            <div class="experience-item">
                <div class="job-header">
                    <div>
                        <div class="job-title">{{jobTitle}}</div>
                        <div class="company">{{company}}</div>
                    </div>
                    <div class="date">{{startDate}} - {{endDate}}</div>
                </div>
                {{#if responsibilities}}
                <div class="description">
                    <ul>
                        {{#each responsibilities}}
                        {{#if (hasContent this)}}<li>{{this}}</li>{{/if}}
                        {{/each}}
                    </ul>
                </div>
                {{/if}}
            </div>
            {{/each}}
        </div>
        {{/if}}

        {{#if education}}
        <div class="section">
            <div class="section-title">Pendidikan</div>
            {{#each education}}
            <div class="education-item">
                <div class="job-header">
                    <div>
                        <div class="degree">{{degree}}</div>
                        <div class="school">{{institution}}</div>
                        {{#if location}}<div class="school">{{location}}</div>{{/if}}
                    </div>
                    <div class="date">{{graduationDate}}</div>
                </div>
                {{#if gpa}}<div style="font-size: 9pt; color: #95a5a6; margin-top: 2px;">IPK: {{gpa}}</div>{{/if}}
                {{#if relevantCoursework}}
                <div style="margin-top: 3px; font-size: 9pt; color: #7f8c8d;">
                    <strong>Mata Kuliah Terkait:</strong> {{relevantCoursework}}
                </div>
                {{/if}}
                {{#if honors}}
                <div style="margin-top: 2px; font-size: 9pt; color: #7f8c8d;">
                    <strong>Penghargaan:</strong> {{honors}}
                </div>
                {{/if}}
            </div>
            {{/each}}
        </div>
        {{/if}}

        {{#if skills}}
        <div class="section">
            <div class="section-title">Keahlian</div>
            <div class="skills-list">
                {{#each skills}}
                {{#if (hasContent skills)}}
                <div class="skill-category">
                    {{#if (hasContent category)}}<strong>{{category}}:</strong>{{/if}} {{skills}}
                </div>
                {{/if}}
                {{/each}}
            </div>
        </div>
        {{/if}}

        <!-- Projects Section -->
        {{#if projects}}
        <div class="section">
            <div class="section-title">Proyek Utama</div>
            {{#each projects}}
            <div class="project-item">
                <div class="project-title">{{title}}</div>
                {{#if link}}<div class="project-url">{{link}}</div>{{/if}}
                {{#if description}}<div class="project-description">{{description}}</div>{{/if}}
                {{#if technologies}}<div class="project-tech">Teknologi: {{technologies}}</div>{{/if}}
                {{#if achievements}}
                <ul style="margin-left: 18px; margin-top: 5px; font-size: 9pt; color: #34495e;">
                    {{#each achievements}}
                    {{#if (hasContent this)}}<li>{{this}}</li>{{/if}}
                    {{/each}}
                </ul>
                {{/if}}
            </div>
            {{/each}}
        </div>
        {{/if}}

        {{#if certifications}}
        <div class="section">
            <div class="section-title">Sertifikasi</div>
            <div class="description">
                {{#each certifications}}
                <div style="margin-bottom: 5px;">
                    {{name}} - {{issuer}} ({{date}})
                    {{#if credentialId}}<div style="font-size: 9pt; color: #95a5a6; margin-top: 1px;">ID: {{credentialId}}</div>{{/if}}
                </div>
                {{/each}}
            </div>
        </div>
        {{/if}}

        <!-- Languages Section -->
        {{#if languages}}
        <div class="section">
            <div class="section-title">Bahasa</div>
            <div class="description">
                {{#each languages}}
                {{#if language}}<div style="margin-bottom: 5px;">{{language}}{{#if proficiency}} - {{proficiency}}{{/if}}</div>{{/if}}
                {{/each}}
            </div>
        </div>
        {{/if}}

        <!-- Awards Section -->
        {{#if awards}}
        <div class="section">
            <div class="section-title">Penghargaan</div>
            <div class="description">
                {{#each awards}}
                <div style="margin-bottom: 5px;">
                    <div style="font-weight: bold; color: #2c3e50;">{{title}} - {{issuer}} ({{date}})</div>
                    {{#if description}}<div style="font-size: 9pt; color: #95a5a6; margin-top: 2px;">{{description}}</div>{{/if}}
                </div>
                {{/each}}
            </div>
        </div>
        {{/if}}
    </div>
</div>