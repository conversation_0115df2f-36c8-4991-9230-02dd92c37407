<style>
    .resume-container * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }

    .resume-container {
        font-family: 'Arial', sans-serif;
        line-height: 1.3;
        color: #333;
        padding: 10mm 15mm;
        font-size: 10pt;
        width: 210mm;
        height: 297mm;
        margin: 0 auto;
        background-color: white;
        page-break-inside: avoid;
        box-sizing: border-box;
        overflow: hidden;
    }

    /* Header Section */
    .resume-container .header {
        text-align: center;
        margin-bottom: 16px;
        border-bottom: 2px solid #333;
        padding-bottom: 10px;
    }

    .resume-container .name {
        font-size: 20pt;
        font-weight: bold;
        color: #2c3e50;
        margin-bottom: 4px;
    }

    .resume-container .title {
        font-size: 12pt;
        color: #7f8c8d;
        margin-bottom: 6px;
    }

    .resume-container .contact-info {
        display: flex;
        justify-content: center;
        gap: 8px;
        flex-wrap: wrap;
        font-size: 9pt;
    }

    .resume-container .contact-item {
        display: flex;
        align-items: center;
        gap: 4px;
    }

    /* Section Styling */
    .resume-container .section {
        margin-bottom: 12px;
    }

    .resume-container .section-title {
        font-size: 11pt;
        font-weight: bold;
        color: #2c3e50;
        text-transform: uppercase;
        margin-bottom: 6px;
        border-bottom: 1px solid #bdc3c7;
        padding-bottom: 2px;
    }

    /* Experience Section */
    .resume-container .experience-item {
        margin-bottom: 10px;
    }

    .resume-container .job-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 3px;
    }

    .resume-container .job-title {
        font-weight: bold;
        font-size: 10pt;
        color: #2c3e50;
    }

    .resume-container .company {
        font-weight: bold;
        color: #34495e;
        margin-bottom: 1px;
    }

    .resume-container .location-date {
        text-align: right;
        font-size: 9pt;
        color: #7f8c8d;
    }

    .resume-container .job-description {
        margin-top: 4px;
    }

    .resume-container .job-description ul {
        margin-left: 15px;
        margin-top: 2px;
    }

    .resume-container .job-description li {
        margin-bottom: 1px;
        text-align: justify;
    }

    /* Education Section */
    .resume-container .education-item {
        margin-bottom: 8px;
    }

    .resume-container .degree {
        font-weight: bold;
        color: #2c3e50;
    }

    .resume-container .institution {
        color: #34495e;
        margin-top: 1px;
    }

    .resume-container .education-details {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 1px;
    }

    .resume-container .gpa {
        font-size: 9pt;
        color: #7f8c8d;
    }

    /* Skills Section */
    .resume-container .skills-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 10px;
    }

    .resume-container .skill-category {
        margin-bottom: 6px;
    }

    .resume-container .skill-category-title {
        font-weight: bold;
        color: #2c3e50;
        margin-bottom: 3px;
    }

    .resume-container .skill-list {
        color: #34495e;
        line-height: 1.2;
    }

    /* Projects Section */
    .resume-container .project-item {
        margin-bottom: 8px;
    }

    .resume-container .project-title {
        font-weight: bold;
        color: #2c3e50;
        margin-bottom: 2px;
    }

    .resume-container .project-description {
        color: #34495e;
        margin-bottom: 2px;
    }

    .resume-container .project-tech {
        font-size: 9pt;
        color: #7f8c8d;
        font-style: italic;
    }

    /* Languages Section */
    .resume-container .languages-list {
        margin-left: 15px;
        margin-top: 4px;
    }

    .resume-container .languages-list li {
        margin-bottom: 2px;
        color: #34495e;
    }

    /* Print styles for A4 */
    @media print {
        .resume-container {
            width: 210mm !important;
            height: 297mm !important;
            margin: 0 !important;
            padding: 10mm 15mm !important;
            page-break-inside: avoid !important;
            box-sizing: border-box !important;
        }
    }

    /* PDF generation styles */
    @page {
        size: A4;
        margin: 0;
    }
</style>
<div class="resume-container">
    <!-- Header Section -->
    <div class="header">
        <div class="name">{{name}}</div>
        <div class="title">{{jobTitle}}</div>
        <div class="contact-info">
            {{#if email}}<div class="contact-item">📧 {{email}}</div>{{/if}}
            {{#if phone}}<div class="contact-item">📱 {{phone}}</div>{{/if}}
            {{#if linkedin}}<div class="contact-item">🌐 {{linkedin}}</div>{{/if}}
            {{#if website}}<div class="contact-item">🌍 {{website}}</div>{{/if}}
            {{#if github}}<div class="contact-item">💻 {{github}}</div>{{/if}}
            {{#if location}}<div class="contact-item">📍 {{location}}</div>{{/if}}
        </div>
    </div>

    <!-- Professional Summary -->
    {{#if summary}}
    <div class="section">
        <div class="section-title">Ringkasan Profesional</div>
        <p>{{summary}}</p>
    </div>
    {{/if}}

    <!-- Experience Section -->
    {{#if experiences}}
    <div class="section">
        <div class="section-title">Pengalaman Profesional</div>
        {{#each experiences}}
        <div class="experience-item">
            <div class="job-header">
                <div>
                    <div class="job-title">{{title}}</div>
                    <div class="company">{{company}}</div>
                </div>
                <div class="location-date">
                    {{#if location}}<div>{{location}}</div>{{/if}}
                    <div>{{startDate}} - {{endDate}}</div>
                </div>
            </div>
            {{#if responsibilities}}
            <div class="job-description">
                <ul>
                    {{#each responsibilities}}
                    {{#if (hasContent this)}}<li>{{this}}</li>{{/if}}
                    {{/each}}
                </ul>
            </div>
            {{/if}}
        </div>
        {{/each}}
    </div>
    {{/if}}

    <!-- Education Section -->
    {{#if education}}
    <div class="section">
        <div class="section-title">Pendidikan</div>
        {{#each education}}
        <div class="education-item">
            <div class="degree">{{degree}}</div>
            <div class="institution">{{institution}}</div>
            {{#if location}}<div class="institution">{{location}}</div>{{/if}}
            <div class="education-details">
                {{#if gpa}}<div class="gpa">IPK: {{gpa}}</div>{{/if}}
                <div class="location-date">{{graduationDate}}</div>
            </div>
            {{#if relevantCoursework}}
            <div style="margin-top: 3px; font-size: 9pt; color: #7f8c8d;">
                <strong>Mata Kuliah Terkait:</strong> {{relevantCoursework}}
            </div>
            {{/if}}
            {{#if honors}}
            <div style="margin-top: 2px; font-size: 9pt; color: #7f8c8d;">
                <strong>Penghargaan:</strong> {{honors}}
            </div>
            {{/if}}
        </div>
        {{/each}}
    </div>
    {{/if}}

    <!-- Skills Section -->
    {{#if skillCategories}}
    <div class="section">
        <div class="section-title">Keahlian</div>
        <div class="skills-grid">
            {{#each skillCategories}}
            {{#if (hasContent skills)}}
            <div class="skill-category">
                {{#if (hasContent category)}}<div class="skill-category-title">{{category}}</div>{{/if}}
                <div class="skill-list">{{skills}}</div>
            </div>
            {{/if}}
            {{/each}}
        </div>
    </div>
    {{/if}}

    <!-- Projects Section -->
    {{#if projects}}
    <div class="section">
        <div class="section-title">Proyek Utama</div>
        {{#each projects}}
        <div class="project-item">
            <div class="project-title">{{title}}</div>
            {{#if link}}<div style="font-size: 9pt; color: #3498db; margin-bottom: 2px;">{{link}}</div>{{/if}}
            {{#if description}}<div class="project-description">{{description}}</div>{{/if}}
            {{#if technologies}}<div class="project-tech">Teknologi: {{technologies}}</div>{{/if}}
            {{#if achievements}}
            <ul style="margin-left: 15px; margin-top: 3px; font-size: 9pt; color: #34495e;">
                {{#each achievements}}
                {{#if (hasContent this)}}<li>{{this}}</li>{{/if}}
                {{/each}}
            </ul>
            {{/if}}
        </div>
        {{/each}}
    </div>
    {{/if}}
    <!-- Certifications Section -->
    {{#if certifications}}
    <div class="section">
        <div class="section-title">Sertifikasi</div>
        {{#each certifications}}
        <div class="certification-item">
            {{name}} - {{issuer}} ({{date}})
            {{#if credentialId}}<div style="font-size: 9pt; color: #7f8c8d; margin-top: 1px;">ID: {{credentialId}}</div>{{/if}}
        </div>
        {{/each}}
    </div>
    {{/if}}

    <!-- Languages Section -->
    {{#if languages}}
    <div class="section">
        <div class="section-title">Bahasa</div>
        <ul class="languages-list">
            {{#each languages}}
            {{#if language}}<li>{{language}}{{#if proficiency}} - {{proficiency}}{{/if}}</li>{{/if}}
            {{/each}}
        </ul>
    </div>
    {{/if}}

    <!-- Awards Section -->
    {{#if awards}}
    <div class="section">
        <div class="section-title">Penghargaan</div>
        {{#each awards}}
        <div class="award-item">
            <div style="font-weight: bold; color: #2c3e50;">{{title}} - {{issuer}} ({{date}})</div>
            {{#if description}}<div style="font-size: 9pt; color: #7f8c8d; margin-top: 2px;">{{description}}</div>{{/if}}
        </div>
        {{/each}}
    </div>
    {{/if}}
</div>