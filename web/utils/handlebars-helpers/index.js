/**
 * Handlebars helpers for resume templates
 * These helpers will be automatically registered by handlebars-loader
 */

// Date formatting helper
function formatDate(dateString, format = 'MMMM YYYY') {
  if (!dateString) return '';
  
  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) {
      return dateString; // Return original if invalid date
    }
    
    // Simple date formatting
    const months = [
      'January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'
    ];
    
    const year = date.getFullYear();
    const month = months[date.getMonth()];
    
    if (format === 'MMMM YYYY') {
      return `${month} ${year}`;
    } else if (format === 'MM/YYYY') {
      return `${(date.getMonth() + 1).toString().padStart(2, '0')}/${year}`;
    } else if (format === 'YYYY') {
      return year.toString();
    }
    
    return `${month} ${year}`;
  } catch (error) {
    return dateString; // Return original on error
  }
}

// Date range helper
function dateRange(startDate, endDate) {
  if (!startDate && !endDate) return '';
  
  const formatDate = (date) => {
    if (!date) return '';
    if (date.toLowerCase() === 'present') return 'Present';
    
    try {
      const dateObj = new Date(date);
      if (isNaN(dateObj.getTime())) {
        return date; // Return original if invalid date
      }
      
      const months = [
        'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
        'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
      ];
      
      return `${months[dateObj.getMonth()]} ${dateObj.getFullYear()}`;
    } catch (error) {
      return date;
    }
  };
  
  const formattedStart = formatDate(startDate);
  const formattedEnd = formatDate(endDate);
  
  if (formattedStart && formattedEnd) {
    return `${formattedStart} - ${formattedEnd}`;
  } else if (formattedStart) {
    return formattedStart;
  } else if (formattedEnd) {
    return formattedEnd;
  }
  
  return '';
}

// String join helper
function join(array, separator = ', ') {
  if (!array || !Array.isArray(array)) return '';
  return array.filter(item => item != null && item !== '').join(separator);
}

// Conditional helper for checking array length
function hasItems(array) {
  return Array.isArray(array) && array.length > 0;
}

// Capitalize helper
function capitalize(str) {
  if (!str) return '';
  return str.charAt(0).toUpperCase() + str.slice(1);
}

// Truncate helper
function truncate(str, length = 100) {
  if (!str) return '';
  if (str.length <= length) return str;
  return str.substring(0, length) + '...';
}

// URL helper to ensure proper URL formatting
function formatUrl(url) {
  if (!url) return '';
  if (url.startsWith('http://') || url.startsWith('https://')) {
    return url;
  }
  return `https://${url}`;
}

// Clean LinkedIn URL helper
function cleanLinkedIn(linkedinUrl) {
  if (!linkedinUrl) return '';
  
  // Extract just the username/profile part
  const match = linkedinUrl.match(/linkedin\.com\/in\/([^\/]+)/);
  if (match) {
    return `linkedin.com/in/${match[1]}`;
  }
  
  return linkedinUrl;
}

// Filter empty/whitespace strings from arrays
function filterEmpty(array) {
  if (!array || !Array.isArray(array)) return [];
  return array.filter(item => item && typeof item === 'string' && item.trim().length > 0);
}

// Join arrays with proper separator handling, filtering empty strings
function joinFiltered(array, separator = ', ') {
  if (!array || !Array.isArray(array)) return '';
  const filtered = array.filter(item => item && typeof item === 'string' && item.trim().length > 0);
  return filtered.join(separator);
}

// Check if string has meaningful content
function hasContent(str) {
  return str && typeof str === 'string' && str.trim().length > 0;
}

// Create array from contact fields, filtering out empty ones
function contactArray(email, phone, linkedin, website, github, location) {
  const fields = [];
  if (hasContent(email)) fields.push(email);
  if (hasContent(phone)) fields.push(phone);
  if (hasContent(linkedin)) fields.push(`LinkedIn: ${linkedin}`);
  if (hasContent(website)) fields.push(website);
  if (hasContent(github)) fields.push(`GitHub: ${github}`);
  if (hasContent(location)) fields.push(location);
  return fields;
}

// Join contact fields with separator - handles Handlebars options object
function joinContact(email, phone, linkedin, website, github, location, options) {
  // When called from Handlebars, the last parameter is an options object
  // When called directly, all parameters are the actual values
  let separator = ' • ';
  
  // Check if this is a Handlebars call (options object as last parameter)
  if (typeof options === 'object' && options && options.hash !== undefined) {
    // This is a Handlebars call - use the separator from hash if provided
    separator = options.hash.separator || ' • ';
  } else if (typeof options === 'string') {
    // This is a direct call with separator as 7th parameter
    separator = options;
  }
  
  const fields = contactArray(email, phone, linkedin, website, github, location);
  return fields.join(separator);
}

module.exports = {
  formatDate,
  dateRange,
  join,
  hasItems,
  capitalize,
  truncate,
  formatUrl,
  cleanLinkedIn,
  filterEmpty,
  joinFiltered,
  hasContent,
  contactArray,
  joinContact
};