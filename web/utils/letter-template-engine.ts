import { LetterTemplate } from './letter-templates/applicationLetterTemplates';
import {
  StructuredLetterData,
  LetterTemplateData,
  convertToLetterTemplateData,
  validateLetterTemplateData
} from '../types/letter-structured';

// Import precompiled templates (generated by webpack's Handlebars loader)
import plainTextTemplate from './handlebars-templates/letters/plain-text.hbs';
import classicBlueTemplate from './handlebars-templates/letters/classic-blue.hbs';
import professionalClassicTemplate from './handlebars-templates/letters/professional-classic.hbs';
import minimalistSidebarTemplate from './handlebars-templates/letters/minimalist-sidebar.hbs';
import minimalistBorderFrameTemplate from './handlebars-templates/letters/minimalist-border-frame.hbs';
import minimalistAccentTemplate from './handlebars-templates/letters/minimalist-accent.hbs';
import minimalistCircularAccentsTemplate from './handlebars-templates/letters/minimalist-circular-accents.hbs';

/**
 * Template compilation cache for performance
 * Key: template ID, Value: precompiled Handlebars template
 */
const precompiledTemplates = new Map<string, any>();

// Initialize precompiled templates when available
if (plainTextTemplate) precompiledTemplates.set('plain-text', plainTextTemplate);
if (classicBlueTemplate) precompiledTemplates.set('classic-blue', classicBlueTemplate);
if (professionalClassicTemplate) precompiledTemplates.set('professional-classic', professionalClassicTemplate);
if (minimalistSidebarTemplate) precompiledTemplates.set('minimalist-sidebar', minimalistSidebarTemplate);
if (minimalistBorderFrameTemplate) precompiledTemplates.set('minimalist-border-frame', minimalistBorderFrameTemplate);
if (minimalistAccentTemplate) precompiledTemplates.set('minimalist-accent', minimalistAccentTemplate);
if (minimalistCircularAccentsTemplate) precompiledTemplates.set('minimalist-circular-accents', minimalistCircularAccentsTemplate);

/**
 * Get precompiled template by ID
 * @param templateId - Template ID
 * @returns Precompiled Handlebars template or null if not found
 */
function getPrecompiledTemplate(templateId: string): any {
  const template = precompiledTemplates.get(templateId);
  if (!template) {
    throw new Error(`Precompiled template not found for ID: ${templateId}`);
  }
  return template;
}

/**
 * Validate template data and provide detailed error information
 * @param templateData - Template data to validate
 * @returns Validation result with details
 */
function validateTemplateDataWithDetails(templateData: LetterTemplateData): {
  isValid: boolean;
  errors: string[];
  warnings: string[];
} {
  const errors: string[] = [];
  const warnings: string[] = [];
  
  // Validate required fields
  const validation = validateLetterTemplateData(templateData);
  if (!validation.isValid) {
    errors.push(...validation.missingFields.map(field => `Missing required field: ${field}`));
  }
  
  // Add warnings if available
  if (validation.warnings) {
    warnings.push(...validation.warnings);
  }
  
  // Additional validation for data quality
  if (templateData.paragraphs && templateData.paragraphs.length === 0) {
    errors.push('Letter must have at least one paragraph');
  }
  
  if (templateData.recipientLines && templateData.recipientLines.length === 0) {
    errors.push('Recipient information is required');
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
}

/**
 * Main function to fill letter template with structured data
 * @param template - Letter template object
 * @param data - Structured letter data
 * @returns Filled HTML string
 * @throws Error if template compilation fails or data validation fails
 */
export function fillLetterTemplate(template: LetterTemplate, data: StructuredLetterData): string {
  try {
    // Convert structured data to template format
    const templateData = convertToLetterTemplateData(data);
    
    // Validate template data
    const validation = validateTemplateDataWithDetails(templateData);
    
    if (!validation.isValid) {
      throw new Error(`Template data validation failed: ${validation.errors.join(', ')}`);
    }
    
    // Log warnings if any
    if (validation.warnings.length > 0) {
      console.warn('Template data warnings:', validation.warnings);
    }
    
    // Get precompiled template
    const compiledTemplate = getPrecompiledTemplate(template.id);
    
    // Generate HTML with additional error context
    let html: string;
    try {
      html = compiledTemplate(templateData);
    } catch (renderError) {
      const renderErrorMessage = renderError instanceof Error ? renderError.message : 'Unknown render error';
      throw new Error(`Template rendering failed: ${renderErrorMessage}. This might be due to missing or malformed data in the template.`);
    }
    
    // Basic validation of generated HTML
    if (!html || html.trim().length === 0) {
      throw new Error('Generated HTML is empty');
    }
    
    if (!html.includes(templateData.signatureName)) {
      throw new Error('Generated HTML does not contain the signature name');
    }
    
    return html;
    
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    
    // Log error for debugging with more context
    console.error('Letter template fill error:', {
      templateId: template.id,
      templateName: template.name,
      error: errorMessage,
      signatureName: data?.signature?.name || 'Unknown',
      dataStructure: {
        hasMetadata: !!data?.metadata,
        hasHeader: !!data?.header,
        hasSubject: !!data?.subject,
        hasRecipient: !!data?.recipient,
        hasBody: !!data?.body,
        paragraphsCount: data?.body?.paragraphs?.length || 0,
        hasSignature: !!data?.signature,
      }
    });
    
    throw new Error(`Failed to fill letter template "${template.name}": ${errorMessage}`);
  }
}

/**
 * Get available precompiled letter template IDs
 */
export function getAvailableLetterTemplateIds(): string[] {
  return Array.from(precompiledTemplates.keys());
}

/**
 * Check if a letter template is available
 */
export function isLetterTemplateAvailable(templateId: string): boolean {
  return precompiledTemplates.has(templateId);
}

/**
 * Test letter template compilation with sample data
 * @param template - Template to test
 * @param sampleData - Sample structured letter data
 * @returns Test result
 */
export function testLetterTemplate(template: LetterTemplate, sampleData: StructuredLetterData): {
  success: boolean;
  error?: string;
  warnings?: string[];
  htmlLength?: number;
} {
  try {
    const html = fillLetterTemplate(template, sampleData);
    const templateData = convertToLetterTemplateData(sampleData);
    const validation = validateTemplateDataWithDetails(templateData);
    
    return {
      success: true,
      warnings: validation.warnings,
      htmlLength: html.length
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}