import { createClient } from '@/lib/supabase-server';

/**
 * Utility functions for cleaning up duplicate manual resumes
 */

interface ResumeRecord {
  id: string;
  user_id: string;
  structured_data: any;
  created_at: string;
  status: string;
}

/**
 * Checks if a resume is essentially empty (no meaningful content)
 */
export function isEmptyManualResume(resumeData: any): boolean {
  if (!resumeData || !resumeData.structured_data) return true;
  
  const data = resumeData.structured_data;
  
  // Consider a resume empty if it has no meaningful content
  const hasPersonalInfo = data.personalInfo?.fullName?.trim() || 
                         data.personalInfo?.email?.trim() || 
                         data.personalInfo?.phone?.trim();
  
  const hasExperiences = data.experiences?.length > 0 && 
                        data.experiences.some((exp: any) => 
                          exp.jobTitle?.trim() || exp.company?.trim());
  
  const hasEducation = data.education?.length > 0 && 
                      data.education.some((edu: any) => 
                        edu.degree?.trim() || edu.institution?.trim());
  
  const hasSkills = data.skills?.categories?.length > 0 && 
                   data.skills.categories.some((cat: any) => 
                     cat.skills?.length > 0 && 
                     cat.skills.some((skill: string) => skill?.trim()));

  return !hasPersonalInfo && !hasExperiences && !hasEducation && !hasSkills;
}

/**
 * Finds duplicate empty manual resumes for a specific user
 */
export async function findDuplicateEmptyResumes(userId: string): Promise<ResumeRecord[]> {
  const supabase = await createClient();
  
  const { data: resumes, error } = await supabase
    .from('resumes')
    .select('id, user_id, structured_data, created_at, status')
    .eq('user_id', userId)
    .eq('status', 'done')
    .order('created_at', { ascending: false });

  if (error) {
    console.error('Error fetching resumes:', error);
    return [];
  }

  if (!resumes || resumes.length <= 1) {
    return []; // No duplicates possible
  }

  // Find all empty resumes
  const emptyResumes = resumes.filter(resume => isEmptyManualResume(resume));
  
  // Return all but the most recent empty resume (keep the newest one)
  return emptyResumes.slice(1);
}

/**
 * Removes duplicate empty manual resumes for a specific user
 * Keeps the most recent empty resume and removes older duplicates
 */
export async function cleanupDuplicateEmptyResumes(userId: string): Promise<{
  success: boolean;
  removedCount: number;
  error?: string;
}> {
  try {
    const duplicates = await findDuplicateEmptyResumes(userId);
    
    if (duplicates.length === 0) {
      return { success: true, removedCount: 0 };
    }

    const supabase = await createClient();
    const idsToRemove = duplicates.map(resume => resume.id);
    
    const { error } = await supabase
      .from('resumes')
      .delete()
      .in('id', idsToRemove)
      .eq('user_id', userId); // Extra safety check

    if (error) {
      console.error('Error removing duplicate resumes:', error);
      return { success: false, removedCount: 0, error: error.message };
    }

    console.log(`Removed ${duplicates.length} duplicate empty resumes for user ${userId}`);
    return { success: true, removedCount: duplicates.length };
    
  } catch (error) {
    console.error('Error in cleanupDuplicateEmptyResumes:', error);
    return { 
      success: false, 
      removedCount: 0, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}

/**
 * Finds all users with duplicate empty manual resumes
 * This is useful for bulk cleanup operations
 */
export async function findUsersWithDuplicateEmptyResumes(): Promise<string[]> {
  const supabase = await createClient();
  
  const { data: resumes, error } = await supabase
    .from('resumes')
    .select('user_id, structured_data, created_at')
    .eq('status', 'done')
    .order('user_id', { ascending: true })
    .order('created_at', { ascending: false });

  if (error) {
    console.error('Error fetching all resumes:', error);
    return [];
  }

  if (!resumes) return [];

  // Group by user_id
  const userResumes = new Map<string, any[]>();
  resumes.forEach(resume => {
    if (!userResumes.has(resume.user_id)) {
      userResumes.set(resume.user_id, []);
    }
    userResumes.get(resume.user_id)!.push(resume);
  });

  // Find users with multiple empty resumes
  const usersWithDuplicates: string[] = [];
  
  userResumes.forEach((userResumeList, userId) => {
    const emptyResumes = userResumeList.filter(resume => isEmptyManualResume(resume));
    if (emptyResumes.length > 1) {
      usersWithDuplicates.push(userId);
    }
  });

  return usersWithDuplicates;
}
