import { GoogleGenAI, Part, Type } from '@google/genai';
import mammoth from 'mammoth';
import { captureApiError } from '@/utils/errorMonitoring';

// Initialize the Google Generative AI client
const googleAI = new GoogleGenAI({apiKey: process.env.NEXT_PUBLIC_GOOGLE_AI_API_KEY || ''});

// Define the job matching result interface
export interface JobMatchingResult {
  overallMatch: number;            // Overall match percentage (0-100)
  skillsMatch: number;             // Skills match percentage (0-100)
  experienceMatch: number;         // Experience match percentage (0-100)
  educationMatch: number;          // Education match percentage (0-100)
  analysis: string[];              // Key analysis points
  missingSkills: string[];         // Skills that need development
  strengths: string[];             // Candidate's strengths
  tips: string[];                  // Tips to improve match percentage
  skillsAnalysis: string[];        // Detailed skills analysis
  experienceAnalysis: string[];    // Detailed experience analysis
  educationAnalysis: string[];     // Detailed education analysis
}

/**
 * Converts a file to a generative part for the AI model
 * @param file - The file buffer to convert
 * @param mimeType - The MIME type of the file
 * @returns Promise with the generative part
 */
async function fileToGenerativePart(file: ArrayBuffer, mimeType: string): Promise<Part> {
  const uint8Array = new Uint8Array(file);
  return {
    inlineData: {
      data: Buffer.from(uint8Array).toString('base64'),
      mimeType
    }
  };
}

/**
 * Extracts text from a DOCX file buffer
 * @param buffer - The file buffer containing DOCX data
 * @returns Promise with the extracted text
 */
async function extractTextFromDocx(buffer: ArrayBuffer): Promise<string> {
  try {
    // Convert ArrayBuffer to Buffer for mammoth
    const nodeBuffer = Buffer.from(buffer);
    
    // Use the correct API call with arrayBuffer
    const result = await mammoth.extractRawText({
      buffer: nodeBuffer
    });
    
    return result.value || '';
  } catch (error) {
    console.error('Error extracting text from DOCX:', error);
    throw new Error('Failed to extract text from DOCX file');
  }
}

/**
 * Generates a job match analysis using the gemini-2.0-flash model
 * @param resumeFile - The resume file buffer and mime type
 * @param jobDescription - The job description (optional if jobImage is provided)
 * @param jobImage - The job posting image file (optional if jobDescription is provided)
 * @returns The generated job match analysis as a structured object
 */
export async function generateAIJobMatching(
  resumeFile: { buffer: ArrayBuffer, mimeType: string },
  jobDescription?: string,
  jobImage?: { buffer: ArrayBuffer, mimeType: string },
): Promise<JobMatchingResult> {
  try {
    // Make sure the mime type is supported (PDF, DOCX, TXT, PNG, JPG, JPEG)
    if (!['application/pdf', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'text/plain', 'image/png', 'image/jpeg', 'image/jpg'].includes(resumeFile.mimeType)) {
      throw new Error('Unsupported resume file format. Please use PDF, DOCX, PNG, JPG, or JPEG files.');
    }

    // Validate that at least one of jobDescription or jobImage is provided
    if (!jobDescription && !jobImage) {
      throw new Error('Either job description text or job posting image must be provided');
    }

    let resumePart: Part;
    let jobImagePart: Part | undefined;
    
    // For DOCX files, extract text first and then process
    if (resumeFile.mimeType === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') {
      const extractedText = await extractTextFromDocx(resumeFile.buffer);
      resumePart = { text: extractedText };
    } else if (['image/png', 'image/jpeg', 'image/jpg'].includes(resumeFile.mimeType)) {
      // For image formats (PNG, JPG, JPEG), use the file as is
      resumePart = await fileToGenerativePart(resumeFile.buffer, resumeFile.mimeType);
    } else {
      // For other formats (PDF, TXT), use the original approach
      resumePart = await fileToGenerativePart(resumeFile.buffer, resumeFile.mimeType);
    }
    
    // Process job image if provided
    if (jobImage) {
      // Validate image type (only accept image formats)
      if (!jobImage.mimeType.startsWith('image/')) {
        throw new Error('Unsupported job image format. Only image formats are accepted.');
      }
      
      // Convert job image to generative part
      jobImagePart = await fileToGenerativePart(jobImage.buffer, jobImage.mimeType);
    }

    // Prompt that generates structured JSON data for job matching analysis
    const prompt = `
You are a professional job match analyzer. Analyze the resume against the job description and output a JSON object with comprehensive match analysis.

First, carefully identify the key requirements, skills, and qualifications mentioned in the job ${jobDescription ? 'description' : 'posting image'} provided:
${jobDescription || '[Job image provided]'}

Then thoroughly analyze how well the candidate's resume matches these specific requirements. Be objective and specific.

Output ONLY a valid JSON object with the following structure (no other text or explanation):

{
  "overallMatch": 75,  // Example: The overall match percentage (1-100)
  "skillsMatch": 80,  // Example: Skills match percentage (1-100)
  "experienceMatch": 70,  // Example: Experience match percentage (1-100)
  "educationMatch": 85,  // Example: Education match percentage (1-100),
  "analysis": ["Analysis point 1", "Analysis point 2", "Analysis point 3", ...],  // 3-5 key overall analysis points
  "strengths": ["Strength 1", "Strength 2", "Strength 3", "Strength 4", "Strength 5", ...],  // List all specific strengths from resume that directly match job requirements
  "missingSkills": ["Skill 1", "Skill 2", "Skill 3", "Skill 4", ...],  // List all important missing skills from job description
  "tips": ["Tip 1", "Tip 2", "Tip 3"],  // 3 actionable tips to improve match
  "skillsAnalysis": ["Skills analysis 1", "Skills analysis 2", "Skills analysis 3", ...],  // List all specific points about skills match, and ensure these points are consistent with the skillsMatch percentage
  "experienceAnalysis": ["Experience analysis 1", "Experience analysis 2", "Experience analysis 3", ...],  // List all specific points about experience match, and ensure these points are consistent with the experienceMatch percentage
  "educationAnalysis": ["Education analysis 1", "Education analysis 2", "Education analysis 3", ...]  // List all specific points about education match, and ensure these points are consistent with the educationMatch percentage
}

Guidelines:
1. Be honest but positive in assessment
2. Focus only on what's actually in the resume, but be aware of skill hierarchies (e.g., "Android Development" likely includes knowledge of Kotlin, Android SDK, etc.)
3. For the "strengths" field, include skills and experiences from the resume that match key requirements in the job description, recognizing that general skills often encompass more specific ones
4. For "missingSkills", ONLY include skills that are genuinely absent from the resume - do not list specific technologies if the resume shows expertise in the parent technology (e.g., don't list "Kotlin" as missing if "Android Development" is present)
5. The analysis in skillsAnalysis, experienceAnalysis, and educationAnalysis MUST be consistent with the respective percentage values (skillsMatch, experienceMatch, educationMatch). For example, if the percentage is low (education under 100, besides under 70), the analysis should mention more gaps or weaknesses; if high (above 70), the analysis should highlight more strengths and fewer gaps.
6. All text must be in Bahasa Indonesia
7. Make sure the JSON is perfectly valid with no extra text
8. Use complete sentences for analysis points
`;

    // Define the response schema for strict JSON output
    const schema = {
      type: Type.OBJECT,
      properties: {
        overallMatch: { type: Type.NUMBER, description: 'Overall match percentage (0-100)' },
        skillsMatch: { type: Type.NUMBER, description: 'Skills match percentage (0-100)' },
        experienceMatch: { type: Type.NUMBER, description: 'Experience match percentage (0-100)' },
        educationMatch: { type: Type.NUMBER, description: 'Education match percentage (0-100)' },
        analysis: { type: Type.ARRAY, items: { type: Type.STRING }, description: 'Key analysis points' },
        missingSkills: { type: Type.ARRAY, items: { type: Type.STRING }, description: 'Skills that need development' },
        strengths: { type: Type.ARRAY, items: { type: Type.STRING }, description: 'Candidate\'s strengths' },
        tips: { type: Type.ARRAY, items: { type: Type.STRING }, description: 'Tips to improve match percentage' },
        skillsAnalysis: { type: Type.ARRAY, items: { type: Type.STRING }, description: 'Detailed skills analysis' },
        experienceAnalysis: { type: Type.ARRAY, items: { type: Type.STRING }, description: 'Detailed experience analysis' },
        educationAnalysis: { type: Type.ARRAY, items: { type: Type.STRING }, description: 'Detailed education analysis' },
      },
      required: [
        'overallMatch',
        'skillsMatch',
        'experienceMatch',
        'educationMatch',
        'analysis',
        'missingSkills',
        'strengths',
        'tips',
        'skillsAnalysis',
        'experienceAnalysis',
        'educationAnalysis',
      ],
    };

    // Generate content just once
    const result = await googleAI.models.generateContent({
      model: "gemini-2.0-flash",
      contents: [
        {
          role: "user",
          parts: [
            { text: prompt },
            resumePart,
            ...(jobImagePart ? [jobImagePart] : [])
          ]
        }
      ],
      config: {
        temperature: 0.2,
        topK: 40,
        topP: 0.95,
        maxOutputTokens: 2048,
        responseMimeType: "application/json",
        responseSchema: schema,
      },
    });

    const response = result.text || '';
    let structuredData: JobMatchingResult;
    try {
      structuredData = JSON.parse(response.trim());
    } catch (e) {
      throw new Error('Failed to parse AI response as JSON');
    }
    
    // If we still couldn't get valid JSON, set default values
    if (!structuredData) {
      // Set default fallback values
      const defaultMatch = 70;
      structuredData = {
        overallMatch: defaultMatch,
        skillsMatch: Math.min(100, defaultMatch + (Math.random() < 0.5 ? -5 : 5)),
        experienceMatch: Math.min(100, defaultMatch + (Math.random() < 0.5 ? -8 : 8)),
        educationMatch: Math.min(100, defaultMatch + (Math.random() < 0.5 ? -10 : 10)),
        analysis: ['Resume menunjukkan kecocokan dengan posisi ini.'],
        missingSkills: ['Tidak ada data'],
        strengths: ['Tidak ada data'],
        tips: ['Tambahkan keterampilan yang relevan dengan posisi ini.'],
        skillsAnalysis: ['Keterampilan yang dimiliki cukup sesuai dengan yang dibutuhkan.'],
        experienceAnalysis: ['Pengalaman yang dimiliki cukup relevan.'],
        educationAnalysis: ['Latar belakang pendidikan sesuai dengan posisi ini.']
      };
    }

    // Ensure all fields exist in the response
    if (!structuredData.tips) structuredData.tips = ['Tambahkan keterampilan yang relevan dengan posisi ini.'];
    if (!structuredData.skillsAnalysis) structuredData.skillsAnalysis = ['Keterampilan yang dimiliki cukup sesuai.'];
    if (!structuredData.experienceAnalysis) structuredData.experienceAnalysis = ['Pengalaman yang dimiliki cukup relevan.'];
    if (!structuredData.educationAnalysis) structuredData.educationAnalysis = ['Latar belakang pendidikan sesuai.'];
    
    // Return structured result
    return {
      overallMatch: structuredData.overallMatch,
      skillsMatch: structuredData.skillsMatch,
      experienceMatch: structuredData.experienceMatch,
      educationMatch: structuredData.educationMatch,
      analysis: structuredData.analysis || ['Resume menunjukkan kecocokan dengan posisi ini.'],
      missingSkills: structuredData.missingSkills || ['Tidak ada data'],
      strengths: structuredData.strengths || ['Tidak ada data'],
      tips: structuredData.tips,
      skillsAnalysis: structuredData.skillsAnalysis,
      experienceAnalysis: structuredData.experienceAnalysis,
      educationAnalysis: structuredData.educationAnalysis
    };
  } catch (error) {
    console.error('Error generating job matching with AI:', error);
    
    // Log error to Rollbar
    captureApiError('generate-job-matching', error, {
      resumeType: resumeFile.mimeType,
      hasJobDescription: !!jobDescription,
      hasJobImage: !!jobImage
    });
    
    throw new Error('Failed to generate job matching analysis using AI');
  }
}
