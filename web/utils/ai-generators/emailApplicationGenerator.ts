import { GoogleGenAI, Type, Part } from '@google/genai';
import mammoth from 'mammoth';
import { captureApiError } from '@/utils/errorMonitoring';

// Initialize the Google Generative AI client
const googleAI = new GoogleGenAI({apiKey: process.env.NEXT_PUBLIC_GOOGLE_AI_API_KEY || ''});

// Define interface for email application response
export interface EmailApplicationResult {
  subject: string;
  body: string;
}

/**
 * Converts a file to a generative part for the AI model
 * @param file - The file buffer to convert
 * @param mimeType - The MIME type of the file
 * @returns Promise with the generative part
 */
async function fileToGenerativePart(file: ArrayBuffer, mimeType: string): Promise<Part> {
  const uint8Array = new Uint8Array(file);
  return {
    inlineData: {
      data: Buffer.from(uint8Array).toString('base64'),
      mimeType
    }
  };
}

/**
 * Extracts text from a DOCX file buffer
 * @param buffer - The file buffer containing DOCX data
 * @returns Promise with the extracted text
 */
async function extractTextFromDocx(buffer: ArrayBuffer): Promise<string> {
  try {
    // Convert ArrayBuffer to Buffer for mammoth
    const nodeBuffer = Buffer.from(buffer);
    
    // Use the correct API call with arrayBuffer
    const result = await mammoth.extractRawText({
      buffer: nodeBuffer
    });
    
    return result.value || '';
  } catch (error) {
    console.error('Error extracting text from DOCX:', error);
    throw new Error('Failed to extract text from DOCX file');
  }
}

/**
 * Generates an email application using the gemini-2.0-flash model
 * @param resumeFile - The resume file buffer and mime type
 * @param jobDescription - The job description (optional if jobImage is provided)
 * @param jobImage - The job posting image file (optional if jobDescription is provided)
 * @returns An object containing the email subject and body
 */
export async function generateAIEmailApplication(
  resumeFile: { buffer: ArrayBuffer, mimeType: string },
  jobDescription?: string,
  jobImage?: { buffer: ArrayBuffer, mimeType: string },
): Promise<EmailApplicationResult> {
  try {
    // Make sure the mime type is supported (PDF, DOCX, TXT, PNG, JPG, JPEG)
    if (!['application/pdf', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'text/plain', 'image/png', 'image/jpeg', 'image/jpg'].includes(resumeFile.mimeType)) {
      throw new Error('Unsupported resume file format. Please use PDF, DOCX, PNG, JPG, or JPEG files.');
    }

    // Validate that at least one of jobDescription or jobImage is provided
    if (!jobDescription && !jobImage) {
      throw new Error('Either job description text or job posting image must be provided');
    }

    let resumePart: Part;
    let jobImagePart: Part | undefined;
    
    // For DOCX files, extract text first and then process
    if (resumeFile.mimeType === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') {
      const extractedText = await extractTextFromDocx(resumeFile.buffer);
      resumePart = { text: extractedText };
    } else if (['image/png', 'image/jpeg', 'image/jpg'].includes(resumeFile.mimeType)) {
      // For image formats (PNG, JPG, JPEG), use the file as is
      resumePart = await fileToGenerativePart(resumeFile.buffer, resumeFile.mimeType);
    } else {
      // For other formats (PDF, TXT), use the original approach
      resumePart = await fileToGenerativePart(resumeFile.buffer, resumeFile.mimeType);
    }
    
    // Process job image if provided
    if (jobImage) {
      // Validate image type (only accept image formats)
      if (!jobImage.mimeType.startsWith('image/')) {
        throw new Error('Unsupported job image format. Only image formats are accepted.');
      }
      
      // Convert job image to generative part
      jobImagePart = await fileToGenerativePart(jobImage.buffer, jobImage.mimeType);
    }

    // Prepare the prompt
    const prompt = `
You are a professional job application email writer. You will receive a resume and a job description with a specific job position title. Craft a professional email application in Bahasa Indonesia that is formal and concise to be sent to a potential employer. Do not include any markdown or formatting; write only the email text ready to copy-paste.

First, analyze the resume and job description thoroughly to identify:
1. Skills EXPLICITLY mentioned in the resume that match the job requirements
2. Skills IMPLICITLY suggested by the resume
3. Related skills in the resume that could TRANSFER to the job requirements
4. Education, certifications, and relevant experience from the resume

Then, write a formal email application that:
- Includes a clear, professional subject line
- Has a proper greeting addressed to the hiring team 
- Introduces the applicant briefly
- States the purpose of the email (applying for the specific position)
- Includes 2-3 brief sentences highlighting the skills that PRECISELY match the key technical requirements in the job description
- Mentions that the CV/resume is attached
- Expresses interest in an interview opportunity
- Has a professional closing

Job Information: ${jobDescription ? jobDescription : '[A job posting image is provided. Please analyze it to identify the job requirements, responsibilities, and qualifications]'}

Guidelines:
1. The email should be written in a formal and professional tone suitable for business correspondence in Indonesia
2. Make it concise (around 200-250 words) - employers prefer brief, to-the-point emails
3. Focus ONLY on the candidate's strengths and relevant experience, with special emphasis on skills that match the job description
4. PRIORITIZE mentioning the EXACT technical skills listed in the job requirements (e.g., if Laravel, JavaScript, Node.js are mentioned in the job description, be sure to highlight these even if they appear only briefly in the resume)
5. EXTREMELY IMPORTANT: NEVER use phrases like "belum memiliki pengalaman" or mention any lack of experience
6. IMPORTANT: DO NOT mention how the applicant found the job posting (e.g., "seperti yang saya lihat di website perusahaan") - make the application generic without requiring manual editing
7. Avoid lengthy paragraphs - use short, clear paragraphs for ease of reading
8. The email should complement the resume, not repeat it entirely

RESPOND ONLY WITH A VALID JSON OBJECT IN THIS EXACT FORMAT:

{"subject":"subject line here","body":"complete email body here"}

IMPORTANT: Your entire response must be ONLY this JSON object. Do not include any other text, markdown formatting, code blocks, explanation, or commentary before or after the JSON.
    `;

    const schema = {
      type: Type.OBJECT,
      properties: {
        subject: {
          type: Type.STRING,
          description: "The email subject line",
        },
        body: {
          type: Type.STRING,
          description: "The full email body text starting with greeting and ending with signature",
        }
      },
      required: ["subject", "body"]
    };

    // Generate the content
    const response = await googleAI.models.generateContent({
      model: "gemini-2.0-flash",
      contents: [
        {
          role: "user",
          parts: [
            { text: prompt },
            resumePart,
            ...(jobImagePart ? [jobImagePart] : [])
          ]
        }
      ],
      config: {
        temperature: 0.2,
        topK: 40,
        topP: 0.95,
        maxOutputTokens: 1024,
        responseMimeType: "application/json",
        responseSchema: schema,
      },
    });

    const emailContent = response.text || '';
    
    try {
      // Parse the JSON response directly
      const parsedResponse: EmailApplicationResult = JSON.parse(emailContent);
      
      // Validate the structure
      if (!parsedResponse.subject || !parsedResponse.body) {
        throw new Error('AI response missing required fields');
      }
      
      return {
        subject: parsedResponse.subject,
        body: parsedResponse.body
      };
    } catch (parseError) {
      console.error('Error parsing AI response as JSON:', parseError);
      
      // Fallback to regex extraction if JSON parsing fails
      const subjectMatch = emailContent.match(/^Subject: (.+?)(?=\n)/i);
      const subject = subjectMatch ? subjectMatch[1].trim() : 'Lamaran Kerja';
      
      // The body is everything after the subject line or the whole content if no subject found
      let body = emailContent;
      if (subjectMatch) {
        body = emailContent.substring(subjectMatch[0].length).trim();
      }
      
      return {
        subject,
        body
      };
    }
  } catch (error) {
    console.error('Error generating email application with AI:', error);
    
    // Log error to Rollbar
    captureApiError('generate-email-application', error, {
      resumeType: resumeFile.mimeType,
      hasJobDescription: !!jobDescription,
      hasJobImage: !!jobImage
    });
    
    throw new Error('Failed to generate email application using AI');
  }
}
