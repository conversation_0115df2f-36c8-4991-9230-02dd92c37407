import rollbar from '@/lib/rollbar';

/**
 * Utility untuk menangkap dan merekam error di Rollbar dengan konteks tambahan
 */
export function captureError(
  error: Error | string,
  context: Record<string, any> = {},
  severity: 'critical' | 'error' | 'warning' | 'info' = 'error'
) {
  // Pastikan konteks selalu berisi data yang berguna
  const enrichedContext = {
    timestamp: new Date().toISOString(),
    url: typeof window !== 'undefined' ? window.location.href : '',
    userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : '',
    ...context
  };

  // Log berdasarkan severity
  if (severity === 'critical') {
    rollbar.logError(error, { ...enrichedContext, severity: 'critical' });
  } else if (severity === 'warning') {
    rollbar.logWarning(typeof error === 'string' ? error : error.message, enrichedContext);
  } else if (severity === 'info') {
    rollbar.logInfo(typeof error === 'string' ? error : error.message, enrichedContext);
  } else {
    rollbar.logError(error, enrichedContext);
  }
}

/**
 * Menangkap error di API endpoints
 */
export function captureApiError(
  endpoint: string,
  error: Error | unknown,
  request?: Record<string, any>
) {
  const errorMessage = error instanceof Error ? error.message : 'Unknown API error';
  
  captureError(
    error instanceof Error ? error : new Error(errorMessage),
    {
      endpoint,
      request: JSON.stringify(request || {}),
      type: 'api_error'
    },
    'critical'
  );
}

/**
 * Setup global error handlers di luar React components
 */
export function setupGlobalErrorHandlers() {
  if (typeof window !== 'undefined') {
    // Tangkap promise rejections
    window.addEventListener('unhandledrejection', (event) => {
      captureError(
        event.reason instanceof Error ? event.reason : new Error(String(event.reason)),
        { type: 'unhandled_promise_rejection' },
        'critical'
      );
    });

    // Tangkap global errors
    window.addEventListener('error', (event) => {
      captureError(
        event.error || new Error(event.message),
        {
          type: 'global_error',
          filename: event.filename,
          lineno: event.lineno,
          colno: event.colno
        },
        'critical'
      );
    });
  }
}

/**
 * Track page views untuk debugging
 */
export function trackPageView(path: string, context: Record<string, any> = {}) {
  rollbar.logInfo('Page View', {
    path,
    timestamp: new Date().toISOString(),
    ...context
  });
}
