import { useState, useRef, useCallback, useEffect } from 'react';
import { apiFetch } from '@/lib/apiFetch';

type InvoiceStatus = 'idle' | 'creating' | 'pending' | 'paid' | 'expired' | 'error';

interface UseInvoiceResult {
  status: InvoiceStatus;
  invoiceUrl: string | null;
  purchaseId: string | null;
  error: string | null;
  startPayment: (params: { letterId: string; templateId: string; templateName: string }) => Promise<void>;
  reset: () => void;
}

/**
 * useInvoice - React hook to create and poll for invoice/payment status
 * - Calls /api/payment/create to create an invoice
 * - Polls /api/purchases/[purchaseId] for payment status
 * - Returns invoiceUrl, purchaseId, status, error, and startPayment()
 */
export function useInvoice(): UseInvoiceResult {
  const [status, setStatus] = useState<InvoiceStatus>('idle');
  const [invoiceUrl, setInvoiceUrl] = useState<string | null>(null);
  const [purchaseId, setPurchaseId] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  const pollInterval = useRef<NodeJS.Timeout | null>(null);

  // Reset state
  const reset = useCallback(() => {
    setStatus('idle');
    setInvoiceUrl(null);
    setPurchaseId(null);
    setError(null);
    if (pollInterval.current) {
      clearInterval(pollInterval.current);
      pollInterval.current = null;
    }
  }, []);

  // Poll purchase status
  const pollStatus = useCallback((purchaseId: string) => {
    if (pollInterval.current) {
      clearInterval(pollInterval.current);
    }
    setStatus('pending');
    pollInterval.current = setInterval(async () => {
      try {
        const res = await apiFetch(`/api/purchases/${purchaseId}`);
        if (!res.ok) {
          throw new Error('Failed to fetch purchase status');
        }
        const data = await res.json();
        if (data.status === 'paid') {
          setStatus('paid');
          if (pollInterval.current) {
            clearInterval(pollInterval.current);
            pollInterval.current = null;
          }
        } else if (data.status === 'expired') {
          setStatus('expired');
          if (pollInterval.current) {
            clearInterval(pollInterval.current);
            pollInterval.current = null;
          }
        }
        // else: still pending, keep polling
      } catch (err: any) {
        setError(err?.message || 'Unknown error');
        setStatus('error');
        if (pollInterval.current) {
          clearInterval(pollInterval.current);
          pollInterval.current = null;
        }
      }
    }, 2000); // poll every 2s
  }, []);

  // Start payment: create invoice and begin polling
  const startPayment = useCallback(
    async ({ letterId, templateId, templateName }: { letterId: string; templateId: string; templateName: string }) => {
      reset();
      setStatus('creating');
      try {
        const res = await apiFetch('/api/payment/create', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ letterId, templateId, templateName }),
        });
        if (!res.ok) {
          const data = await res.json().catch(() => ({}));
          throw new Error(data.error || 'Failed to create invoice');
        }
        const data = await res.json();
        if (!data.invoice_url || !data.purchaseId) {
          throw new Error('Invalid response from server');
        }
        setInvoiceUrl(data.invoice_url);
        setPurchaseId(data.purchaseId);
        setStatus('pending');
        pollStatus(data.purchaseId);
      } catch (err: any) {
        setError(err?.message || 'Unknown error');
        setStatus('error');
      }
    },
    [pollStatus, reset]
  );

  // Cleanup polling on unmount
  // eslint-disable-next-line react-hooks/exhaustive-deps
  useEffect(() => {
    return () => {
      if (pollInterval.current) {
        clearInterval(pollInterval.current);
      }
    };
  }, []);

  return {
    status,
    invoiceUrl,
    purchaseId,
    error,
    startPayment,
    reset,
  };
}