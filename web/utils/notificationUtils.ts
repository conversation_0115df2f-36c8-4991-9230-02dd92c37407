/**
 * Utilitas untuk pengelolaan notifikasi dan toast
 * Membantu dengan pengelolaan pesan notifikasi yang konsisten
 */

// Definisi tipe data untuk toast/notifikasi
export type ToastType = 'success' | 'error' | 'info';

export interface Toast {
  show: boolean;
  message: string;
  type: ToastType;
}

// Default toast - hidden
export const defaultToast: Toast = {
  show: false,
  message: '',
  type: 'info'
};

/**
 * Membuat toast notifikasi dengan tipe dan pesan tertentu
 * @param message Pesan yang akan ditampilkan
 * @param type Tipe toast (success, error, info)
 * @returns Objek toast
 */
export function createToast(message: string, type: ToastType = 'info'): Toast {
  return {
    show: true,
    message,
    type
  };
}

/**
 * Menyembunyikan toast yang sedang ditampilkan
 * @param currentToast Toast saat ini
 * @returns Toast yang disembunyikan
 */
export function hideToast(currentToast: Toast): Toast {
  return {
    ...currentToast,
    show: false
  };
}

/**
 * Menghasilkan pesan error yang konsisten untuk berbagai jenis error
 * @param error Error dari API atau sistem
 * @param defaultMessage Pesan default jika tidak ada pesan error spesifik
 * @returns Pesan error yang sudah diformat
 */
export function getErrorMessage(error: any, defaultMessage: string = 'Terjadi kesalahan'): string {
  if (typeof error === 'string') {
    return error;
  }
  
  if (error?.message) {
    return error.message;
  }
  
  return defaultMessage;
}

/**
 * Helper untuk menangani respons API dengan format yang konsisten
 * @param response Respons fetch API
 * @param successMessage Pesan sukses jika operasi berhasil
 * @param errorMessage Pesan error default jika operasi gagal
 * @returns Objek hasil dengan status dan pesan
 */
export async function handleApiResponse<T>(
  response: Response, 
  successMessage: string = 'Operasi berhasil',
  errorMessage: string = 'Terjadi kesalahan'
): Promise<{ 
  success: boolean; 
  message: string; 
  data?: T; 
  error?: any 
}> {
  try {
    const data = await response.json();
    
    if (response.ok) {
      return {
        success: true,
        message: data.message || successMessage,
        data: data as T
      };
    } else {
      return {
        success: false,
        message: data.error || errorMessage,
        error: data
      };
    }
  } catch (err) {
    return {
      success: false,
      message: errorMessage,
      error: err
    };
  }
}
