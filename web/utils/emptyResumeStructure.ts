import { StructuredResumeData } from "@/types/resume-structured";

export function createEmptyResumeStructure(): StructuredResumeData {
  return {
    personalInfo: {
      fullName: "",
      email: "",
      phone: "",
      location: "",
      linkedin: "",
      website: "",
      github: ""
    },
    professionalSummary: "",
    targetPosition: "",
    experiences: [],
    education: [],
    skills: {
      categories: []
    },
    certifications: [],
    projects: [],
    languages: [],
    awards: [],
    metadata: {
      generatedAt: new Date().toISOString(),
      lastModified: new Date().toISOString()
    }
  };
}