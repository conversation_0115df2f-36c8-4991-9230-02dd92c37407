import { createClient } from '@/lib/supabase';
import { ResumeInput, JobInput } from '@/types/resume';

export interface EdgeFunctionRequest {
  resumeId: string;
  resumeInput: ResumeInput;
  jobInput: JobInput;
  templateId?: string;
}

export interface EdgeFunctionResponse {
  success: boolean;
  resumeId?: string;
  matchScore?: number;
  atsScore?: number;
  message?: string;
  error?: string;
}

export interface ResumeGenerationStatus {
  id: string;
  user_id: string;
  data: {
    resumeInput?: any;
    jobInput?: any;
    suggestions?: string[];
    error_message?: string;
    generatedAt?: string;
    templateId?: string;
  };
  structured_data?: any;
  html?: string;
  status: 'processing' | 'done' | 'error';
  pdf_url?: string;
  created_at: string;
  updated_at: string;
}

/**
 * Converts ArrayBuffer to base64 string
 */
function arrayBufferToBase64(buffer: ArrayBuffer): string {
  const bytes = new Uint8Array(buffer);
  let binary = '';
  
  for (let i = 0; i < bytes.length; i++) {
    binary += String.fromCharCode(bytes[i]);
  }
  
  return btoa(binary);
}

/**
 * Converts ResumeInput and JobInput with ArrayBuffers to base64 format for Edge Function
 */
function serializeRequestForEdgeFunction(request: EdgeFunctionRequest): any {
  // Create a deep copy manually to handle ArrayBuffers properly
  const serializedRequest: any = {
    resumeId: request.resumeId,
    resumeInput: {},
    jobInput: {},
    templateId: request.templateId
  };
  
  // Handle resume input
  if (request.resumeInput.file) {
    serializedRequest.resumeInput.file = {
      mimeType: request.resumeInput.file.mimeType
    };
    
    // Convert ArrayBuffer to base64 if present, or use existing base64 string
    if (request.resumeInput.file.buffer) {
      if (request.resumeInput.file.buffer instanceof ArrayBuffer) {
        serializedRequest.resumeInput.file.buffer = arrayBufferToBase64(request.resumeInput.file.buffer);
      } else if (typeof request.resumeInput.file.buffer === 'string') {
        // Already a base64 string from the API route
        serializedRequest.resumeInput.file.buffer = request.resumeInput.file.buffer;
      }
    }
    
    // Copy extractedText if present
    if (request.resumeInput.file.extractedText) {
      serializedRequest.resumeInput.file.extractedText = request.resumeInput.file.extractedText;
    }
  }
  
  if (request.resumeInput.manual) {
    serializedRequest.resumeInput.manual = { ...request.resumeInput.manual };
  }
  
  // Handle job input
  if (request.jobInput.description) {
    serializedRequest.jobInput.description = request.jobInput.description;
  }
  
  if (request.jobInput.image) {
    serializedRequest.jobInput.image = {
      mimeType: request.jobInput.image.mimeType
    };
    
    // Convert ArrayBuffer to base64 if present, or use existing base64 string
    if (request.jobInput.image.buffer) {
      if (request.jobInput.image.buffer instanceof ArrayBuffer) {
        serializedRequest.jobInput.image.buffer = arrayBufferToBase64(request.jobInput.image.buffer);
      } else if (typeof request.jobInput.image.buffer === 'string') {
        // Already a base64 string from the API route
        serializedRequest.jobInput.image.buffer = request.jobInput.image.buffer;
      }
    }
  }
  
  return serializedRequest;
}

/**
 * Calls the generate-resume Edge Function from the frontend with authentication
 */
export async function callGenerateResumeEdgeFunctionFromFrontend(
  request: EdgeFunctionRequest
): Promise<EdgeFunctionResponse> {
  // Environment validation
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
  
  if (!supabaseUrl || !supabaseAnonKey) {
    const missingVars = [];
    if (!supabaseUrl) missingVars.push('NEXT_PUBLIC_SUPABASE_URL');
    if (!supabaseAnonKey) missingVars.push('NEXT_PUBLIC_SUPABASE_ANON_KEY');
    throw new Error(`Missing required environment variables: ${missingVars.join(', ')}`);
  }

  const supabase = createClient();
  
  // Get the user session for authentication
  const { data: { session }, error: sessionError } = await supabase.auth.getSession();
  
  if (sessionError || !session) {
    console.error('Authentication error:', sessionError);
    throw new Error('User not authenticated');
  }

  try {
    // Serialize the request to convert ArrayBuffers to base64
    const serializedRequest = serializeRequestForEdgeFunction(request);
    
    // Call the generate-resume edge function with authentication
    const { data, error } = await supabase.functions.invoke('generate-resume', {
      body: serializedRequest,
      headers: {
        Authorization: `Bearer ${session.access_token}`
      }
    });

    if (error) {
      // Update the resume status to error in case edge function failed to do so
      try {
        await supabase
          .from('resumes')
          .update({
            status: 'error',
            error_message: error.message || 'Edge function call failed',
            updated_at: new Date().toISOString()
          })
          .eq('id', request.resumeId);
      } catch (updateError) {
        console.error('Failed to update resume status to error:', updateError);
      }
      
      throw new Error(`Edge Function Error: ${error.message || 'Unknown error'}`);
    }

    if (!data) {
      console.error('Edge Function returned no data');
      // Update the resume status to error
      try {
        await supabase
          .from('resumes')
          .update({
            status: 'error',
            error_message: 'Edge function returned no data',
            updated_at: new Date().toISOString()
          })
          .eq('id', request.resumeId);
      } catch (updateError) {
        console.error('Failed to update resume status to error:', updateError);
      }
      
      throw new Error('Edge Function returned no data');
    }

    if (!data.success) {
      console.error('Edge Function returned error:', data.error);
      // The edge function should have already updated the resume status, but let's make sure
      throw new Error(data.error || 'Edge function reported failure');
    }
    
    return data as EdgeFunctionResponse;
  } catch (error) {
    console.error('Edge Function Call Exception:', {
      name: error instanceof Error ? error.name : 'Unknown',
      message: error instanceof Error ? error.message : String(error)
    });
    console.log('=== Frontend Resume Edge Function Call END (ERROR) ===');
    
    // Update resume status to error if not already done
    try {
      await supabase
        .from('resumes')
        .update({
          status: 'error',
          error_message: error instanceof Error ? error.message : String(error),
          updated_at: new Date().toISOString()
        })
        .eq('id', request.resumeId);
    } catch (updateError) {
      console.error('Failed to update resume status to error:', updateError);
    }
    
    throw error;
  }
}

/**
 * Calls the generate-resume Edge Function (legacy - for server-side use)
 */
export async function callGenerateResumeEdgeFunction(
  request: EdgeFunctionRequest
): Promise<void> {
  console.log('=== Edge Function Diagnostic START ===');
  console.log('1. Request Summary:', {
    resumeId: request.resumeId,
    resumeInputType: request.resumeInput.file ? 'file' : 'manual',
    jobInputType: request.jobInput.description ? 'text' : 'image',
    resumeFileType: request.resumeInput.file?.mimeType,
    jobImageType: request.jobInput.image?.mimeType
  });
  
  console.log('1.5. Original Request Detailed Check:', {
    requestKeys: Object.keys(request),
    resumeInputKeys: Object.keys(request.resumeInput),
    jobInputKeys: Object.keys(request.jobInput),
    resumeFileBuffer: request.resumeInput.file?.buffer ?
      (request.resumeInput.file.buffer instanceof ArrayBuffer ?
        `ArrayBuffer(${request.resumeInput.file.buffer.byteLength} bytes)` :
        `Base64String(${request.resumeInput.file.buffer.length} chars)`) :
      'NONE',
    resumeFileExtractedText: request.resumeInput.file?.extractedText ? `Text(${request.resumeInput.file.extractedText.length} chars)` : 'NONE',
    jobImageBuffer: request.jobInput.image?.buffer ?
      (request.jobInput.image.buffer instanceof ArrayBuffer ?
        `ArrayBuffer(${request.jobInput.image.buffer.byteLength} bytes)` :
        `Base64String(${request.jobInput.image.buffer.length} chars)`) :
      'NONE',
    jobDescription: request.jobInput.description ? `Text(${request.jobInput.description.length} chars)` : 'NONE'
  });

  // Environment validation
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
  
  console.log('2. Environment Check:', {
    supabaseUrl: supabaseUrl ? `${supabaseUrl.substring(0, 30)}...` : 'MISSING',
    supabaseAnonKey: supabaseAnonKey ? `${supabaseAnonKey.substring(0, 20)}...` : 'MISSING',
    edgeFunctionUrl: supabaseUrl ? `${supabaseUrl}/functions/v1/generate-resume` : 'UNDEFINED'
  });

  if (!supabaseUrl || !supabaseAnonKey) {
    const missingVars = [];
    if (!supabaseUrl) missingVars.push('NEXT_PUBLIC_SUPABASE_URL');
    if (!supabaseAnonKey) missingVars.push('NEXT_PUBLIC_SUPABASE_ANON_KEY');
    throw new Error(`Missing required environment variables: ${missingVars.join(', ')}`);
  }

  const supabase = createClient();
  console.log('3. Supabase client created');
  
  try {
    console.log('4. Invoking Edge Function "generate-resume"...');
    
    // Serialize the request to convert ArrayBuffers to base64
    const serializedRequest = serializeRequestForEdgeFunction(request);
    
    console.log('5. Serialized Request Debug:', {
      requestKeys: Object.keys(serializedRequest),
      resumeInputKeys: Object.keys(serializedRequest.resumeInput || {}),
      jobInputKeys: Object.keys(serializedRequest.jobInput || {}),
      resumeId: serializedRequest.resumeId,
      hasResumeFile: !!serializedRequest.resumeInput?.file,
      hasResumeManual: !!serializedRequest.resumeInput?.manual,
      hasJobDescription: !!serializedRequest.jobInput?.description,
      hasJobImage: !!serializedRequest.jobInput?.image
    });
    
    const requestBodyString = JSON.stringify(serializedRequest);
    console.log('6. Request Body String Length:', requestBodyString.length);
    console.log('7. Request Body Preview (first 500 chars):', requestBodyString.substring(0, 500));
    
    // Try passing the object directly instead of stringified JSON
    const { data, error } = await supabase.functions.invoke('generate-resume', {
      body: serializedRequest,
    });

    console.log('5. Edge Function Response Received:', {
      hasData: !!data,
      dataType: typeof data,
      hasError: !!error,
      errorType: typeof error
    });

    if (error) {
      console.error('6. Edge Function Error Details:', {
        message: error.message,
        details: error.details,
        hint: error.hint,
        code: error.code,
        context: error.context
      });
      
      throw new Error(`Edge Function Error: ${error.message || 'Unknown error'}${error.details ? ` (${error.details})` : ''}${error.hint ? ` Hint: ${error.hint}` : ''}`);
    }

    if (!data) {
      throw new Error('Edge Function returned no data');
    }

    console.log('7. Edge Function Success:', data);
    console.log('=== Edge Function Diagnostic END (SUCCESS) ===');
  } catch (error) {
    console.error('8. Edge Function Call Exception:', {
      name: error instanceof Error ? error.name : 'Unknown',
      message: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined
    });
    console.log('=== Edge Function Diagnostic END (ERROR) ===');
    
    // Re-throw the error to match the expected behavior in route.ts
    throw error;
  }
}

/**
 * Creates a new resume generation request in the database
 */
export async function createResumeGenerationRequest(
  userId: string,
  resumeInput: ResumeInput,
  jobInput: JobInput,
  templateId?: string
): Promise<string> {
  const supabase = createClient();
  
  const { data, error } = await supabase
    .from('resumes')
    .insert({
      user_id: userId,
      data: { resumeInput, jobInput, templateId },
      status: 'processing'
    })
    .select('id')
    .single();

  if (error) {
    throw new Error(`Failed to create resume generation request: ${error.message}`);
  }

  return data.id;
}

/**
 * Gets the status of a resume generation request
 */
export async function getResumeGenerationStatus(
  resumeId: string
): Promise<ResumeGenerationStatus | null> {
  const supabase = createClient();
  
  const { data, error } = await supabase
    .from('resumes')
    .select('id, user_id, data, structured_data, html, status, pdf_url, created_at, updated_at')
    .eq('id', resumeId)
    .single();

  if (error) {
    if (error.code === 'PGRST116') {
      return null; // Record not found
    }
    throw new Error(`Failed to get resume generation status: ${error.message}`);
  }

  return data as ResumeGenerationStatus;
}

/**
 * Polls for resume generation completion
 */
export async function pollResumeGenerationStatus(
  resumeId: string,
  maxAttempts: number = 60, // 60 attempts = 5 minutes with 5-second intervals
  intervalMs: number = 5000
): Promise<ResumeGenerationStatus> {
  return new Promise((resolve, reject) => {
    let attempts = 0;
    
    const pollInterval = setInterval(async () => {
      attempts++;
      
      try {
        const status = await getResumeGenerationStatus(resumeId);
        
        if (!status) {
          clearInterval(pollInterval);
          reject(new Error('Resume generation record not found'));
          return;
        }

        // Check if generation is complete
        if (status.status === 'done' || status.status === 'error') {
          clearInterval(pollInterval);
          resolve(status);
          return;
        }

        // Check if we've exceeded max attempts
        if (attempts >= maxAttempts) {
          clearInterval(pollInterval);
          reject(new Error('Resume generation timed out'));
          return;
        }
      } catch (error) {
        clearInterval(pollInterval);
        reject(error);
      }
    }, intervalMs);
  });
}

/**
 * Initiates async resume generation and returns immediately
 */
export async function initiateAsyncResumeGeneration(
  userId: string,
  resumeInput: ResumeInput,
  jobInput: JobInput,
  templateId?: string
): Promise<string> {
  // Create the database record
  const resumeId = await createResumeGenerationRequest(
    userId,
    resumeInput,
    jobInput,
    templateId
  );

  // Call the Edge Function (fire and forget)
  const request: EdgeFunctionRequest = {
    resumeId,
    resumeInput,
    jobInput,
    templateId
  };

  // Don't await this - let it run in the background
  callGenerateResumeEdgeFunction(request).catch(error => {
    console.error('Edge Function call failed:', error);
  });

  return resumeId;
}

/**
 * Initiates async resume generation and waits for completion
 */
export async function generateResumeAndWait(
  userId: string,
  resumeInput: ResumeInput,
  jobInput: JobInput,
  templateId?: string,
  maxWaitTimeMs: number = 300000 // 5 minutes
): Promise<ResumeGenerationStatus> {
  // Initiate the generation
  const resumeId = await initiateAsyncResumeGeneration(
    userId,
    resumeInput,
    jobInput,
    templateId
  );

  // Wait for completion
  const maxAttempts = Math.floor(maxWaitTimeMs / 5000);
  return await pollResumeGenerationStatus(resumeId, maxAttempts);
}

/**
 * Gets all resume generation requests for a user
 */
export async function getUserResumeGenerations(
  userId: string,
  limit: number = 50,
  offset: number = 0
): Promise<ResumeGenerationStatus[]> {
  const supabase = createClient();
  
  const { data, error } = await supabase
    .from('resumes')
    .select('id, user_id, data, structured_data, html, status, pdf_url, created_at, updated_at')
    .eq('user_id', userId)
    .order('created_at', { ascending: false })
    .range(offset, offset + limit - 1);

  if (error) {
    throw new Error(`Failed to get user resume generations: ${error.message}`);
  }

  return data as ResumeGenerationStatus[];
}

/**
 * Deletes a resume generation request
 */
export async function deleteResumeGeneration(resumeId: string): Promise<void> {
  const supabase = createClient();
  
  const { error } = await supabase
    .from('resumes')
    .delete()
    .eq('id', resumeId);

  if (error) {
    throw new Error(`Failed to delete resume generation: ${error.message}`);
  }
}

/**
 * Updates a resume generation request
 */
export async function updateResumeGeneration(
  resumeId: string,
  updates: Partial<ResumeGenerationStatus>
): Promise<void> {
  const supabase = createClient();
  
  const { error } = await supabase
    .from('resumes')
    .update(updates)
    .eq('id', resumeId);

  if (error) {
    throw new Error(`Failed to update resume generation: ${error.message}`);
  }
}