import { NextRequest, NextResponse } from 'next/server';
import rollbar from '@/lib/rollbar';
import { captureApiError } from './errorMonitoring';

/**
 * Middleware untuk menangkap dan melaporkan error di API endpoints
 */
export async function withErrorReporting(
  request: NextRequest,
  handler: () => Promise<NextResponse>,
  endpoint: string
) {
  try {
    return await handler();
  } catch (error) {
    // Ekstrak informasi request untuk konteks debugging
    const url = request.url;
    const method = request.method;
    const headers = Object.fromEntries(request.headers);
    
    // Log ke Rollbar
    captureApiError(endpoint, error, {
      url,
      method,
      headers: JSON.stringify(headers),
    });
    
    // <PERSON><PERSON> respons error
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'An unexpected error occurred' 
      },
      { status: 500 }
    );
  }
}

/**
 * Wrapper untuk API route handlers
 */
// Overload for handlers without context parameter (like /letters/route.ts)
export function createApiHandler(
  endpoint: string,
  handler: (req: NextRequest) => Promise<NextResponse>
): (request: NextRequest) => Promise<NextResponse>;

// Overload for handlers with required context parameter (like /resume/[id]/route.ts)
export function createApiHandler(
  endpoint: string,
  handler: (req: NextRequest, context: { params: any }) => Promise<NextResponse>
): (request: NextRequest, context: { params: any }) => Promise<NextResponse>;

// Overload for handlers with optional context parameter (like /generate-application-letter/route.ts)
export function createApiHandler(
  endpoint: string,
  handler: (req: NextRequest, context?: { params: any }) => Promise<NextResponse>
): (request: NextRequest, context?: { params: any }) => Promise<NextResponse>;

// Implementation
export function createApiHandler(
  endpoint: string,
  handler: (req: NextRequest, context?: any) => Promise<NextResponse>
) {
  return async (
    request: NextRequest,
    context?: any
  ) => {
    return withErrorReporting(
      request,
      () => handler(request, context),
      endpoint
    );
  };
}
