const fs = require('fs');
const path = require('path');
const Handlebars = require('handlebars');

// Load and register helpers
const helpers = require('./utils/handlebars-helpers/index.js');
Object.keys(helpers).forEach(name => {
  Handlebars.registerHelper(name, helpers[name]);
});

// Load templates
const templates = {
  'clean-professional': fs.readFileSync(path.join(__dirname, 'utils/handlebars-templates/clean-professional.hbs'), 'utf8'),
  'modern-clean': fs.readFileSync(path.join(__dirname, 'utils/handlebars-templates/modern-clean.hbs'), 'utf8'),
  'classic-professional': fs.readFileSync(path.join(__dirname, 'utils/handlebars-templates/classic-professional.hbs'), 'utf8')
};

// Compile templates
const compiledTemplates = {};
Object.keys(templates).forEach(name => {
  compiledTemplates[name] = Handlebars.compile(templates[name]);
});

// Test data with extreme edge cases
const testData = {
  name: "<PERSON>",
  jobTitle: "Software Developer",
  // Test null/undefined/empty contact fields
  email: "<EMAIL>",
  phone: null,
  linkedin: "",
  website: undefined,
  github: "   ",
  location: "Jakarta",
  
  // Test empty arrays and arrays with empty strings
  experiences: [
    {
      jobTitle: "Developer",
      company: "Tech Corp",
      startDate: "2022",
      endDate: "Present",
      responsibilities: ["", "   ", null, undefined, "Developed applications", "", "Led team meetings", "   "]
    }
  ],
  
  // Test skills with empty values
  skills: [
    {
      category: "Programming",
      skills: "JavaScript, Python"
    },
    {
      category: "",
      skills: "   "
    },
    {
      category: "Tools",
      skills: ""
    }
  ],
  
  // Test projects with mixed content
  projects: [
    {
      title: "Test Project",
      description: "A test project",
      achievements: ["", "   ", "Completed on time", "", "Improved performance"]
    }
  ],
  
  // Test languages with empty values
  languages: [
    {
      language: "English",
      proficiency: "Native"
    },
    {
      language: "",
      proficiency: "Fluent"
    },
    {
      language: "Indonesian",
      proficiency: ""
    }
  ]
};

console.log('=== TESTING CURRENT EMPTY FIELD HANDLING ===\n');

Object.keys(compiledTemplates).forEach(templateName => {
  console.log(`\n--- Testing ${templateName} Template ---`);
  
  try {
    const html = compiledTemplates[templateName](testData);
    
    // Check for common issues
    const issues = [];
    
    // Check for empty list items
    const emptyListItems = html.match(/<li>\s*<\/li>/g);
    if (emptyListItems) {
      issues.push(`Found ${emptyListItems.length} empty <li> elements`);
    }
    
    // Check for empty list items with only whitespace
    const whitespaceListItems = html.match(/<li>\s+<\/li>/g);
    if (whitespaceListItems) {
      issues.push(`Found ${whitespaceListItems.length} whitespace-only <li> elements`);
    }
    
    // Check for trailing separators in contact info
    const trailingSeparators = html.match(/[•|]\s*<\/div>/g);
    if (trailingSeparators) {
      issues.push(`Found ${trailingSeparators.length} potential trailing separators`);
    }
    
    // Check for double separators
    const doubleSeparators = html.match(/[•|]\s*[•|]/g);
    if (doubleSeparators) {
      issues.push(`Found ${doubleSeparators.length} double separators`);
    }
    
    // Extract contact info section for detailed analysis
    const contactMatch = html.match(/<div class="contact-info">(.*?)<\/div>/s);
    if (contactMatch) {
      const contactInfo = contactMatch[1].trim();
      console.log(`Contact Info Output: "${contactInfo}"`);
    }
    
    if (issues.length === 0) {
      console.log('✅ PASSED - No issues detected');
    } else {
      console.log('❌ ISSUES FOUND:');
      issues.forEach(issue => console.log(`  - ${issue}`));
    }
    
  } catch (error) {
    console.log(`❌ ERROR: ${error.message}`);
  }
});

console.log('\n=== HELPER FUNCTION TESTS ===\n');

// Test helper functions directly
console.log('Testing hasContent helper:');
console.log(`hasContent("test"): ${helpers.hasContent("test")}`);
console.log(`hasContent(""): ${helpers.hasContent("")}`);
console.log(`hasContent("   "): ${helpers.hasContent("   ")}`);
console.log(`hasContent(null): ${helpers.hasContent(null)}`);
console.log(`hasContent(undefined): ${helpers.hasContent(undefined)}`);

console.log('\nTesting filterEmpty helper:');
console.log(`filterEmpty(["a", "", "b", "   ", "c"]): ${JSON.stringify(helpers.filterEmpty(["a", "", "b", "   ", "c"]))}`);

console.log('\nTesting joinFiltered helper:');
console.log(`joinFiltered(["a", "", "b", "   ", "c"], ", "): "${helpers.joinFiltered(["a", "", "b", "   ", "c"], ", ")}"`);

console.log('\nTesting contactArray helper:');
const contactResult = helpers.contactArray("<EMAIL>", null, "", undefined, "   ", "Jakarta");
console.log(`contactArray with mixed empty values: ${JSON.stringify(contactResult)}`);

console.log('\nTesting joinContact helper:');
const joinResult = helpers.joinContact("<EMAIL>", null, "", undefined, "   ", "Jakarta", " • ");
console.log(`joinContact with mixed empty values: "${joinResult}"`);