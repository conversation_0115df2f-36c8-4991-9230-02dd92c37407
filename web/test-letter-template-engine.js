/**
 * Simple test script to verify the letter template engine implementation
 */

const { 
  fillLetterTemplate, 
  getAvailableLetterTemplateIds, 
  isLetterTemplateAvailable, 
  testLetterTemplate 
} = require('./utils/letter-template-engine.ts');

const { 
  plainTextTemplate, 
  getTemplateById 
} = require('./utils/letter-templates/applicationLetterTemplates.ts');

const { 
  createDefaultStructuredLetterData 
} = require('./types/letter-structured.ts');

console.log('🚀 Testing Letter Template Engine...\n');

// Test 1: Check available template IDs
console.log('📋 Test 1: Available Template IDs');
try {
  const availableIds = getAvailableLetterTemplateIds();
  console.log('✅ Available template IDs:', availableIds);
  console.log(`   Found ${availableIds.length} templates\n`);
} catch (error) {
  console.error('❌ Failed to get available template IDs:', error.message);
}

// Test 2: Check template availability
console.log('🔍 Test 2: Template Availability Check');
const testTemplateIds = ['plain-text', 'classic-blue', 'nonexistent-template'];
testTemplateIds.forEach(templateId => {
  try {
    const isAvailable = isLetterTemplateAvailable(templateId);
    console.log(`   ${templateId}: ${isAvailable ? '✅ Available' : '❌ Not Available'}`);
  } catch (error) {
    console.error(`   ${templateId}: ❌ Error checking availability:`, error.message);
  }
});
console.log();

// Test 3: Create sample structured data
console.log('📝 Test 3: Creating Sample Data');
try {
  const sampleData = createDefaultStructuredLetterData('plain-text', 'id');
  
  // Add some sample content
  sampleData.subject.position = 'Software Developer';
  sampleData.signature.name = 'John Doe';
  sampleData.body.paragraphs = [
    'Saya sangat tertarik untuk bergabung dengan perusahaan Anda sebagai Software Developer.',
    'Dengan pengalaman 3 tahun di bidang pengembangan web, saya yakin dapat berkontribusi positif.'
  ];
  sampleData.recipient.company = 'PT Example Company';
  
  console.log('✅ Sample data created successfully');
  console.log(`   Position: ${sampleData.subject.position}`);
  console.log(`   Applicant: ${sampleData.signature.name}`);
  console.log(`   Company: ${sampleData.recipient.company}\n`);
} catch (error) {
  console.error('❌ Failed to create sample data:', error.message);
  process.exit(1);
}

// Test 4: Test template with sample data
console.log('🧪 Test 4: Template Testing');
try {
  const template = getTemplateById('plain-text');
  if (!template) {
    throw new Error('Plain text template not found');
  }
  
  const testResult = testLetterTemplate(template, sampleData);
  
  if (testResult.success) {
    console.log('✅ Template test passed');
    console.log(`   HTML length: ${testResult.htmlLength} characters`);
    if (testResult.warnings && testResult.warnings.length > 0) {
      console.log('   Warnings:', testResult.warnings);
    }
  } else {
    console.log('❌ Template test failed:', testResult.error);
  }
  console.log();
} catch (error) {
  console.error('❌ Template test error:', error.message);
}

// Test 5: Full template filling
console.log('🎨 Test 5: Full Template Rendering');
try {
  const template = getTemplateById('plain-text');
  if (!template) {
    throw new Error('Plain text template not found');
  }
  
  const html = fillLetterTemplate(template, sampleData);
  
  console.log('✅ Template rendered successfully');
  console.log(`   HTML length: ${html.length} characters`);
  console.log(`   Contains signature name: ${html.includes(sampleData.signature.name) ? '✅' : '❌'}`);
  console.log(`   Contains position: ${html.includes(sampleData.subject.position) ? '✅' : '❌'}`);
  console.log(`   Contains company: ${html.includes(sampleData.recipient.company || '') ? '✅' : '❌'}`);
  
  // Save a sample output for manual inspection
  const fs = require('fs');
  fs.writeFileSync('./test-letter-output.html', html);
  console.log('   Sample output saved to: test-letter-output.html\n');
  
} catch (error) {
  console.error('❌ Template rendering failed:', error.message);
  console.error('   Stack trace:', error.stack);
}

console.log('🎯 Letter Template Engine Test Complete!');