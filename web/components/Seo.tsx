import Head from 'next/head';

interface SeoProps {
  title?: string;
  description?: string;
  canonical?: string;
  ogType?: string;
  ogImage?: string;
}

export default function Seo({
  title = 'Gigsta - Buat Surat Lamaran secara Instan dan Dipersonalisasi menggunakan AI',
  description = 'Buat surat lamaran dan email lamaran otomatis berbasis AI, serta analisis kecocokan CV dengan lowongan kerja. Maksimalkan peluang karir Anda bersama Gigsta.',
  canonical = 'https://www.gigsta.io',
  ogType = 'website',
  ogImage = '/images/logo.png',
}: SeoProps) {
  const fullTitle = title.includes('Gigsta') ? title : `${title} | Gigsta`;
  
  return (
    <Head>
      {/* Primary Meta Tags */}
      <title>{fullTitle}</title>
      <meta name="title" content={fullTitle} />
      <meta name="description" content={description} />
      
      {/* Canonical Link */}
      <link rel="canonical" href={canonical} />
      
      {/* Open Graph / Facebook */}
      <meta property="og:type" content={ogType} />
      <meta property="og:url" content={canonical} />
      <meta property="og:title" content={fullTitle} />
      <meta property="og:description" content={description} />
      <meta property="og:image" content={ogImage} />
      
      {/* Twitter */}
      <meta property="twitter:card" content="summary_large_image" />
      <meta property="twitter:url" content={canonical} />
      <meta property="twitter:title" content={fullTitle} />
      <meta property="twitter:description" content={description} />
      <meta property="twitter:image" content={ogImage} />
      
      {/* Favicon */}
      <link rel="icon" href="/favicon.ico" />
    </Head>
  );
}
