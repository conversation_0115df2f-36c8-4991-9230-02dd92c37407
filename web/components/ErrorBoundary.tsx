'use client';

import { useEffect } from 'react';
import rollbar from '../lib/rollbar';

export function ErrorBoundary({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  useEffect(() => {
    // Log the error to Rollbar
    rollbar.logError(error);
  }, [error]);

  return (
    <div className="flex flex-col items-center justify-center min-h-screen p-4">
      <div className="text-center p-6 bg-red-50 rounded-lg border border-red-200 max-w-xl">
        <h2 className="text-2xl font-semibold text-red-700 mb-4">Ada yang tidak beres</h2>
        <p className="text-gray-700 mb-6">
          <PERSON><PERSON> maaf, kami mengalami masalah teknis. Tim kami telah diberitahu dan akan segera memperbaikinya.
        </p>
        <button
          onClick={reset}
          className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
        >
          <PERSON><PERSON>
        </button>
      </div>
    </div>
  );
}
