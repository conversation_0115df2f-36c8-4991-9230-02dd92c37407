"use client";

import { FC, useMemo, useEffect, useRef, useState } from "react";
import { createPortal } from "react-dom";
import { ResumeTemplate } from "@/utils/resume-templates/resumeTemplates";
import { fillResumeTemplate } from "@/utils/template-engine";
import { StructuredResumeData } from "@/types/resume-structured";
import { useShadowDOM } from "@/hooks/useShadowDOM";

interface TemplatePreviewThumbnailProps {
  template: ResumeTemplate;
  className?: string;
}

// Sample resume data for preview thumbnails in StructuredResumeData format
const sampleResumeData: StructuredResumeData = {
  personalInfo: {
    fullName: "John Doe",
    email: "<EMAIL>",
    phone: "+62 812 3456 7890",
    linkedin: "linkedin.com/in/johndoe",
    location: "Jakarta, Indonesia"
  },
  professionalSummary: "Profesional marketing berpengalaman dengan 5+ tahun pengalaman dalam pemasaran digital dan manajemen merek. Terampil dalam pengembangan kampanye, strategi media sosial, dan analisis pasar untuk mencapai target penjualan.",
  targetPosition: "Eksekutif Pemasaran",
  experiences: [
    {
      id: "exp-1",
      jobTitle: "Senior Eksekutif Pemasaran",
      company: "PT Digital Solutions Indonesia",
      location: "Jakarta",
      startDate: "Jan 2021",
      endDate: "Sekarang",
      responsibilities: [
        "Memimpin kampanye pemasaran digital dan meningkatkan kesadaran merek sebesar 40%",
        "Mengembangkan strategi konten untuk media sosial yang meningkatkan engagement 60%",
        "Mengelola anggaran pemasaran dan mengoptimalkan return on investment kampanye"
      ]
    },
    {
      id: "exp-2",
      jobTitle: "Koordinator Pemasaran",
      company: "Creative Media Group",
      location: "Jakarta",
      startDate: "Jun 2019",
      endDate: "Des 2020",
      responsibilities: [
        "Mengkoordinasikan acara pemasaran dan kampanye peluncuran produk",
        "Berkolaborasi dengan tim kreatif untuk mengembangkan materi pemasaran",
        "Menganalisis tren pasar dan analisis kompetitor untuk perbaikan strategi"
      ]
    }
  ],
  education: [
    {
      id: "edu-1",
      degree: "Sarjana Komunikasi Pemasaran",
      institution: "Universitas Indonesia",
      graduationDate: "2019",
      gpa: "3.8"
    }
  ],
  skills: {
    categories: [
      {
        category: "Pemasaran Digital",
        skills: ["Media Sosial Marketing", "Google Ads", "Facebook Ads", "Pembuatan Konten"]
      },
      {
        category: "Analisis & Tools",
        skills: ["Google Analytics", "Hootsuite", "Canva", "Microsoft Office"]
      },
      {
        category: "Komunikasi",
        skills: ["Presentasi", "Copywriting", "Public Speaking", "Bahasa Inggris"]
      }
    ]
  },
  projects: [
    {
      id: "proj-1",
      title: "Peluncuran Kampanye Digital",
      description: "Kampanye peluncuran produk baru dengan strategi pemasaran digital terintegrasi",
      technologies: ["Google Ads", "Facebook Ads", "Instagram"]
    }
  ],
  certifications: [
    {
      id: "cert-1",
      name: "Sertifikat Google Ads",
      issuer: "Google",
      date: "2023"
    }
  ]
};

// Modal Preview Component
const ModalPreview: FC<{ template: ResumeTemplate; previewHtml: string; scale: number }> = ({ previewHtml, scale }) => {
  const modalShadowRef = useShadowDOM({ html: previewHtml });
  
  return (
    <div
      ref={modalShadowRef}
      className="bg-white shadow-lg origin-top-left"
      style={{
        width: '794px', // A4 width at 96 DPI
        minHeight: '1123px', // A4 height at 96 DPI
        transform: `scale(${scale})`,
      }}
    />
  );
};

const TemplatePreviewThumbnail: FC<TemplatePreviewThumbnailProps> = ({ template }) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [scale, setScale] = useState(0.5);
  const [modalScale, setModalScale] = useState(1);
  const [isModalOpen, setIsModalOpen] = useState(false);

  // Generate the preview HTML first
  const previewHtml = useMemo(() => {
    try {
      // Use the template engine to fill the template with sample data
      return fillResumeTemplate(template, sampleResumeData);
    } catch (error) {
      console.error('Error generating template preview:', error);
      // Return a fallback error message
      return `
        <div style="padding: 20px; text-align: center; color: #666;">
          <p>Tidak dapat melihat pratinjau template ini</p>
          <p style="font-size: 12px; margin-top: 10px;">Kesalahan: ${error instanceof Error ? error.message : 'Kesalahan tidak diketahui'}</p>
        </div>
      `;
    }
  }, [template]);

  // Use Shadow DOM for style isolation
  const shadowContainerRef = useShadowDOM({ html: previewHtml });

  // Calculate optimal scale based on container size
  useEffect(() => {
    const calculateScale = () => {
      if (!containerRef.current || !shadowContainerRef.current) return;
      
      const container = containerRef.current;
      const containerWidth = container.offsetWidth;
      const containerHeight = container.offsetHeight;
      
      // Standard A4 dimensions in pixels at 96 DPI (210mm x 297mm)
      const a4Width = 794; // 210mm at 96 DPI
      const a4Height = 1123; // 297mm at 96 DPI
      
      // Calculate scale to fit within container while maintaining aspect ratio
      const scaleX = containerWidth / a4Width;
      const scaleY = containerHeight / a4Height;
      const optimalScale = Math.min(scaleX, scaleY);
      
      setScale(Math.max(0.1, Math.min(1, optimalScale)));
    };

    // Small delay to ensure DOM is ready
    const timer = setTimeout(calculateScale, 100);
    
    // Recalculate on window resize
    window.addEventListener('resize', calculateScale);
    
    // Use ResizeObserver for more precise container size changes
    const resizeObserver = new ResizeObserver(calculateScale);
    if (containerRef.current) {
      resizeObserver.observe(containerRef.current);
    }
    
    return () => {
      clearTimeout(timer);
      window.removeEventListener('resize', calculateScale);
      resizeObserver.disconnect();
    };
  }, [shadowContainerRef]);

  // Calculate modal scale to fit screen
  useEffect(() => {
    if (!isModalOpen) return;

    const calculateModalScale = () => {
      // Get viewport dimensions
      const viewportWidth = window.innerWidth;
      const viewportHeight = window.innerHeight;
      
      // Account for modal padding and margins
      const modalPadding = 32; // 16px padding on each side
      const availableWidth = viewportWidth - modalPadding;
      const availableHeight = viewportHeight - modalPadding;
      
      // Standard A4 dimensions in pixels at 96 DPI
      const a4Width = 794;
      const a4Height = 1123;
      
      // Calculate scale to fit within available space
      const scaleX = availableWidth / a4Width;
      const scaleY = availableHeight / a4Height;
      
      // Use the smaller scale to ensure it fits both dimensions
      const optimalScale = Math.min(scaleX, scaleY, 1); // Don't scale beyond 100%
      
      setModalScale(Math.max(0.1, optimalScale));
    };

    calculateModalScale();
    
    // Recalculate on window resize
    window.addEventListener('resize', calculateModalScale);
    
    return () => {
      window.removeEventListener('resize', calculateModalScale);
    };
  }, [isModalOpen]);

  return (
    <>
      <div
        ref={containerRef}
        className={`w-full h-full overflow-hidden relative text-left`}
      >
        <div
          ref={shadowContainerRef}
          className="absolute top-0 left-0 origin-top-left transform-gpu bg-white"
          style={{
            transform: `scale(${scale})`,
          }}
        />
        
        {/* Sticky Enlarge Button */}
        <div
          className="absolute top-2 right-2 bg-primary text-white p-2 rounded-full hover:bg-blue-700 transition-all duration-200 cursor-pointer shadow-lg z-15"
          onClick={(e) => {
            e.stopPropagation();
            e.preventDefault();
            setIsModalOpen(true);
          }}
          title="Perbesar pratinjau"
          role="button"
          tabIndex={0}
          onKeyDown={(e) => {
            if (e.key === 'Enter' || e.key === ' ') {
              e.preventDefault();
              setIsModalOpen(true);
            }
          }}
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-4 w-4"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7"
            />
          </svg>
        </div>
      </div>

      {/* Modal Overlay - Rendered via Portal */}
      {isModalOpen && typeof document !== 'undefined' && createPortal(
        <div
          className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4"
          onClick={(e) => {
            // Only close if clicking on the overlay itself, not on child elements
            if (e.target === e.currentTarget) {
              e.stopPropagation();
              e.preventDefault();
              setIsModalOpen(false);
            }
          }}
        >
          <div
            className="relative bg-white rounded-lg shadow-xl overflow-hidden"
            onClick={(e) => {
              e.stopPropagation();
              e.preventDefault();
            }}
            style={{
              width: `${794 * modalScale}px`,
              height: `${1123 * modalScale}px`,
              maxWidth: '100vw',
              maxHeight: '100vh',
            }}
          >
            {/* Close Button */}
            <button
              className="absolute top-2 right-2 bg-gray-200 hover:bg-gray-300 text-gray-700 p-2 rounded-full transition-colors duration-200 z-10"
              onClick={(e) => {
                e.stopPropagation();
                e.preventDefault();
                setIsModalOpen(false);
              }}
              title="Tutup pratinjau"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-5 w-5"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>

            {/* Full-size Preview */}
            <div className="w-full h-full overflow-hidden">
              <ModalPreview template={template} previewHtml={previewHtml} scale={modalScale} />
            </div>
          </div>
        </div>,
        document.body
      )}
    </>
  );
};

export default TemplatePreviewThumbnail;