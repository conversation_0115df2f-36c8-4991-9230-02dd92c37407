'use client';

import Link from 'next/link';
import { Nunito } from 'next/font/google';

const nunito = Nunito({ subsets: ['latin'], weight: '700' });

export default function NavbarMinimal() {
  return (
    <nav className="bg-white shadow-sm w-full z-10">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-1">
        <div className="flex justify-between h-16">
          <div className="flex">
            <div className="flex-shrink-0 flex items-center">
              <Link href="/" className="flex items-center">
                <img src="/images/logo.svg" alt="Gigsta Logo" className="h-16 invert" />
                <span className={`ml-2 text-2xl font-bold text-gray-900 tracking-tight ${nunito.className}`}>Gigsta</span>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </nav>
  );
}
