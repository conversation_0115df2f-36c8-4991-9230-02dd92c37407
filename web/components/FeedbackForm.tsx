'use client';

import { apiFetch } from '@/lib/apiFetch';

import { useState, useEffect } from 'react';
import { useAnalytics } from '@/hooks/useAnalytics';

type FeedbackRating = 1 | 2 | 3 | 4 | 5;

interface FeedbackFormProps {
  featureType: 'email-application' | 'application-letter' | 'job-match' | 'general';
  onClose: () => void;
}

export default function FeedbackForm({ featureType, onClose }: FeedbackFormProps) {
  const { trackEvent } = useAnalytics();
  const [rating, setRating] = useState<FeedbackRating | null>(null);
  const [comment, setComment] = useState('');
  const [email, setEmail] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [error, setError] = useState('');

  const featureNames = {
    'email-application': 'Em<PERSON>',
    'application-letter': 'Surat Lamaran',
    'job-match': 'Kecoco<PERSON>',
    'general': 'Keseluruhan <PERSON>p<PERSON>'
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!rating) {
      setError('Harap pilih rating terlebih dahulu');
      return;
    }
    
    setError('');
    setIsSubmitting(true);
    
    try {
      const response = await apiFetch('/api/feedback', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          feature: featureType,
          rating,
          comment,
          email: email || null, // Only send email if provided
        }),
      });
      
      const data = await response.json();
      
      if (data.success) {
        setIsSubmitted(true);
        trackEvent('Feedback Submitted', {
          feature: featureType,
          rating,
          comment: comment || null,
          email: email || null,
        });
      } else {
        setError(data.error || 'Terjadi kesalahan saat mengirim masukan');
        trackEvent('Feedback Submission Failed', {
          feature: featureType,
          error: data.error || 'unknown_error'
        });
      }
    } catch (err) {
      console.error('Error submitting feedback:', err);
      setError('Gagal mengirim masukan. Silakan coba lagi.');
      trackEvent('Feedback Submission Error', {
        feature: featureType,
        error: err instanceof Error ? err.message : 'Unknown error'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isSubmitted) {
    return (
      <div className="bg-white rounded-lg shadow-md p-4 sm:p-6 w-full max-w-[95%] sm:max-w-md mx-auto">
        <div className="text-center">
          <svg className="w-12 h-12 sm:w-16 sm:h-16 text-green-500 mx-auto mb-3 sm:mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
          <h3 className="text-lg font-semibold mb-2">Terima Kasih!</h3>
          <p className="text-gray-600 mb-4 text-sm sm:text-base">Masukan Anda sangat berharga. Kami akan berusaha sebaik mungkin untuk meningkatkan layanan Gigsta berdasarkan masukan Anda.</p>
          <button
            type="button"
            onClick={onClose}
            className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark transition-colors"
          >
            Tutup
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-md p-4 sm:p-6 w-full max-w-[95%] sm:max-w-md mx-auto">
      <div className="flex justify-between items-center mb-3 sm:mb-4">
        <h3 className="text-base sm:text-lg font-semibold">Beri Masukan Anda</h3>
        <button
          type="button"
          onClick={onClose}
          className="text-gray-400 hover:text-gray-500 p-2 -mr-2"
          aria-label="Close"
        >
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>
      
      <p className="text-gray-600 mb-3 sm:mb-4 text-sm sm:text-base">
        Kami memprioritaskan pengalaman pengguna dan terus berusaha meningkatkan layanan Gigsta. 
        Bagaimana pengalaman Anda menggunakan fitur Gigsta?
      </p>
      
      {error && (
        <div className="mb-4 p-2 bg-red-50 text-red-600 rounded-md text-sm">
          {error}
        </div>
      )}
      
      <form onSubmit={handleSubmit}>
        <div className="mb-3 sm:mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Rating
          </label>
          <div className="flex flex-col sm:flex-row sm:items-center">
            <div className="flex space-x-2 sm:space-x-3 mb-2 sm:mb-0">
              {[1, 2, 3, 4, 5].map((value) => (
                <button
                  key={value}
                  type="button"
                  onClick={() => setRating(value as FeedbackRating)}
                  className={`w-8 h-8 sm:w-10 sm:h-10 rounded-full flex items-center justify-center ${
                    rating === value ? 'bg-primary text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  } transition-colors`}
                >
                  {value}
                </button>
              ))}
            </div>
            <span className="text-sm text-gray-500 sm:ml-2 block sm:inline-block">
              {rating ? (
                rating === 1 ? 'Sangat Kurang Baik' : 
                rating === 2 ? 'Kurang Baik' : 
                rating === 3 ? 'Cukup' : 
                rating === 4 ? 'Baik' : 
                rating === 5 ? 'Sangat Baik' : 'Pilih Rating'
              ) : 'Pilih Rating'}
            </span>
          </div>
        </div>
        
        <div className="mb-3 sm:mb-4">
          <label htmlFor="comment" className="block text-sm font-medium text-gray-700 mb-1 sm:mb-2">
            Komentar (Opsional)
          </label>
          <textarea
            id="comment"
            rows={3}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary text-sm sm:text-base"
            placeholder="Bagikan saran atau komentar Anda..."
            value={comment}
            onChange={(e) => setComment(e.target.value)}
          />
        </div>
        
        <div className="mb-4 sm:mb-5">
          <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1 sm:mb-2">
            Email untuk Follow-up (Opsional)
          </label>
          <input
            type="email"
            id="email"
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary text-sm sm:text-base"
            placeholder="<EMAIL>"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
          />
          <p className="mt-1 text-xs text-gray-500">
            Kami hanya akan menghubungi Anda jika perlu informasi lebih lanjut tentang masukan Anda
          </p>
        </div>
        
        <div className="bg-gray-50 border border-gray-100 rounded-md p-3 mb-4 flex items-center">
          <svg className="w-4 h-4 mr-2 text-gray-500 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2h-1V9z" clipRule="evenodd"></path>
          </svg>
          <span className="text-xs text-gray-600">Anda juga dapat memberikan masukan kapan saja melalui tombol <span className="font-bold">Dukung Kami</span> di bagian bawah website.</span>
        </div>
        
        <div className="flex justify-end">
          <button
            type="submit"
            disabled={isSubmitting}
            className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark transition-colors disabled:opacity-50"
          >
            {isSubmitting ? 'Mengirim...' : 'Kirim Masukan'}
          </button>
        </div>
      </form>
    </div>
  );
}
