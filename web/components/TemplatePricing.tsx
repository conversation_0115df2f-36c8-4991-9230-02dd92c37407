import React from 'react';

interface TemplatePricingProps {
  tokenCost: number;
  size?: 'small' | 'medium' | 'large';
}

const TemplatePricing: React.FC<TemplatePricingProps> = ({
  tokenCost,
  size = 'medium'
}) => {
  const sizeClasses = {
    small: {
      container: 'text-xs',
      currentPrice: 'text-sm font-semibold'
    },
    medium: {
      container: 'text-sm',
      currentPrice: 'text-base font-bold'
    },
    large: {
      container: 'text-base',
      currentPrice: 'text-lg font-bold'
    }
  };

  const classes = sizeClasses[size];

  return (
    <div className={`flex items-center space-x-1 ${classes.container}`}>
      <svg className="w-4 h-4 text-amber-600" fill="currentColor" viewBox="0 0 24 24">
        <path d="M21 6.375C21 9.06739 16.9706 11.25 12 11.25C7.02944 11.25 3 9.06739 3 6.375C3 3.68261 7.02944 1.5 12 1.5C16.9706 1.5 21 3.68261 21 6.375Z" />
        <path d="M12 12.75C14.6852 12.75 17.1905 12.1637 19.0784 11.1411C19.7684 10.7673 20.4248 10.3043 20.9747 9.75674C20.9915 9.87831 21 10.0011 21 10.125C21 12.8174 16.9706 15 12 15C7.02944 15 3 12.8174 3 10.125C3 10.0011 3.00853 9.8783 3.02529 9.75674C3.57523 10.3043 4.23162 10.7673 4.92161 11.1411C6.80949 12.1637 9.31481 12.75 12 12.75Z" />
        <path d="M12 16.5C14.6852 16.5 17.1905 15.9137 19.0784 14.8911C19.7684 14.5173 20.4248 14.0543 20.9747 13.5067C20.9915 13.6283 21 13.7511 21 13.875C21 16.5674 16.9706 18.75 12 18.75C7.02944 18.75 3 16.5674 3 13.875C3 13.7511 3.00853 13.6283 3.02529 13.5067C3.57523 14.0543 4.23162 14.5173 4.92161 14.8911C6.80949 15.9137 9.31481 16.5 12 16.5Z" />
        <path d="M12 20.25C14.6852 20.25 17.1905 19.6637 19.0784 18.6411C19.7684 18.2673 20.4248 17.8043 20.9747 17.2567C20.9915 17.3783 21 17.5011 21 17.625C21 20.3174 16.9706 22.5 12 22.5C7.02944 22.5 3 20.3174 3 17.625C3 17.5011 3.00853 17.3783 3.02529 17.2567C3.57523 17.8043 4.23162 18.2673 4.92161 18.6411C6.80949 19.6637 9.31481 20.25 12 20.25Z" />
      </svg>
      <span className={`text-amber-600 ${classes.currentPrice}`}>{tokenCost}</span>
    </div>
  );
};

export default TemplatePricing;