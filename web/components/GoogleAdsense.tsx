'use client';

import { useEffect } from 'react';

export default function GoogleAdsense() {
  useEffect(() => {
    try {
      // Create the first script element (the main AdSense script)
      const adsbygoogleScript = document.createElement('script');
      adsbygoogleScript.src = `https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=${process.env.NEXT_PUBLIC_GOOGLE_ADS_CLIENT_ID}`;
      adsbygoogleScript.async = true;
      adsbygoogleScript.crossOrigin = 'anonymous';
      document.head.appendChild(adsbygoogleScript);
    } catch (error) {
      console.error('Error loading AdSense scripts:', error);
    }
  }, []);

  return null; // This component doesn't render anything
}