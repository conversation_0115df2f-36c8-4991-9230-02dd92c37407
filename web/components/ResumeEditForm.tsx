"use client";

import React, { useState, useEffect, useLayoutEffect, useCallback, useRef } from "react";
import { StructuredResumeData } from "@/types/resume-structured";

// Icon components
const ChevronDownIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
  </svg>
);

const ChevronUpIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 15l7-7 7 7" />
  </svg>
);

const PlusIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
  </svg>
);

const TrashIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
  </svg>
);

const GripVerticalIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 9l3 3-3 3m5 0h3M5 20h14a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
  </svg>
);

const SparklesIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09zM18.259 8.715L18 9.75l-.259-1.035a3.375 3.375 0 00-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 002.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 002.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 00-2.456 2.456zM16.894 20.567L16.5 21.75l-.394-1.183a2.25 2.25 0 00-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 001.423-1.423L16.5 15.75l.394 1.183a2.25 2.25 0 001.423 1.423L19.5 18.75l-1.183.394a2.25 2.25 0 00-1.423 1.423z" />
  </svg>
);

const MoreVerticalIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
  </svg>
);

const InfoIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
  </svg>
);

const XMarkIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
  </svg>
);

interface ResumeEditFormProps {
  data: StructuredResumeData;
  onChange: (data: StructuredResumeData) => void;
  onAutoSave?: (data: StructuredResumeData) => void;
  autoSaveDelay?: number;
}

interface ValidationErrors {
  [key: string]: string | undefined;
}

interface AIAssistantState {
  isOpen: boolean;
  suggestions: string[];
  isLoading: boolean;
  fieldKey: string;
}

interface AIAssistantProps {
  onSuggestionSelect: (suggestion: string) => void;
  fieldKey: string;
  currentValue: string;
  fieldType: 'text' | 'textarea' | 'email' | 'phone' | 'url';
  placeholder?: string;
}

interface CollapsibleSectionProps {
  title: string;
  children: React.ReactNode;
  defaultExpanded?: boolean;
  badge?: string;
}

const CollapsibleSection: React.FC<CollapsibleSectionProps> = ({
  title,
  children,
  defaultExpanded = false,
  badge
}) => {
  const [isExpanded, setIsExpanded] = useState(defaultExpanded);

  return (
    <div className="mb-8 rounded-xl overflow-hidden shadow-sm border border-gray-100 bg-white">
      <button
        onClick={() => setIsExpanded(!isExpanded)}
        className={`w-full px-3 sm:px-6 py-3 sm:py-4 flex items-center justify-between text-left transition-all duration-200 ${
          isExpanded
            ? 'bg-gradient-to-r from-blue-50 via-indigo-50 to-purple-50 shadow-inner'
            : 'bg-gradient-to-r from-gray-50 to-slate-50 hover:from-blue-50 hover:via-indigo-50 hover:to-purple-50'
        }`}
      >
        <div className="flex items-center gap-3">
          <h3 className={`font-bold text-xl transition-colors duration-200 ${
            isExpanded ? 'text-indigo-800' : 'text-gray-800 group-hover:text-indigo-700'
          }`}>{title}</h3>
          {badge && (
            <span className={`px-3 py-1.5 text-xs font-semibold rounded-full min-w-[2rem] text-center transition-all duration-200 ${
              isExpanded
                ? 'bg-indigo-100 text-indigo-800 shadow-sm'
                : 'bg-blue-100 text-blue-700 hover:bg-indigo-100 hover:text-indigo-800'
            }`}>
              {badge}
            </span>
          )}
        </div>
        <div className={`transition-all duration-200 ${
          isExpanded ? 'rotate-180 text-indigo-600' : 'text-gray-500 hover:text-indigo-600'
        }`}>
          <ChevronDownIcon className="w-5 h-5 flex-shrink-0" />
        </div>
      </button>
      {isExpanded && (
        <div className="px-3 sm:px-6 py-4 sm:py-5 bg-gradient-to-br from-white via-gray-50/30 to-blue-50/20 border-t border-gray-100">
          {children}
        </div>
      )}
    </div>
  );
};

const AIAssistantButton: React.FC<{ onClick: () => void; isLoading: boolean }> = ({ onClick, isLoading }) => (
  <button
    type="button"
    onClick={onClick}
    disabled={isLoading}
    className="ml-2 p-1.5 text-purple-600 hover:text-purple-700 hover:bg-purple-50 rounded-lg transition-colors disabled:opacity-50"
    title="AI Writing Assistant"
  >
    {isLoading ? (
      <div className="w-4 h-4 animate-spin rounded-full border-2 border-purple-600 border-t-transparent" />
    ) : (
      <SparklesIcon className="w-4 h-4" />
    )}
  </button>
);

const AISuggestionsDropdown: React.FC<{
  suggestions: string[];
  onSelect: (suggestion: string) => void;
  onClose: () => void;
  isLoading: boolean;
  childrenRef?: React.RefObject<HTMLElement>;
}> = ({ suggestions, onSelect, onClose, isLoading, childrenRef }) => {
  const dropdownRef = useRef<HTMLDivElement>(null);
  const [position, setPosition] = useState<{ top: number; left: number; width: number }>({ top: 0, left: 0, width: 0 });

  // Calculate dropdown position relative to the viewport based on the triggering element (textarea / input)
  useLayoutEffect(() => {
    const calculatePosition = () => {
      const triggerEl = childrenRef?.current ?? dropdownRef.current?.parentElement;
      if (!triggerEl) return;
      const rect = triggerEl.getBoundingClientRect();
      setPosition({
        top: rect.bottom + 4, // 4px gap
        left: rect.left,
        width: rect.width,
      });
    };
    calculatePosition();
    window.addEventListener('resize', calculatePosition);
    window.addEventListener('scroll', calculatePosition, true);
    return () => {
      window.removeEventListener('resize', calculatePosition);
      window.removeEventListener('scroll', calculatePosition, true);
    };
  }, [childrenRef]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [onClose]);

  return (
    <div
      ref={dropdownRef}
      style={{ position: 'fixed', top: position.top, left: position.left, width: position.width, zIndex: 9999 }}
      className="mt-1 bg-white border border-gray-200 rounded-lg shadow-lg max-h-60 overflow-y-auto"
    >
      {isLoading ? (
        <div className="p-4 text-center">
          <div className="inline-flex items-center gap-2 text-purple-600">
            <div className="w-4 h-4 animate-spin rounded-full border-2 border-purple-600 border-t-transparent" />
            <span className="text-sm">Membuat saran...</span>
          </div>
        </div>
      ) : suggestions.length > 0 ? (
        <div className="py-2">
          <div className="flex items-center gap-2 px-3 py-1 text-xs font-semibold text-purple-700 bg-purple-50 border-b border-purple-100">
            <SparklesIcon className="w-3 h-3" />
            Saran AI
          </div>
          {suggestions.map((suggestion, index) => (
            <button
              key={index}
              onClick={() => onSelect(suggestion)}
              className="w-full px-3 py-2 text-left text-sm hover:bg-purple-50 hover:text-purple-700 transition-colors border-b border-gray-100 last:border-b-0"
            >
              {suggestion}
            </button>
          ))}
        </div>
      ) : (
        <div className="p-4 text-center text-sm text-gray-500">
          Tidak ada saran
        </div>
      )}
    </div>
  );
};

// Responsibility dropdown component for more actions
const ResponsibilityDropdown: React.FC<{
  onAIAssist: () => void;
  onDelete: () => void;
  onClose: () => void;
  position: { top: number; left: number };
}> = ({ onAIAssist, onDelete, onClose, position }) => {
  const dropdownRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [onClose]);

  return (
    <div
      ref={dropdownRef}
      style={{ 
        position: 'fixed', 
        top: position.top, 
        left: position.left, 
        zIndex: 9999 
      }}
      className="bg-white border border-gray-200 rounded-lg shadow-lg py-1 min-w-[150px]"
    >
      <button
        onClick={onAIAssist}
        className="w-full px-3 py-2 text-left text-sm hover:bg-purple-50 hover:text-purple-700 transition-colors flex items-center gap-2"
      >
        <SparklesIcon className="w-4 h-4" />
        Saran AI
      </button>
      <button
        onClick={onDelete}
        className="w-full px-3 py-2 text-left text-sm hover:bg-red-50 hover:text-red-700 transition-colors flex items-center gap-2"
      >
        <TrashIcon className="w-4 h-4" />
        Hapus
      </button>
    </div>
  );
};

const AIPromoBanner: React.FC = () => (
  <div className="mb-4 bg-gradient-to-r from-purple-50 to-blue-50 border border-purple-200 rounded-lg p-3 shadow-sm">
    <div className="flex items-center gap-2">
      <div className="flex-shrink-0 p-1.5 bg-purple-100 rounded-lg">
        <SparklesIcon className="w-5 h-5 text-purple-600" />
      </div>
      <div className="flex-1">
        <h3 className="text-base font-semibold text-gray-900 mb-1">
          ✨ AI Writing Assistant
        </h3>
        <p className="text-gray-600 text-sm leading-snug">
          Cari ikon <SparklesIcon className="w-3 h-3 inline text-purple-600 mx-1" />
          untuk saran konten pintar yang disesuaikan dengan CV Anda
        </p>
      </div>
    </div>
  </div>
);

const ResumeEditForm: React.FC<ResumeEditFormProps> = ({
  data,
  onChange,
  onAutoSave,
  autoSaveDelay = 1000
}) => {
  const [errors, setErrors] = useState<ValidationErrors>({});
  const [autoSaveTimer, setAutoSaveTimer] = useState<NodeJS.Timeout | null>(null);
  const [editingValues, setEditingValues] = useState<{[key: string]: string}>({});
  const [aiAssistant, setAiAssistant] = useState<AIAssistantState>({
    isOpen: false,
    suggestions: [],
    isLoading: false,
    fieldKey: ''
  });
  const [openResponsibilityDropdown, setOpenResponsibilityDropdown] = useState<string | null>(null);

  // Auto-save functionality
  const triggerAutoSave = useCallback((updatedData: StructuredResumeData) => {
    if (autoSaveTimer) {
      clearTimeout(autoSaveTimer);
    }

    const timer = setTimeout(() => {
      onAutoSave?.(updatedData);
    }, autoSaveDelay);

    setAutoSaveTimer(timer);
  }, [autoSaveTimer, onAutoSave, autoSaveDelay]);

  const updateData = useCallback((updatedData: StructuredResumeData) => {
    onChange(updatedData);
    triggerAutoSave(updatedData);
  }, [onChange, triggerAutoSave]);

  // AI Assistant functions
  const generateAISuggestions = async (fieldKey: string, currentValue: string, fieldType: string): Promise<string[]> => {
    try {
      const context = {
        fieldType,
        currentValue,
        personalInfo: data.personalInfo,
        targetPosition: data.targetPosition,
        professionalSummary: data.professionalSummary,
        skills: data.skills,
        experiences: data.experiences,
        education: data.education,
        projects: data.projects,
        certifications: data.certifications,
        languages: data.languages,
        awards: data.awards
      };

      const response = await fetch('/api/ai/generate-suggestions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          fieldKey,
          currentValue,
          context,
          fieldType
        })
      });

      if (!response.ok) {
        throw new Error('Failed to generate AI suggestions');
      }

      const result = await response.json();
      return result.suggestions || [];
    } catch (error) {
      console.error('Error generating AI suggestions:', error);
      return [];
    }
  };

  const handleAIAssistantClick = async (fieldKey: string, currentValue: string, fieldType: string) => {
    setAiAssistant({
      isOpen: true,
      suggestions: [],
      isLoading: true,
      fieldKey
    });

    const suggestions = await generateAISuggestions(fieldKey, currentValue, fieldType);
    
    setAiAssistant(prev => ({
      ...prev,
      suggestions,
      isLoading: false
    }));
  };

  const handleAISuggestionSelect = (suggestion: string, fieldKey: string) => {
    // Parse field key to determine what to update
    if (fieldKey.startsWith('personalInfo.')) {
      const field = fieldKey.replace('personalInfo.', '') as keyof StructuredResumeData['personalInfo'];
      updatePersonalInfo(field, suggestion);
    } else if (fieldKey === 'targetPosition') {
      updateTargetPosition(suggestion);
    } else if (fieldKey === 'professionalSummary') {
      updateProfessionalSummary(suggestion);
    } else if (fieldKey.startsWith('experience.')) {
      const parts = fieldKey.split('.');
      const expIndex = parseInt(parts[1]);
      if (parts[2] === 'responsibilities') {
        const respIndex = parseInt(parts[3]);
        updateExperienceResponsibility(expIndex, respIndex, suggestion);
      } else {
        const field = parts[2] as keyof NonNullable<StructuredResumeData['experiences']>[0];
        updateExperience(expIndex, field, suggestion);
      }
    } else if (fieldKey.startsWith('skills.')) {
      const parts = fieldKey.split('.');
      const skillIndex = parseInt(parts[1]);
      if (parts[2] === 'category') {
        updateSkillCategory(skillIndex, 'category', suggestion);
      } else if (parts[2] === 'skills') {
        updateSkillCategory(skillIndex, 'skills', stringToArray(suggestion));
      }
    } else if (fieldKey.startsWith('projects.')) {
      const parts = fieldKey.split('.');
      const projectIndex = parseInt(parts[1]);
      if (parts[2] === 'description') {
        updateProject(projectIndex, 'description', suggestion);
      } else if (parts[2] === 'achievements') {
        updateProject(projectIndex, 'achievements', stringToArray(suggestion));
      }
    } else if (fieldKey.startsWith('awards.')) {
      const parts = fieldKey.split('.');
      const awardIndex = parseInt(parts[1]);
      if (parts[2] === 'description') {
        updateAward(awardIndex, 'description', suggestion);
      }
    }
    
    // Close AI assistant
    setAiAssistant({
      isOpen: false,
      suggestions: [],
      isLoading: false,
      fieldKey: ''
    });
  };

  const closeAIAssistant = () => {
    setAiAssistant({
      isOpen: false,
      suggestions: [],
      isLoading: false,
      fieldKey: ''
    });
  };

  // Responsibility dropdown functions
  const [dropdownPosition, setDropdownPosition] = useState<{ top: number; left: number }>({ top: 0, left: 0 });
  
  const handleResponsibilityMoreClick = (event: React.MouseEvent, fieldKey: string) => {
    event.preventDefault();
    const rect = (event.target as HTMLElement).getBoundingClientRect();
    setDropdownPosition({
      top: rect.bottom + 4,
      left: rect.right - 150 // Align to right edge of button
    });
    setOpenResponsibilityDropdown(fieldKey);
  };

  const closeResponsibilityDropdown = () => {
    setOpenResponsibilityDropdown(null);
  };

  // Close dropdown on scroll
  useEffect(() => {
    const handleScroll = () => {
      if (openResponsibilityDropdown) {
        closeResponsibilityDropdown();
      }
    };

    if (openResponsibilityDropdown) {
      window.addEventListener('scroll', handleScroll, true);
      return () => {
        window.removeEventListener('scroll', handleScroll, true);
      };
    }
  }, [openResponsibilityDropdown]);

  const handleResponsibilityAI = (fieldKey: string, currentValue: string) => {
    closeResponsibilityDropdown();
    handleAIAssistantClick(fieldKey, currentValue, 'responsibility');
  };

  const handleResponsibilityDelete = (expIndex: number, respIndex: number) => {
    closeResponsibilityDropdown();
    removeExperienceResponsibility(expIndex, respIndex);
  };

  // Validation (no fields are required anymore)
  const validateField = (_field: string, _value: any): string | undefined => {
    // No validation needed since all fields are optional now
    return undefined;
  };

  const updatePersonalInfo = (field: keyof StructuredResumeData['personalInfo'], value: string) => {
    const updatedData = {
      ...data,
      personalInfo: {
        ...data.personalInfo,
        [field]: value
      }
    };
    
    // Validate field
    const error = validateField(`personalInfo.${field}`, value);
    setErrors(prev => {
      const newErrors = { ...prev };
      if (error) {
        newErrors[`personalInfo.${field}`] = error;
      } else {
        delete newErrors[`personalInfo.${field}`];
      }
      return newErrors;
    });
    
    updateData(updatedData);
  };

  const updateTargetPosition = (value: string) => {
    const updatedData = {
      ...data,
      targetPosition: value
    };
    
    const error = validateField('targetPosition', value);
    setErrors(prev => {
      const newErrors = { ...prev };
      if (error) {
        newErrors.targetPosition = error;
      } else {
        delete newErrors.targetPosition;
      }
      return newErrors;
    });
    
    updateData(updatedData);
  };

  const updateProfessionalSummary = (value: string) => {
    const updatedData = {
      ...data,
      professionalSummary: value
    };
    updateData(updatedData);
  };

  // Experience handlers
  const addExperience = () => {
    const newExperience = {
      id: Date.now().toString(),
      jobTitle: '',
      company: '',
      location: '',
      startDate: '',
      endDate: '',
      responsibilities: ['']
    };
    
    const updatedData = {
      ...data,
      experiences: [...(data.experiences || []), newExperience]
    };
    updateData(updatedData);
  };

  const updateExperience = (index: number, field: keyof NonNullable<StructuredResumeData['experiences']>[0], value: string) => {
    const updatedExperiences = [...(data.experiences || [])];
    updatedExperiences[index] = {
      ...updatedExperiences[index],
      [field]: value
    };
    
    const updatedData = {
      ...data,
      experiences: updatedExperiences
    };
    updateData(updatedData);
  };

  const updateExperienceResponsibility = (expIndex: number, respIndex: number, value: string) => {
    const updatedExperiences = [...(data.experiences || [])];
    const responsibilities = [...updatedExperiences[expIndex].responsibilities];
    responsibilities[respIndex] = value;
    updatedExperiences[expIndex] = {
      ...updatedExperiences[expIndex],
      responsibilities
    };
    
    const updatedData = {
      ...data,
      experiences: updatedExperiences
    };
    updateData(updatedData);
  };

  const addExperienceResponsibility = (expIndex: number) => {
    const updatedExperiences = [...(data.experiences || [])];
    updatedExperiences[expIndex] = {
      ...updatedExperiences[expIndex],
      responsibilities: [...updatedExperiences[expIndex].responsibilities, '']
    };
    
    const updatedData = {
      ...data,
      experiences: updatedExperiences
    };
    updateData(updatedData);
  };

  const removeExperienceResponsibility = (expIndex: number, respIndex: number) => {
    const updatedExperiences = [...(data.experiences || [])];

    const responsibilities = [...updatedExperiences[expIndex].responsibilities];

    if (responsibilities.length === 1) {
      // Only one responsibility left – clear its content instead of removing the array item
      responsibilities[0] = '';
    } else {
      // Remove the selected responsibility item
      responsibilities.splice(respIndex, 1);
    }

    updatedExperiences[expIndex] = {
      ...updatedExperiences[expIndex],
      responsibilities,
    };

    const updatedData = {
      ...data,
      experiences: updatedExperiences,
    };
    updateData(updatedData);
  };

  const removeExperience = (index: number) => {
    const updatedExperiences = [...(data.experiences || [])];
    updatedExperiences.splice(index, 1);
    
    const updatedData = {
      ...data,
      experiences: updatedExperiences
    };
    updateData(updatedData);
  };

  // Education handlers
  const addEducation = () => {
    const newEducation = {
      id: Date.now().toString(),
      degree: '',
      institution: '',
      location: '',
      graduationDate: '',
      gpa: '',
      relevantCoursework: [],
      honors: []
    };
    
    const updatedData = {
      ...data,
      education: [...(data.education || []), newEducation]
    };
    updateData(updatedData);
  };

  const updateEducation = (index: number, field: keyof NonNullable<StructuredResumeData['education']>[0], value: string | string[]) => {
    const updatedEducation = [...(data.education || [])];
    updatedEducation[index] = {
      ...updatedEducation[index],
      [field]: value
    };
    
    const updatedData = {
      ...data,
      education: updatedEducation
    };
    updateData(updatedData);
  };

  const removeEducation = (index: number) => {
    const updatedEducation = [...(data.education || [])];
    updatedEducation.splice(index, 1);
    
    const updatedData = {
      ...data,
      education: updatedEducation
    };
    updateData(updatedData);
  };

  // Skills handlers
  const addSkillCategory = () => {
    const newCategory = {
      category: '',
      skills: ['']
    };
    
    const updatedData = {
      ...data,
      skills: {
        ...data.skills,
        categories: [...(data.skills?.categories || []), newCategory]
      }
    };
    updateData(updatedData);
  };

  const updateSkillCategory = (index: number, field: 'category' | 'skills', value: string | string[]) => {
    const updatedCategories = [...(data.skills?.categories || [])];
    updatedCategories[index] = {
      ...updatedCategories[index],
      [field]: value
    };
    
    const updatedData = {
      ...data,
      skills: {
        ...data.skills,
        categories: updatedCategories
      }
    };
    updateData(updatedData);
  };

  const removeSkillCategory = (index: number) => {
    const updatedCategories = [...(data.skills?.categories || [])];
    updatedCategories.splice(index, 1);
    
    const updatedData = {
      ...data,
      skills: {
        ...data.skills,
        categories: updatedCategories
      }
    };
    updateData(updatedData);
  };

  // Optional sections handlers
  const addCertification = () => {
    const newCertification = {
      id: Date.now().toString(),
      name: '',
      issuer: '',
      date: '',
      credentialId: ''
    };
    
    const updatedData = {
      ...data,
      certifications: [...(data.certifications || []), newCertification]
    };
    updateData(updatedData);
  };

  const updateCertification = (index: number, field: keyof NonNullable<StructuredResumeData['certifications']>[0], value: string) => {
    const updatedCertifications = [...(data.certifications || [])];
    updatedCertifications[index] = {
      ...updatedCertifications[index],
      [field]: value
    };
    
    const updatedData = {
      ...data,
      certifications: updatedCertifications
    };
    updateData(updatedData);
  };

  const removeCertification = (index: number) => {
    const updatedCertifications = [...(data.certifications || [])];
    updatedCertifications.splice(index, 1);
    
    const updatedData = {
      ...data,
      certifications: updatedCertifications
    };
    updateData(updatedData);
  };

  const addProject = () => {
    const newProject = {
      id: Date.now().toString(),
      title: '',
      description: '',
      technologies: [''],
      link: '',
      achievements: ['']
    };
    
    const updatedData = {
      ...data,
      projects: [...(data.projects || []), newProject]
    };
    updateData(updatedData);
  };

  const updateProject = (index: number, field: keyof NonNullable<StructuredResumeData['projects']>[0], value: string | string[]) => {
    const updatedProjects = [...(data.projects || [])];
    updatedProjects[index] = {
      ...updatedProjects[index],
      [field]: value
    };
    
    const updatedData = {
      ...data,
      projects: updatedProjects
    };
    updateData(updatedData);
  };

  const removeProject = (index: number) => {
    const updatedProjects = [...(data.projects || [])];
    updatedProjects.splice(index, 1);
    
    const updatedData = {
      ...data,
      projects: updatedProjects
    };
    updateData(updatedData);
  };

  const addLanguage = () => {
    const newLanguage = {
      language: '',
      proficiency: ''
    };
    
    const updatedData = {
      ...data,
      languages: [...(data.languages || []), newLanguage]
    };
    updateData(updatedData);
  };

  const updateLanguage = (index: number, field: keyof NonNullable<StructuredResumeData['languages']>[0], value: string) => {
    const updatedLanguages = [...(data.languages || [])];
    updatedLanguages[index] = {
      ...updatedLanguages[index],
      [field]: value
    };
    
    const updatedData = {
      ...data,
      languages: updatedLanguages
    };
    updateData(updatedData);
  };

  const removeLanguage = (index: number) => {
    const updatedLanguages = [...(data.languages || [])];
    updatedLanguages.splice(index, 1);
    
    const updatedData = {
      ...data,
      languages: updatedLanguages
    };
    updateData(updatedData);
  };

  const addAward = () => {
    const newAward = {
      id: Date.now().toString(),
      title: '',
      issuer: '',
      date: '',
      description: ''
    };
    
    const updatedData = {
      ...data,
      awards: [...(data.awards || []), newAward]
    };
    updateData(updatedData);
  };

  const updateAward = (index: number, field: keyof NonNullable<StructuredResumeData['awards']>[0], value: string) => {
    const updatedAwards = [...(data.awards || [])];
    updatedAwards[index] = {
      ...updatedAwards[index],
      [field]: value
    };
    
    const updatedData = {
      ...data,
      awards: updatedAwards
    };
    updateData(updatedData);
  };

  const removeAward = (index: number) => {
    const updatedAwards = [...(data.awards || [])];
    updatedAwards.splice(index, 1);
    
    const updatedData = {
      ...data,
      awards: updatedAwards
    };
    updateData(updatedData);
  };

  // Helper to convert comma-separated string to array
  const stringToArray = (str: string): string[] => {
    return str.split(',').map(item => item.trim()).filter(item => item.length > 0);
  };

  const arrayToString = (arr: string[] | undefined): string => {
    return (arr || []).join(', ');
  };

  return (
    <div className="space-y-6">
      {/* AI Promo Banner */}
      <AIPromoBanner />

      {/* Personal Information */}
      <CollapsibleSection title="Informasi Pribadi" defaultExpanded={true}>
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="flex flex-col">
              <div className="flex items-center justify-between mb-1.5">
                <label className="block text-sm font-medium text-gray-700">
                  Nama Lengkap
                </label>
              </div>
              <div className="relative">
                <input
                  type="text"
                  value={data.personalInfo.fullName}
                  onChange={(e) => updatePersonalInfo('fullName', e.target.value)}
                  className={`w-full h-10 px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                    errors['personalInfo.fullName'] ? 'border-red-500' : 'border-gray-300'
                  }`}
                  placeholder="Masukkan nama lengkap Anda"
                />
              </div>
              {errors['personalInfo.fullName'] && (
                <p className="mt-1.5 text-xs text-red-600 min-h-[16px]">{errors['personalInfo.fullName']}</p>
              )}
            </div>
            
            <div className="flex flex-col">
              <div className="flex items-center justify-between mb-1.5">
                <label className="block text-sm font-medium text-gray-700">
                  Email
                </label>
              </div>
              <div className="relative">
                <input
                  type="email"
                  value={data.personalInfo.email}
                  onChange={(e) => updatePersonalInfo('email', e.target.value)}
                  className={`w-full h-10 px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                    errors['personalInfo.email'] ? 'border-red-500' : 'border-gray-300'
                  }`}
                  placeholder="<EMAIL>"
                />
              </div>
              {errors['personalInfo.email'] && (
                <p className="mt-1.5 text-xs text-red-600 min-h-[16px]">{errors['personalInfo.email']}</p>
              )}
            </div>
            
            <div className="flex flex-col">
              <div className="flex items-center justify-between mb-1.5">
                <label className="block text-sm font-medium text-gray-700">
                  Nomor Telepon
                </label>
              </div>
              <div className="relative">
                <input
                  type="tel"
                  value={data.personalInfo.phone}
                  onChange={(e) => updatePersonalInfo('phone', e.target.value)}
                  className={`w-full h-10 px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                    errors['personalInfo.phone'] ? 'border-red-500' : 'border-gray-300'
                  }`}
                  placeholder="+62 812 3456 7890"
                />
              </div>
              {errors['personalInfo.phone'] && (
                <p className="mt-1.5 text-xs text-red-600 min-h-[16px]">{errors['personalInfo.phone']}</p>
              )}
            </div>
            
            <div className="flex flex-col">
              <div className="flex items-center justify-between mb-1.5">
                <label className="block text-sm font-medium text-gray-700">
                  Lokasi
                </label>
              </div>
              <div className="relative">
                <input
                  type="text"
                  value={data.personalInfo.location}
                  onChange={(e) => updatePersonalInfo('location', e.target.value)}
                  className="w-full h-10 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                  placeholder="Kota, Provinsi/Negara"
                />
              </div>
              <div className="min-h-[16px] mt-1.5"></div>
            </div>
            
            <div className="flex flex-col">
              <label className="block text-sm font-medium text-gray-700 mb-1.5">
                Profil LinkedIn
              </label>
              <input
                type="url"
                value={data.personalInfo.linkedin || ''}
                onChange={(e) => updatePersonalInfo('linkedin', e.target.value)}
                className="w-full h-10 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                placeholder="https://linkedin.com/in/profilanda"
              />
              <div className="min-h-[16px] mt-1.5"></div>
            </div>
            
            <div className="flex flex-col">
              <label className="block text-sm font-medium text-gray-700 mb-1.5">
                Website
              </label>
              <input
                type="url"
                value={data.personalInfo.website || ''}
                onChange={(e) => updatePersonalInfo('website', e.target.value)}
                className="w-full h-10 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                placeholder="https://websiteanda.com"
              />
              <div className="min-h-[16px] mt-1.5"></div>
            </div>
            
            <div className="flex flex-col md:col-span-1">
              <label className="block text-sm font-medium text-gray-700 mb-1.5">
                Profil GitHub
              </label>
              <input
                type="url"
                value={data.personalInfo.github || ''}
                onChange={(e) => updatePersonalInfo('github', e.target.value)}
                className="w-full h-10 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                placeholder="https://github.com/namaanda"
              />
              <div className="min-h-[16px] mt-1.5"></div>
            </div>
          </div>
        </div>
      </CollapsibleSection>

      {/* Target Position */}
      <CollapsibleSection title="Posisi Target" defaultExpanded={true}>
        <div>
          <div>
            <div className="flex items-center justify-between mb-1">
              <label className="block text-sm font-medium text-gray-700">
                Posisi Pekerjaan yang Diinginkan
              </label>
              {/* <AIAssistantButton
                onClick={() => handleAIAssistantClick('targetPosition', data.targetPosition, 'text')}
                isLoading={aiAssistant.isLoading && aiAssistant.fieldKey === 'targetPosition'}
              /> */}
            </div>
            <div className="relative">
              <input
                type="text"
                value={data.targetPosition}
                onChange={(e) => updateTargetPosition(e.target.value)}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                  errors.targetPosition ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="mis., Senior Software Engineer, Marketing Manager"
              />
              {aiAssistant.isOpen && aiAssistant.fieldKey === 'targetPosition' && (
                <AISuggestionsDropdown
                  suggestions={aiAssistant.suggestions}
                  onSelect={(suggestion) => handleAISuggestionSelect(suggestion, 'targetPosition')}
                  onClose={closeAIAssistant}
                  isLoading={aiAssistant.isLoading}
                />
              )}
            </div>
            {errors.targetPosition && (
              <p className="mt-1 text-xs text-red-600">{errors.targetPosition}</p>
            )}
          </div>
        </div>
      </CollapsibleSection>

      {/* Professional Summary */}
      <CollapsibleSection title="Ringkasan Profesional" defaultExpanded={true}>
        <div>
          <div>
            <div className="flex items-center justify-between mb-1">
              <label className="block text-sm font-medium text-gray-700">
                Ringkasan Profesional
              </label>
              <AIAssistantButton
                onClick={() => handleAIAssistantClick('professionalSummary', data.professionalSummary || '', 'textarea')}
                isLoading={aiAssistant.isLoading && aiAssistant.fieldKey === 'professionalSummary'}
              />
            </div>
            <div className="relative">
              <textarea
                value={data.professionalSummary}
                onChange={(e) => updateProfessionalSummary(e.target.value)}
                rows={4}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Tulis ringkasan singkat tentang latar belakang profesional, keahlian utama, dan tujuan karir Anda..."
              />
              {aiAssistant.isOpen && aiAssistant.fieldKey === 'professionalSummary' && (
                <AISuggestionsDropdown
                  suggestions={aiAssistant.suggestions}
                  onSelect={(suggestion) => handleAISuggestionSelect(suggestion, 'professionalSummary')}
                  onClose={closeAIAssistant}
                  isLoading={aiAssistant.isLoading}
                />
              )}
            </div>
            <p className="mt-1 text-xs text-gray-500">
              2-3 kalimat yang menyoroti keahlian dan tujuan karir Anda
            </p>
          </div>
        </div>
      </CollapsibleSection>

      {/* Work Experience */}
      <CollapsibleSection 
        title="Pengalaman Kerja" 
        defaultExpanded={true}
        badge={(data.experiences || []).length.toString()}
      >
        <div className="space-y-6">
          {(data.experiences || []).map((experience, index) => (
            <div key={experience.id} className="bg-gradient-to-br from-white via-gray-50/50 to-blue-50/30 rounded-xl p-3 sm:p-5 shadow-sm border border-gray-100 hover:shadow-md transition-all duration-200">
              <div className="flex justify-between items-center mb-4">
                <h4 className="font-medium text-gray-900">Pengalaman {index + 1}</h4>
                <button
                  onClick={() => removeExperience(index)}
                  className="text-red-500 hover:text-red-700 p-2 rounded-lg hover:bg-red-50 transition-colors"
                >
                  <TrashIcon className="w-4 h-4" />
                </button>
              </div>
              
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 mb-4">
                <div>
                  <div className="flex items-center justify-between mb-1">
                    <label className="block text-sm font-medium text-gray-700">
                      Posisi Pekerjaan
                    </label>
                    {/* <AIAssistantButton
                      onClick={() => handleAIAssistantClick(`experience.${index}.jobTitle`, experience.jobTitle, 'text')}
                      isLoading={aiAssistant.isLoading && aiAssistant.fieldKey === `experience.${index}.jobTitle`}
                    /> */}
                  </div>
                  <div className="relative">
                    <input
                      type="text"
                      value={experience.jobTitle}
                      onChange={(e) => updateExperience(index, 'jobTitle', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="mis., Senior Software Engineer"
                    />
                    {aiAssistant.isOpen && aiAssistant.fieldKey === `experience.${index}.jobTitle` && (
                      <AISuggestionsDropdown
                        suggestions={aiAssistant.suggestions}
                        onSelect={(suggestion) => handleAISuggestionSelect(suggestion, `experience.${index}.jobTitle`)}
                        onClose={closeAIAssistant}
                        isLoading={aiAssistant.isLoading}
                      />
                    )}
                  </div>
                </div>
                
                <div>
                  <div className="flex items-center justify-between mb-1">
                    <label className="block text-sm font-medium text-gray-700">
                      Perusahaan
                    </label>
                  </div>
                  <div className="relative">
                    <input
                      type="text"
                      value={experience.company}
                      onChange={(e) => updateExperience(index, 'company', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="e.g., Tech Perusahaan Inc."
                    />
                  </div>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Lokasi
                  </label>
                  <input
                    type="text"
                    value={experience.location}
                    onChange={(e) => updateExperience(index, 'location', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="mis., Jakarta, Indonesia"
                  />
                </div>
                
                <div className="grid grid-cols-2 gap-2 items-end">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Tanggal Mulai
                    </label>
                    <input
                      type="text"
                      value={experience.startDate}
                      onChange={(e) => updateExperience(index, 'startDate', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="mis., Jan 2022"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Tanggal Berakhir
                    </label>
                    <input
                      type="text"
                      value={experience.endDate}
                      onChange={(e) => updateExperience(index, 'endDate', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="mis., Sekarang atau Des 2023"
                    />
                  </div>
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Tanggung Jawab Utama & Pencapaian
                </label>
                {experience.responsibilities.map((responsibility, respIndex) => {
                  const fieldKey = `experience.${index}.responsibilities.${respIndex}`;
                  return (
                    <div key={respIndex} className="mb-3">
                      <div className="flex gap-2">
                        <div className="flex-1 relative">
                          <textarea
                            value={responsibility}
                            onChange={(e) => updateExperienceResponsibility(index, respIndex, e.target.value)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 min-h-[60px] resize-y"
                            placeholder="Tanggung jawab atau pencapaian..."
                            rows={2}
                          />
                          {aiAssistant.isOpen && aiAssistant.fieldKey === fieldKey && (
                            <AISuggestionsDropdown
                              suggestions={aiAssistant.suggestions}
                              onSelect={(suggestion) => handleAISuggestionSelect(suggestion, fieldKey)}
                              onClose={closeAIAssistant}
                              isLoading={aiAssistant.isLoading}
                            />
                          )}
                        </div>
                        <div className="relative">
                          <button
                            onClick={(e) => handleResponsibilityMoreClick(e, fieldKey)}
                            className="flex-shrink-0 text-gray-500 hover:text-gray-700 p-2 rounded-lg hover:bg-gray-50 transition-colors"
                          >
                            <MoreVerticalIcon className="w-4 h-4" />
                          </button>
                          {openResponsibilityDropdown === fieldKey && (
                            <ResponsibilityDropdown
                              onAIAssist={() => handleResponsibilityAI(fieldKey, responsibility)}
                              onDelete={() => handleResponsibilityDelete(index, respIndex)}
                              onClose={closeResponsibilityDropdown}
                              position={dropdownPosition}
                            />
                          )}
                        </div>
                      </div>
                    </div>
                  );
                })}
                <button
                  onClick={() => addExperienceResponsibility(index)}
                  className="text-blue-600 hover:text-blue-700 text-sm flex items-center gap-1 px-3 py-2 rounded-lg hover:bg-blue-50 transition-all duration-200 font-medium"
                >
                  <PlusIcon className="w-4 h-4" />
                  Tambah tanggung jawab
                </button>
              </div>
            </div>
          ))}
          
          <button
            onClick={addExperience}
            className="w-full py-4 bg-gradient-to-r from-gray-50 to-blue-50 border-2 border-dashed border-blue-200 rounded-xl text-blue-600 hover:from-blue-50 hover:to-indigo-50 hover:border-blue-300 hover:text-blue-700 flex items-center justify-center gap-2 font-medium transition-all duration-200 shadow-sm hover:shadow-md"
          >
            <PlusIcon className="w-5 h-5" />
            Tambah Pengalaman Kerja
          </button>
        </div>
      </CollapsibleSection>

      {/* Education */}
      <CollapsibleSection 
        title="Pendidikan" 
        defaultExpanded={true}
        badge={(data.education || []).length.toString()}
      >
        <div className="space-y-6">
          {(data.education || []).map((education, index) => (
            <div key={education.id} className="bg-gray-50 rounded-lg p-3 sm:p-4">
              <div className="flex justify-between items-center mb-4">
                <h4 className="font-medium text-gray-900">Pendidikan {index + 1}</h4>
                <button
                  onClick={() => removeEducation(index)}
                  className="text-red-500 hover:text-red-700 p-2 rounded-lg hover:bg-red-50 transition-colors"
                >
                  <TrashIcon className="w-4 h-4" />
                </button>
              </div>
              
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Gelar
                  </label>
                  <input
                    type="text"
                    value={education.degree}
                    onChange={(e) => updateEducation(index, 'degree', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="mis., Sarjana Sains Komputer"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Institusi
                  </label>
                  <input
                    type="text"
                    value={education.institution}
                    onChange={(e) => updateEducation(index, 'institution', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="mis., Universitas Indonesia"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Lokasi
                  </label>
                  <input
                    type="text"
                    value={education.location || ''}
                    onChange={(e) => updateEducation(index, 'location', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="mis., Depok, Indonesia"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Tanggal Lulus
                  </label>
                  <input
                    type="text"
                    value={education.graduationDate}
                    onChange={(e) => updateEducation(index, 'graduationDate', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="mis., Mei 2020"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    IPK (Opsional)
                  </label>
                  <input
                    type="text"
                    value={education.gpa || ''}
                    onChange={(e) => updateEducation(index, 'gpa', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="mis., 3.8/4.0"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Mata Kuliah Relevan (Opsional)
                  </label>
                  <input
                    type="text"
                    value={editingValues[`education-${index}-relevantCoursework`] ?? arrayToString(education.relevantCoursework || [])}
                    onChange={(e) => {
                      setEditingValues(prev => ({ ...prev, [`education-${index}-relevantCoursework`]: e.target.value }));
                      updateEducation(index, 'relevantCoursework', stringToArray(e.target.value));
                    }}
                    onBlur={(e) => {
                      setEditingValues(prev => {
                        const newValues = { ...prev };
                        delete newValues[`education-${index}-relevantCoursework`];
                        return newValues;
                      });
                    }}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="mis., Struktur Data, Algoritma, Rekayasa Perangkat Lunak"
                  />
                  <p className="mt-1 text-xs text-gray-500">Pisahkan dengan koma</p>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Penghargaan (Opsional)
                  </label>
                  <input
                    type="text"
                    value={editingValues[`education-${index}-honors`] ?? arrayToString(education.honors || [])}
                    onChange={(e) => {
                      setEditingValues(prev => ({ ...prev, [`education-${index}-honors`]: e.target.value }));
                      updateEducation(index, 'honors', stringToArray(e.target.value));
                    }}
                    onBlur={(e) => {
                      setEditingValues(prev => {
                        const newValues = { ...prev };
                        delete newValues[`education-${index}-honors`];
                        return newValues;
                      });
                    }}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="mis., Magna Cum Laude, Daftar Dekan"
                  />
                  <p className="mt-1 text-xs text-gray-500">Pisahkan dengan koma</p>
                </div>
              </div>
            </div>
          ))}
          
          <button
            onClick={addEducation}
            className="w-full py-4 bg-gradient-to-r from-gray-50 to-blue-50 border-2 border-dashed border-blue-200 rounded-xl text-blue-600 hover:from-blue-50 hover:to-indigo-50 hover:border-blue-300 hover:text-blue-700 flex items-center justify-center gap-2 font-medium transition-all duration-200 shadow-sm hover:shadow-md"
          >
            <PlusIcon className="w-5 h-5" />
            Tambah Pendidikan
          </button>
        </div>
      </CollapsibleSection>

      {/* Skills */}
      <CollapsibleSection 
        title="Keahlian" 
        defaultExpanded={true}
        badge={(data.skills?.categories || []).length.toString()}
      >
        <div className="space-y-6">
          {(data.skills?.categories || []).map((category, index) => (
            <div key={index} className="bg-gradient-to-br from-white via-purple-50/40 to-pink-50/30 rounded-xl p-3 sm:p-5 shadow-sm border border-gray-100 hover:shadow-md transition-all duration-200">
              <div className="flex justify-between items-center mb-4">
                <h4 className="font-medium text-gray-900">Kategori Keahlian {index + 1}</h4>
                <button
                  onClick={() => removeSkillCategory(index)}
                  className="text-red-500 hover:text-red-700 p-2 rounded-lg hover:bg-red-50 transition-colors"
                >
                  <TrashIcon className="w-4 h-4" />
                </button>
              </div>
              
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
                <div>
                  <div className="flex items-center justify-between mb-1">
                    <label className="block text-sm font-medium text-gray-700">
                      Nama Kategori
                    </label>
                    <AIAssistantButton
                      onClick={() => handleAIAssistantClick(`skills.${index}.category`, category.category, 'text')}
                      isLoading={aiAssistant.isLoading && aiAssistant.fieldKey === `skills.${index}.category`}
                    />
                  </div>
                  <div className="relative">
                    <input
                      type="text"
                      value={category.category}
                      onChange={(e) => updateSkillCategory(index, 'category', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="mis., Bahasa Pemrograman, Alat, Framework"
                    />
                    {aiAssistant.isOpen && aiAssistant.fieldKey === `skills.${index}.category` && (
                      <AISuggestionsDropdown
                        suggestions={aiAssistant.suggestions}
                        onSelect={(suggestion) => handleAISuggestionSelect(suggestion, `skills.${index}.category`)}
                        onClose={closeAIAssistant}
                        isLoading={aiAssistant.isLoading}
                      />
                    )}
                  </div>
                </div>
                
                <div className="sm:col-span-1 lg:col-span-2">
                  <div className="flex items-center justify-between mb-1">
                    <label className="block text-sm font-medium text-gray-700">
                      Keahlian
                    </label>
                    <AIAssistantButton
                      onClick={() => handleAIAssistantClick(`skills.${index}.skills`, arrayToString(category.skills || []), 'text')}
                      isLoading={aiAssistant.isLoading && aiAssistant.fieldKey === `skills.${index}.skills`}
                    />
                  </div>
                  <div className="relative">
                    <input
                      type="text"
                      value={editingValues[`skills-${index}-skills`] ?? arrayToString(category.skills || [])}
                      onChange={(e) => {
                        setEditingValues(prev => ({ ...prev, [`skills-${index}-skills`]: e.target.value }));
                        updateSkillCategory(index, 'skills', stringToArray(e.target.value));
                      }}
                      onBlur={(e) => {
                        setEditingValues(prev => {
                          const newValues = { ...prev };
                          delete newValues[`skills-${index}-skills`];
                          return newValues;
                        });
                      }}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="mis., JavaScript, Python, React, Node.js"
                    />
                    {aiAssistant.isOpen && aiAssistant.fieldKey === `skills.${index}.skills` && (
                      <AISuggestionsDropdown
                        suggestions={aiAssistant.suggestions}
                        onSelect={(suggestion) => handleAISuggestionSelect(suggestion, `skills.${index}.skills`)}
                        onClose={closeAIAssistant}
                        isLoading={aiAssistant.isLoading}
                      />
                    )}
                  </div>
                  <p className="mt-1 text-xs text-gray-500">Pisahkan dengan koma</p>
                </div>
              </div>
            </div>
          ))}
          
          <button
            onClick={addSkillCategory}
            className="w-full py-4 bg-gradient-to-r from-gray-50 to-blue-50 border-2 border-dashed border-blue-200 rounded-xl text-blue-600 hover:from-blue-50 hover:to-indigo-50 hover:border-blue-300 hover:text-blue-700 flex items-center justify-center gap-2 font-medium transition-all duration-200 shadow-sm hover:shadow-md"
          >
            <PlusIcon className="w-5 h-5" />
            Tambah Kategori Keahlian
          </button>
        </div>
      </CollapsibleSection>

      {/* Certifications */}
      <CollapsibleSection 
        title="Sertifikasi" 
        badge={data.certifications?.length.toString() || '0'}
      >
        <div className="space-y-6">
          {(data.certifications || []).map((certification, index) => (
            <div key={certification.id} className="bg-gray-50 rounded-lg p-3 sm:p-4">
              <div className="flex justify-between items-center mb-4">
                <h4 className="font-medium text-gray-900">Sertifikasi {index + 1}</h4>
                <button
                  onClick={() => removeCertification(index)}
                  className="text-red-500 hover:text-red-700 p-2 rounded-lg hover:bg-red-50 transition-colors"
                >
                  <TrashIcon className="w-4 h-4" />
                </button>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Nama Sertifikasi
                  </label>
                  <input
                    type="text"
                    value={certification.name}
                    onChange={(e) => updateCertification(index, 'name', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="mis., AWS Certified Developer"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Organisasi Penerbit
                  </label>
                  <input
                    type="text"
                    value={certification.issuer}
                    onChange={(e) => updateCertification(index, 'issuer', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="mis., Amazon Web Services"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Tanggal Diperoleh
                  </label>
                  <input
                    type="text"
                    value={certification.date}
                    onChange={(e) => updateCertification(index, 'date', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="mis., Maret 2023"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    ID Kredensial (Opsional)
                  </label>
                  <input
                    type="text"
                    value={certification.credentialId || ''}
                    onChange={(e) => updateCertification(index, 'credentialId', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="mis., 12345ABC"
                  />
                </div>
              </div>
            </div>
          ))}
          
          <button
            onClick={addCertification}
            className="w-full py-4 bg-gradient-to-r from-gray-50 to-blue-50 border-2 border-dashed border-blue-200 rounded-xl text-blue-600 hover:from-blue-50 hover:to-indigo-50 hover:border-blue-300 hover:text-blue-700 flex items-center justify-center gap-2 font-medium transition-all duration-200 shadow-sm hover:shadow-md"
          >
            <PlusIcon className="w-5 h-5" />
            Tambah Sertifikasi
          </button>
        </div>
      </CollapsibleSection>

      {/* Projects */}
      <CollapsibleSection 
        title="Proyek" 
        badge={data.projects?.length.toString() || '0'}
      >
        <div className="space-y-6">
          {(data.projects || []).map((project, index) => (
            <div key={project.id} className="bg-gradient-to-br from-white via-cyan-50/40 to-blue-50/30 rounded-xl p-3 sm:p-5 shadow-sm border border-gray-100 hover:shadow-md transition-all duration-200">
              <div className="flex justify-between items-center mb-4">
                <h4 className="font-medium text-gray-900">Proyek {index + 1}</h4>
                <button
                  onClick={() => removeProject(index)}
                  className="text-red-500 hover:text-red-700 p-2 rounded-lg hover:bg-red-50 transition-colors"
                >
                  <TrashIcon className="w-4 h-4" />
                </button>
              </div>
              
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Judul Proyek
                    </label>
                    <input
                      type="text"
                      value={project.title}
                      onChange={(e) => updateProject(index, 'title', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="mis., Website E-commerce"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Link Proyek (Opsional)
                    </label>
                    <input
                      type="url"
                      value={project.link || ''}
                      onChange={(e) => updateProject(index, 'link', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="https://github.com/namaanda/proyek"
                    />
                  </div>
                </div>
                
                <div>
                  <div className="flex items-center justify-between mb-1">
                    <label className="block text-sm font-medium text-gray-700">
                      Deskripsi
                    </label>
                    <AIAssistantButton
                      onClick={() => handleAIAssistantClick(`projects.${index}.description`, project.description, 'textarea')}
                      isLoading={aiAssistant.isLoading && aiAssistant.fieldKey === `projects.${index}.description`}
                    />
                  </div>
                  <div className="relative">
                    <textarea
                      value={project.description}
                      onChange={(e) => updateProject(index, 'description', e.target.value)}
                      rows={3}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Deskripsi singkat tentang proyek..."
                    />
                    {aiAssistant.isOpen && aiAssistant.fieldKey === `projects.${index}.description` && (
                      <AISuggestionsDropdown
                        suggestions={aiAssistant.suggestions}
                        onSelect={(suggestion) => handleAISuggestionSelect(suggestion, `projects.${index}.description`)}
                        onClose={closeAIAssistant}
                        isLoading={aiAssistant.isLoading}
                      />
                    )}
                  </div>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Teknologi yang Digunakan
                  </label>
                  <input
                    type="text"
                    value={editingValues[`projects-${index}-technologies`] ?? arrayToString(project.technologies)}
                    onChange={(e) => {
                      setEditingValues(prev => ({ ...prev, [`projects-${index}-technologies`]: e.target.value }));
                      updateProject(index, 'technologies', stringToArray(e.target.value));
                    }}
                    onBlur={(e) => {
                      setEditingValues(prev => {
                        const newValues = { ...prev };
                        delete newValues[`projects-${index}-technologies`];
                        return newValues;
                      });
                    }}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="mis., React, Node.js, MongoDB, Express"
                  />
                  <p className="mt-1 text-xs text-gray-500">Pisahkan dengan koma</p>
                </div>
                
                <div>
                  <div className="flex items-center justify-between mb-1">
                    <label className="block text-sm font-medium text-gray-700">
                      Pencapaian Utama (Opsional)
                    </label>
                    <AIAssistantButton
                      onClick={() => handleAIAssistantClick(`projects.${index}.achievements`, arrayToString(project.achievements || []), 'text')}
                      isLoading={aiAssistant.isLoading && aiAssistant.fieldKey === `projects.${index}.achievements`}
                    />
                  </div>
                  <div className="relative">
                    <textarea
                      value={editingValues[`projects-${index}-achievements`] ?? arrayToString(project.achievements || [])}
                      onChange={(e) => {
                        setEditingValues(prev => ({ ...prev, [`projects-${index}-achievements`]: e.target.value }));
                        updateProject(index, 'achievements', stringToArray(e.target.value));
                      }}
                      onBlur={(e) => {
                        setEditingValues(prev => {
                          const newValues = { ...prev };
                          delete newValues[`projects-${index}-achievements`];
                          return newValues;
                        });
                      }}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-vertical"
                      placeholder="mis., Meningkatkan performa 40%, Mengimplementasikan autentikasi pengguna"
                      rows={3}
                    />
                    {aiAssistant.isOpen && aiAssistant.fieldKey === `projects.${index}.achievements` && (
                      <AISuggestionsDropdown
                        suggestions={aiAssistant.suggestions}
                        onSelect={(suggestion) => handleAISuggestionSelect(suggestion, `projects.${index}.achievements`)}
                        onClose={closeAIAssistant}
                        isLoading={aiAssistant.isLoading}
                      />
                    )}
                  </div>
                  <p className="mt-1 text-xs text-gray-500">Pisahkan dengan koma</p>
                </div>
              </div>
            </div>
          ))}
          
          <button
            onClick={addProject}
            className="w-full py-4 bg-gradient-to-r from-gray-50 to-blue-50 border-2 border-dashed border-blue-200 rounded-xl text-blue-600 hover:from-blue-50 hover:to-indigo-50 hover:border-blue-300 hover:text-blue-700 flex items-center justify-center gap-2 font-medium transition-all duration-200 shadow-sm hover:shadow-md"
          >
            <PlusIcon className="w-5 h-5" />
            Tambah Proyek
          </button>
        </div>
      </CollapsibleSection>

      {/* Languages */}
      <CollapsibleSection 
        title="Bahasa" 
        badge={data.languages?.length.toString() || '0'}
      >
        <div className="space-y-6">
          {(data.languages || []).map((language, index) => (
            <div key={index} className="bg-gray-50 rounded-lg p-3 sm:p-4">
              <div className="flex justify-between items-center mb-4">
                <h4 className="font-medium text-gray-900">Bahasa {index + 1}</h4>
                <button
                  onClick={() => removeLanguage(index)}
                  className="text-red-500 hover:text-red-700 p-2 rounded-lg hover:bg-red-50 transition-colors"
                >
                  <TrashIcon className="w-4 h-4" />
                </button>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Bahasa
                  </label>
                  <input
                    type="text"
                    value={language.language}
                    onChange={(e) => updateLanguage(index, 'language', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="mis., Bahasa Inggris, Bahasa Spanyol, Bahasa Prancis"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Tingkat Kemampuan
                  </label>
                  <select
                    value={language.proficiency}
                    onChange={(e) => updateLanguage(index, 'proficiency', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="">Pilih tingkat kemampuan</option>
                    <option value="Penutur Asli">Penutur Asli</option>
                    <option value="Fasih">Fasih</option>
                    <option value="Mahir">Mahir</option>
                    <option value="Menengah">Menengah</option>
                    <option value="Dasar">Dasar</option>
                  </select>
                </div>
              </div>
            </div>
          ))}
          
          <button
            onClick={addLanguage}
            className="w-full py-4 bg-gradient-to-r from-gray-50 to-blue-50 border-2 border-dashed border-blue-200 rounded-xl text-blue-600 hover:from-blue-50 hover:to-indigo-50 hover:border-blue-300 hover:text-blue-700 flex items-center justify-center gap-2 font-medium transition-all duration-200 shadow-sm hover:shadow-md"
          >
            <PlusIcon className="w-5 h-5" />
            Tambah Bahasa
          </button>
        </div>
      </CollapsibleSection>

      {/* Awards */}
      <CollapsibleSection 
        title="Penghargaan & Kehormatan" 
        badge={data.awards?.length.toString() || '0'}
      >
        <div className="space-y-6">
          {(data.awards || []).map((award, index) => (
            <div key={award.id} className="bg-gradient-to-br from-white via-rose-50/40 to-pink-50/30 rounded-xl p-3 sm:p-5 shadow-sm border border-gray-100 hover:shadow-md transition-all duration-200">
              <div className="flex justify-between items-center mb-4">
                <h4 className="font-medium text-gray-900">Penghargaan {index + 1}</h4>
                <button
                  onClick={() => removeAward(index)}
                  className="text-red-500 hover:text-red-700 p-2 rounded-lg hover:bg-red-50 transition-colors"
                >
                  <TrashIcon className="w-4 h-4" />
                </button>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Judul Penghargaan
                  </label>
                  <input
                    type="text"
                    value={award.title}
                    onChange={(e) => updateAward(index, 'title', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="mis., Karyawan Terbaik Bulan Ini"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Organisasi Penerbit
                  </label>
                  <input
                    type="text"
                    value={award.issuer}
                    onChange={(e) => updateAward(index, 'issuer', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="mis., Nama Perusahaan"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Tanggal Diterima
                  </label>
                  <input
                    type="text"
                    value={award.date}
                    onChange={(e) => updateAward(index, 'date', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="mis., Desember 2023"
                  />
                </div>
              </div>
              
              <div>
                <div className="flex items-center justify-between mb-1">
                  <label className="block text-sm font-medium text-gray-700">
                    Deskripsi (Opsional)
                  </label>
                  <AIAssistantButton
                    onClick={() => handleAIAssistantClick(`awards.${index}.description`, award.description || '', 'textarea')}
                    isLoading={aiAssistant.isLoading && aiAssistant.fieldKey === `awards.${index}.description`}
                  />
                </div>
                <div className="relative">
                  <textarea
                    value={award.description || ''}
                    onChange={(e) => updateAward(index, 'description', e.target.value)}
                    rows={2}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Deskripsi singkat tentang penghargaan..."
                  />
                  {aiAssistant.isOpen && aiAssistant.fieldKey === `awards.${index}.description` && (
                    <AISuggestionsDropdown
                      suggestions={aiAssistant.suggestions}
                      onSelect={(suggestion) => handleAISuggestionSelect(suggestion, `awards.${index}.description`)}
                      onClose={closeAIAssistant}
                      isLoading={aiAssistant.isLoading}
                    />
                  )}
                </div>
              </div>
            </div>
          ))}
          
          <button
            onClick={addAward}
            className="w-full py-4 bg-gradient-to-r from-gray-50 to-blue-50 border-2 border-dashed border-blue-200 rounded-xl text-blue-600 hover:from-blue-50 hover:to-indigo-50 hover:border-blue-300 hover:text-blue-700 flex items-center justify-center gap-2 font-medium transition-all duration-200 shadow-sm hover:shadow-md"
          >
            <PlusIcon className="w-5 h-5" />
            Tambah Penghargaan
          </button>
        </div>
      </CollapsibleSection>
    </div>
  );
};

export default ResumeEditForm;