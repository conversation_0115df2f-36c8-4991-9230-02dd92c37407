'use client';

import React, { useEffect, PropsWithChildren } from 'react';
import rollbar from '@/lib/rollbar';

// High-order component untuk menangkap error dan melaporkannya ke Rollbar
export function withErrorTracking<P extends object>(
  Component: React.ComponentType<P>,
  componentName: string
): React.FC<P> {
  return (props: P) => {
    try {
      return <Component {...props} />;
    } catch (error) {
      // Log error ke Rollbar
      rollbar.logError(error as Error, {
        componentName,
        componentProps: JSON.stringify(props),
      });
      
      // Re-throw error agar ditangkap oleh error boundary
      throw error;
    }
  };
}

// Global error handler untuk catch uncaught errors
export function setupGlobalErrorHandlers() {
  if (typeof window !== 'undefined') {
    // Tangkap unhandled rejections
    window.addEventListener('unhandledrejection', (event) => {
      rollbar.logError('Unhandled Promise Rejection', {
        reason: event.reason,
        promise: event.promise.toString(),
      });
    });

    // Tangkap global errors
    window.addEventListener('error', (event) => {
      rollbar.logError('Global Error Event', {
        message: event.message,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        error: event.error,
      });
    });

    // Log navigation events for better debugging
    const originalPushState = history.pushState;
    history.pushState = function() {
      const result = originalPushState.apply(this, arguments as any);
      // rollbar.logInfo('Navigation', {
      //   url: window.location.href
      // });
      return result;
    };
  }
}

// Wrapper component untuk menangkap error di children
export function RollbarErrorBoundary({ children }: PropsWithChildren<{}>) {
  useEffect(() => {
    setupGlobalErrorHandlers();
  }, []);

  return <>{children}</>;
}
