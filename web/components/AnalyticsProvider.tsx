'use client';

import { useEffect, Suspense } from 'react';
import { usePathname, useSearchParams } from 'next/navigation';
import mixpanel from '../lib/mixpanel';
import rollbar from '../lib/rollbar';

// Separate component that uses searchParams
function AnalyticsTracker() {
  const pathname = usePathname();
  const searchParams = useSearchParams();
  
  // Track page views
  useEffect(() => {
    // Only track after initialization and when pathname changes
    if (pathname) {
      // Track page view in Mixpanel
      mixpanel.track('Page View', {
        pathname,
        search: searchParams?.toString(),
        url: `${pathname}${searchParams?.toString() ? `?${searchParams.toString()}` : ''}`,
      });
    }
  }, [pathname, searchParams]);

  return null;
}

export function AnalyticsProvider({ 
  children 
}: { 
  children: React.ReactNode 
}) {
  // Initialize services
  useEffect(() => {
    // Initialize analytics services asynchronously
    const initServices = async () => {
      await mixpanel.initMixpanel(); // Now handles async initialization
      rollbar.initRollbar();
    };
    
    initServices();
  }, []);
  
  return (
    <>
      <Suspense fallback={null}>
        <AnalyticsTracker />
      </Suspense>
      {children}
    </>
  );
}
