"use client";

import { FC } from "react";
import { FiCheck } from "react-icons/fi";

interface SuggestionListProps {
  suggestions: string[];
  onApply: (text: string) => void;
}

const SuggestionList: FC<SuggestionListProps> = ({ suggestions, onApply }) => {
  if (!suggestions.length) {
    return <p className="text-gray-500">Tidak ada saran</p>;
  }

  return (
    <ul className="space-y-4">
      {suggestions.map((s, idx) => (
        <li
          key={idx}
          className="flex items-start gap-3 p-3 bg-white rounded-lg border border-gray-200 shadow-sm"
        >
          <FiCheck className="w-5 h-5 text-green-500 flex-shrink-0 mt-1" />
          <div className="flex-1">
            <p className="text-gray-800 text-sm mb-2">{s}</p>
            <button
              onClick={() => onApply(s)}
              className="text-primary text-xs font-semibold hover:underline"
            >
              Terapkan
            </button>
          </div>
        </li>
      ))}
    </ul>
  );
};

export default SuggestionList;
