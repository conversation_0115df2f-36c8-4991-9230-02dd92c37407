"use client";

import { FC, useState } from "react";

type ResumeFormData = {
  fullName: string;
  professionalTitle: string;
  professionalSummary: string;
  mostRecentJob: {
    title: string;
    company: string;
    achievements: string;
  };
  skills: string;
};

interface EditResumeDrawerProps {
  open: boolean;
  onClose: () => void;
  data: ResumeFormData;
  onSave: (updated: ResumeFormData) => void;
}

const EditResumeDrawer: FC<EditResumeDrawerProps> = ({ open, onClose, data, onSave }) => {
  const [form, setForm] = useState<ResumeFormData>(data);

  if (!open) return null;

  return (
    <div className="fixed inset-0 z-50 flex">
      {/* backdrop */}
      <div className="absolute inset-0 bg-black/50" onClick={onClose} />

      {/* drawer */}
      <div className="relative ml-auto h-full w-full max-w-xl bg-white shadow-xl overflow-y-auto p-6 animate-slide-in">
        <h2 className="text-lg font-semibold mb-4">Edit Konten CV</h2>
        {/* Contact & Summary */}
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Nama Lengkap</label>
            <input
              type="text"
              value={form.fullName}
              onChange={(e) => setForm({ ...form, fullName: e.target.value })}
              className="w-full border border-gray-300 rounded p-2 text-sm"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Jabatan Profesional</label>
            <input
              type="text"
              value={form.professionalTitle}
              onChange={(e) => setForm({ ...form, professionalTitle: e.target.value })}
              className="w-full border border-gray-300 rounded p-2 text-sm"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Ringkasan Profesional</label>
            <textarea
              value={form.professionalSummary}
              onChange={(e) => setForm({ ...form, professionalSummary: e.target.value })}
              className="w-full border border-gray-300 rounded p-2 text-sm h-28"
            />
          </div>
        </div>

        {/* Most Recent Job */}
        <hr className="my-6" />
        <div className="space-y-4">
          <h3 className="font-semibold">Pekerjaan Terakhir</h3>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Jabatan</label>
            <input
              type="text"
              value={form.mostRecentJob.title}
              onChange={(e) => setForm({ ...form, mostRecentJob: { ...form.mostRecentJob, title: e.target.value } })}
              className="w-full border border-gray-300 rounded p-2 text-sm"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Perusahaan</label>
            <input
              type="text"
              value={form.mostRecentJob.company}
              onChange={(e) => setForm({ ...form, mostRecentJob: { ...form.mostRecentJob, company: e.target.value } })}
              className="w-full border border-gray-300 rounded p-2 text-sm"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Pencapaian Utama</label>
            <textarea
              value={form.mostRecentJob.achievements}
              onChange={(e) => setForm({ ...form, mostRecentJob: { ...form.mostRecentJob, achievements: e.target.value } })}
              className="w-full border border-gray-300 rounded p-2 text-sm h-24"
            />
          </div>
        </div>

        {/* Skills */}
        <hr className="my-6" />
        <div className="space-y-4">
          <h3 className="font-semibold">Keahlian</h3>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Daftar Keahlian (pisahkan dengan koma)</label>
            <textarea
              value={form.skills}
              onChange={(e) => setForm({ ...form, skills: e.target.value })}
              className="w-full border border-gray-300 rounded p-2 text-sm h-24"
            />
          </div>
        </div>
        <div className="mt-4 flex justify-end gap-2">
          <button
            onClick={onClose}
            className="px-4 py-2 text-sm rounded border border-gray-300 hover:bg-gray-100"
          >
            Batal
          </button>
          <button
            onClick={() => {
              onSave(form);
              onClose();
            }}
            className="px-4 py-2 text-sm rounded bg-primary text-white hover:bg-blue-700"
          >
            Simpan
          </button>
        </div>
      </div>

      <style jsx>{`
        .animate-slide-in {
          animation: slideIn 0.25s ease-out;
        }
        @keyframes slideIn {
          from {
            transform: translateX(100%);
          }
          to {
            transform: translateX(0);
          }
        }
      `}</style>
    </div>
  );
};

export default EditResumeDrawer;
