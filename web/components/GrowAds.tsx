'use client';

import { useEffect } from 'react';

interface GrowMeFunction {
  (e: any): void;
  _: any[];
}

declare global {
  interface Window {
    growMe?: GrowMeFunction;
  }
}

export default function GrowAds() {
  useEffect(() => {
    try {
      const script = document.createElement('script');
      script.setAttribute('data-grow-initializer', '');
      const scriptContent = document.createTextNode(`!(function(){window.growMe||((window.growMe=function(e){window.growMe._.push(e);}),(window.growMe._=[]));var e=document.createElement("script");(e.type="text/javascript"),(e.src="https://faves.grow.me/main.js"),(e.defer=!0),e.setAttribute("data-grow-faves-site-id","U2l0ZTo2MGJhY2M5ZS1hNzhlLTRiNWEtYWEyZi00ZDIyMjVhZWNhOGY=");var t=document.getElementsByTagName("script")[0];t.parentNode.insertBefore(e,t);})();`);
      script.appendChild(scriptContent);
      document.head.appendChild(script);
    } catch (error) {
      console.error('Error initializing GrowMe:', error);
    }

    // return () => {
    //   // Cleanup if needed
    // };
  }, []);

  return null; // This component doesn't render anything
}
