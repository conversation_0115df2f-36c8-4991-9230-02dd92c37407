'use client';

import Link from 'next/link';
import { Nunito } from 'next/font/google';
import { useState, useEffect } from 'react';
import { usePathname, useRouter } from 'next/navigation';
import { useAuth } from '@/hooks/useAuth';
import NavLink from './NavLink';
import { FileText, Mail, Briefcase, User, Menu, X, Coins, LogOut, UserPlus } from 'lucide-react';

const nunito = Nunito({ subsets: ['latin'], weight: '700' });

const navigationItems = [
  { href: '/resume-builder', label: 'CV ATS Friendly', icon: User },
  { href: '/email-application', label: 'Email Lamaran', icon: Mail },
  { href: '/application-letter', label: 'Surat Lamaran', icon: FileText },
  { href: '/job-match', label: 'Kecocokan Lowongan', icon: Briefcase },
];

type NavbarProps = {
  auth: ReturnType<typeof useAuth>;
};

export default function Navbar({ auth }: NavbarProps) {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const router = useRouter();
  const pathname = usePathname();
  const { user, loading, signOut, profile, profileLoading } = auth;

  useEffect(() => {
    setIsMenuOpen(false);
  }, [pathname]);

  const handleSignOut = async () => {
    await signOut();
    router.push('/');
  };

  const renderTokenButton = () => (
    <Link href="/buy-tokens" className="flex items-center bg-amber-50 border border-amber-200 text-amber-700 px-3 py-2 rounded-lg hover:bg-amber-100 transition-colors cursor-pointer">
      <svg className="w-4 h-4 text-amber-600 mr-1" fill="currentColor" viewBox="0 0 24 24">
        <path d="M21 6.375C21 9.06739 16.9706 11.25 12 11.25C7.02944 11.25 3 9.06739 3 6.375C3 3.68261 7.02944 1.5 12 1.5C16.9706 1.5 21 3.68261 21 6.375Z" />
        <path d="M12 12.75C14.6852 12.75 17.1905 12.1637 19.0784 11.1411C19.7684 10.7673 20.4248 10.3043 20.9747 9.75674C20.9915 9.87831 21 10.0011 21 10.125C21 12.8174 16.9706 15 12 15C7.02944 15 3 12.8174 3 10.125C3 10.0011 3.00853 9.8783 3.02529 9.75674C3.57523 10.3043 4.23162 10.7673 4.92161 11.1411C6.80949 12.1637 9.31481 12.75 12 12.75Z" />
        <path d="M12 16.5C14.6852 16.5 17.1905 15.9137 19.0784 14.8911C19.7684 14.5173 20.4248 14.0543 20.9747 13.5067C20.9915 13.6283 21 13.7511 21 13.875C21 16.5674 16.9706 18.75 12 18.75C7.02944 18.75 3 16.5674 3 13.875C3 13.7511 3.00853 13.6283 3.02529 13.5067C3.57523 14.0543 4.23162 14.5173 4.92161 14.8911C6.80949 15.9137 9.31481 16.5 12 16.5Z" />
        <path d="M12 20.25C14.6852 20.25 17.1905 19.6637 19.0784 18.6411C19.7684 18.2673 20.4248 17.8043 20.9747 17.2567C20.9915 17.3783 21 17.5011 21 17.625C21 20.3174 16.9706 22.5 12 22.5C7.02944 22.5 3 20.3174 3 17.625C3 17.5011 3.00853 17.3783 3.02529 17.2567C3.57523 17.8043 4.23162 18.2673 4.92161 18.6411C6.80949 19.6637 9.31481 20.25 12 20.25Z" />
      </svg>
      <span className="text-sm font-medium text-amber-800">
        {profileLoading ? '...' : (profile?.tokens ?? '-')}
      </span>
      <svg className="w-3 h-3 text-amber-600 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 5v14m7-7H5" />
      </svg>
    </Link>
  );

  return (
    <nav className="bg-white shadow-md fixed top-0 w-full z-20">
      <div className="max-w-screen-xl mx-auto px-4 sm:px-6 lg:px-8 py-1">
        <div className="flex items-center justify-between h-16">
          {/* Logo Section */}
          <div className="flex-shrink-0 flex items-center">
            <Link href="/" className="flex items-center">
              <img src="/images/logo.svg" alt="Gigsta Logo" className="h-12 sm:h-16 invert" />
              <span className={`hidden sm:block ml-2 text-2xl font-bold text-gray-900 tracking-tight ${nunito.className}`}>Gigsta</span>
            </Link>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden lg:flex lg:items-center lg:space-x-2 xl:space-x-4">
            {navigationItems.map((item) => (
              <NavLink key={item.href} href={item.href}>
                {item.label}
              </NavLink>
            ))}
          </div>

          {/* User Actions Section */}
          <div className="flex items-center justify-end">
            <div className="hidden lg:flex items-center space-x-3">
              {!loading && (
                <>
                  {user ? (
                    <>
                      {renderTokenButton()}
                      <div className="relative">
                        <Link href="/profile" className="flex items-center justify-center w-10 h-10 bg-gray-100 hover:bg-gray-200 rounded-full">
                          <User className="w-5 h-5 text-gray-600" />
                        </Link>
                      </div>
                      <button onClick={handleSignOut} className="flex items-center justify-center w-10 h-10 bg-gray-100 hover:bg-gray-200 rounded-full">
                        <LogOut className="w-5 h-5 text-gray-600" />
                      </button>
                    </>
                  ) : (
                    <>
                      <Link href="/login" className="px-4 py-2 text-sm font-medium text-gray-600 hover:text-primary transition-colors">
                        Masuk
                      </Link>
                      <Link href="/register" className="ml-2 inline-flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary-dark">
                        Daftar
                      </Link>
                    </>
                  )}
                </>
              )}
            </div>

            {/* Mobile Menu Button */}
            <div className="-mr-2 flex items-center lg:hidden gap-2">
              {!loading && user && renderTokenButton()}
              <button
                onClick={() => setIsMenuOpen(!isMenuOpen)}
                className="inline-flex items-center justify-center p-2 rounded-md text-gray-500 hover:text-primary hover:bg-gray-100 focus:outline-none"
              >
                <span className="sr-only">Buka menu</span>
                {isMenuOpen ? <X className="block h-6 w-6" /> : <Menu className="block h-6 w-6" />}
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile Menu */}
      {isMenuOpen && (
        <div className="lg:hidden bg-white border-t border-gray-200">
          <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3">
            {navigationItems.map((item) => (
              <NavLink key={item.href} href={item.href} mobileView>
                {item.label}
              </NavLink>
            ))}
          </div>
          <div className="pt-4 pb-3 border-t border-gray-200">
            <div className="px-4 space-y-3">
              {!loading && (
                <>
                  {user ? (
                    <>
                      <Link href="/profile" className="flex items-center p-3 rounded-lg bg-gray-50 hover:bg-gray-100">
                        <div className="flex-shrink-0">
                          <User className="w-6 h-6 text-primary" />
                        </div>
                        <div className="ml-3 flex-1">
                          <p className="text-sm font-medium text-gray-800 truncate">{user.email}</p>
                          <p className="text-xs text-gray-500">Lihat Profil</p>
                        </div>
                      </Link>
                      <button onClick={handleSignOut} className="w-full flex items-center p-3 rounded-lg bg-gray-50 hover:bg-gray-100 text-gray-700">
                        <LogOut className="w-5 h-5 mr-3" />
                        <span className="text-sm font-medium">Keluar</span>
                      </button>
                    </>
                  ) : (
                    <div className="grid grid-cols-2 gap-3">
                       <Link href="/login" className="w-full flex items-center justify-center px-4 py-3 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                         <LogOut className="w-5 h-5 mr-2" /> Masuk
                      </Link>
                      <Link href="/register" className="w-full flex items-center justify-center px-4 py-3 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary-dark">
                        <UserPlus className="w-5 h-5 mr-2" /> Daftar
                      </Link>
                    </div>
                  )}
                </>
              )}
            </div>
          </div>
        </div>
      )}
    </nav>
  );
}
