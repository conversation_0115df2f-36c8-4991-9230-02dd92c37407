'use client';

import { useState, useCallback, useRef, useEffect } from 'react';
import Image from "next/legacy/image";
import { useAnalytics } from '@/hooks/useAnalytics';
import { InputMethod } from '../types/InputMethod';

interface JobInfoInputProps {
  jobDescription: string;
  onJobDescriptionChange: (value: string) => void;
  isProcessingImage?: boolean;
  jobImage?: File | null; // Added to store the job poster image
  onJobImageChange?: (file: File | null) => void; // Added to update the job image
  onInputMethodChange?: (method: InputMethod) => void; // Callback for parent to access input method
  onError?: (message: string) => void; // Callback for error handling
}

export default function JobInfoInput({
  jobDescription,
  onJobDescriptionChange,
  jobImage,
  onJobImageChange,
  onInputMethodChange,
  onError
}: JobInfoInputProps) {
  const { trackEvent } = useAnalytics();
  const [inputMethod, setInputMethod] = useState<InputMethod>(InputMethod.TEXT);
  const [previewImage, setPreviewImage] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isImageEnlarged, setIsImageEnlarged] = useState(false);

  // Effect to recreate preview image when jobImage prop changes
  useEffect(() => {
    if (jobImage && jobImage instanceof File) {
      const reader = new FileReader();
      reader.onload = (e) => {
        setPreviewImage(e.target?.result as string);
      };
      reader.readAsDataURL(jobImage);

      // Switch to image input method if we have an image
      setInputMethod(InputMethod.IMAGE);
    } else if (!jobImage) {
      setPreviewImage(null);
    }
  }, [jobImage]);

  // Toggle between text and image input methods
  const toggleInputMethod = (method: InputMethod) => {
    setInputMethod(method);
    trackEvent('Job Info Input Method Changed', { method });
    if (onInputMethodChange) {
      onInputMethodChange(method);
    }
  };

  // Handle click on upload box
  const handleBoxClick = () => {
    fileInputRef.current?.click();
  };

  // Function to handle clicking on the preview image
  const handlePreviewImageClick = (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent triggering the parent's onClick
    setIsImageEnlarged(true);
  };

  // Function to handle removing the preview image
  const handleRemovePreview = (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent triggering the parent's onClick
    setPreviewImage(null);
    if (onJobImageChange) {
      onJobImageChange(null);
    }
    // Clear the file input value
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  // Handle file selection
  const handleFileChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;
    
    // Validate file type
    const validTypes = ['image/png', 'image/jpg', 'image/jpeg'];
    if (!validTypes.includes(file.type)) {
      if (onError) {
        onError('Hanya file PNG, JPG, dan JPEG yang diterima');
      }
      return;
    }
    
    // Track file selection
    trackEvent('Job Poster Image Selected', {
      file_type: file.type,
      file_size: file.size
    });
    
    // Show preview
    const reader = new FileReader();
    reader.onload = (e) => {
      setPreviewImage(e.target?.result as string);
    };
    reader.readAsDataURL(file);
    
    // Update job image if handler exists
    if (onJobImageChange) {
      onJobImageChange(file);
    }
  }, [onJobImageChange, trackEvent, onError]);

  return (
    <>
      <div>
        {/* Toggle buttons for input method */}
        <div className="flex mb-4 gap-2">
          <button
            type="button"
            onClick={() => toggleInputMethod(InputMethod.TEXT)}
            className={`px-4 py-2 rounded-md transition ${
              inputMethod === InputMethod.TEXT
                ? 'bg-primary text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            Input Teks
          </button>
          <button
            type="button"
            onClick={() => toggleInputMethod(InputMethod.IMAGE)}
            className={`px-4 py-2 rounded-md transition ${
              inputMethod === InputMethod.IMAGE
                ? 'bg-primary text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            Upload Gambar
          </button>
        </div>

        {/* Show the appropriate input method */}
        {inputMethod === InputMethod.TEXT ? (
          <div className="mb-4">
            <textarea
              id="jobDescription"
              rows={8}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary"
              placeholder="Salin dan tempel informasi lowongan pekerjaan di sini..."
              value={jobDescription}
              onChange={(e) => onJobDescriptionChange(e.target.value)}
            ></textarea>
            <p className="mt-1 text-sm text-gray-500">
              *Mohon sertakan posisi, nama perusahaan, persyaratan, tanggung jawab, dan kualifikasi untuk hasil terbaik
            </p>
          </div>
        ) : (
          <div>
            <div 
              className="border-2 border-dashed border-gray-300 rounded-lg p-8 mb-4 text-center transition-colors duration-300 ease-in-out cursor-pointer hover:border-primary"
              onClick={handleBoxClick}
            >
              {previewImage ? (
                <div className="max-w-full mx-auto relative">
                  <Image 
                    src={previewImage} 
                    alt="Job poster preview" 
                    width={400} 
                    height={400} 
                    className="mx-auto max-h-[400px] object-contain cursor-pointer"
                    onClick={handlePreviewImageClick}
                  />
                  <button
                    onClick={handleRemovePreview}
                    className="absolute top-2 right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600 transition"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                  </button>
                </div>
              ) : (
                <>
                  <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                  </svg>
                  <p className="mt-2 text-sm text-gray-500">
                    Klik untuk mengunggah gambar poster lowongan
                  </p>
                  <input
                    ref={fileInputRef}
                    type="file"
                    className="hidden"
                    accept=".png,.jpg,.jpeg"
                    onChange={handleFileChange}
                  />
                  <p className="mt-1 text-xs text-gray-400">PNG, JPG, JPEG (maks. 5MB)</p>
                </>
              )}
            </div>
            <p className="mt-1 text-sm text-gray-500">
              *Anda dapat mengunggah poster lowongan maupun screenshot informasi lowongan
            </p>
          </div>
        )}
      </div>

      {/* Image Lightbox Modal */}
      {isImageEnlarged && previewImage && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-80 z-50 flex items-center justify-center p-4"
          onClick={() => setIsImageEnlarged(false)}
        >
          <div className="relative max-w-screen-lg max-h-screen">
            <Image
              src={previewImage}
              alt="Job poster enlarged"
              width={1200}
              height={1200}
              className="max-w-full max-h-[90vh] object-contain"
            />
            <button
              onClick={() => setIsImageEnlarged(false)}
              className="absolute top-2 right-2 bg-red-500 text-white rounded-full p-2 hover:bg-red-600 transition"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </button>
          </div>
        </div>
      )}
    </>
  );
}
