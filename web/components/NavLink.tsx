'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';

interface NavLinkProps {
  href: string;
  children: React.ReactNode;
  onClick?: () => void;
  className?: string;
  mobileView?: boolean;
}

export default function NavLink({ href, children, onClick, mobileView = false }: NavLinkProps) {
  const pathname = usePathname();
  const isActive = pathname === href;
  
  // Style untuk mobile view
  if (mobileView) {
    return (
      <Link 
        href={href} 
        className={`${isActive ? 'bg-gray-50 border-primary text-primary' : 'border-transparent text-gray-500 hover:bg-gray-50 hover:border-primary hover:text-primary'} block pl-4 pr-5 py-3 border-l-4 text-base font-medium`}
        onClick={onClick}
      >
        {children}
      </Link>
    );
  }
  
  // Style untuk desktop view
  return (
    <Link 
      href={href} 
      className={`${isActive ? 'border-transparent text-primary' : 'border-transparent text-gray-500'} hover:text-primary inline-flex items-center px-1 lg:px-2 pt-1 border-b-2 text-center text-sm lg:text-base font-medium`}
    >
      {children}
    </Link>
  );
}
