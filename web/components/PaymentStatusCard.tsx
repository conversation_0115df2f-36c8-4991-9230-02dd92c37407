import React from 'react';
import Link from 'next/link';
import { useAnalytics } from '@/hooks/useAnalytics';

// Define types for component props and status
export type PaymentStatus = 'pending' | 'error' | 'paid';

export interface PaymentStatusCardProps {
  status: PaymentStatus;
  purchaseId?: string | null;
  invoiceUrl?: string | null;
  error?: string | null;
  onRetry?: (() => void) | null;
}

// Configuration for different status states
const STATUS_CONFIG = {
  pending: {
    title: 'Pembayaran Sedang Diproses',
    description: 'Invoice telah dibuat dan menunggu pembayaran Anda',
    iconPath: 'M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z',
    bgClasses: 'from-yellow-50 to-orange-50 border-yellow-200',
    iconBgClass: 'bg-yellow-100',
    iconColorClass: 'text-yellow-600',
    titleColorClass: 'text-yellow-800',
    descColorClass: 'text-yellow-700',
    buttonBgClass: 'bg-yellow-600 hover:bg-yellow-700',
    profileMessage: 'Anda dapat mengakses dan menyelesaikan pembayaran ini kapan saja dari halaman profil Anda. Pembayaran akan tersimpan dan dapat diakses meski Anda menutup halaman ini.',
    profileLinkText: 'Lihat di Profil',
    primaryActionText: 'Selesaikan Pembayaran',
    primaryActionIcon: 'M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z',
  },
  error: {
    title: 'Pembayaran Gagal Diproses',
    description: 'Terjadi masalah saat memproses pembayaran Anda',
    iconPath: 'M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z',
    bgClasses: 'from-red-50 to-pink-50 border-red-200',
    iconBgClass: 'bg-red-100',
    iconColorClass: 'text-red-600',
    titleColorClass: 'text-red-800',
    descColorClass: 'text-red-700',
    buttonBgClass: 'bg-red-600 hover:bg-red-700',
    profileMessage: 'Jika pembayaran gagal, Anda dapat mencoba lagi dari halaman profil.',
    profileLinkText: 'Kelola di Profil',
    primaryActionText: 'Coba Lagi',
    primaryActionIcon: 'M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15',
  },
  paid: {
    title: 'Pembayaran Berhasil!',
    description: 'Surat lamaran Anda akan diunduh dalam beberapa detik',
    iconPath: 'M5 13l4 4L19 7',
    bgClasses: 'from-green-50 to-emerald-50 border-green-200',
    iconBgClass: 'bg-green-100',
    iconColorClass: 'text-green-600',
    titleColorClass: 'text-green-800',
    descColorClass: 'text-green-700',
    buttonBgClass: 'bg-green-600 hover:bg-green-700',
    profileMessage: 'Pembelian ini telah disimpan di profil Anda. Anda dapat mengunduh ulang file kapan saja.',
    profileLinkText: 'Lihat Riwayat Pembelian',
    primaryActionText: 'Lihat Detail',
    primaryActionIcon: 'M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z',
  }
};

const PaymentStatusCard: React.FC<PaymentStatusCardProps> = ({
  status,
  purchaseId,
  invoiceUrl,
  error,
  onRetry
}) => {
  const { trackEvent } = useAnalytics();
  const config = STATUS_CONFIG[status];

  const handleProfileNavigation = () => {
    trackEvent('Profile Link Clicked', {
      source: `payment_${status}`,
      purchase_id: purchaseId
    });
  };

  const handlePrimaryAction = () => {
    if (status === 'error' && onRetry) {
      onRetry();
    }
    
    trackEvent('Payment Link Clicked', {
      source: 'payment_status_card',
      purchase_id: purchaseId
    });
  };

  // Show success message for paid status, but don't show for other statuses that don't need action
  if (status === 'paid') {
    return (
      <div className={`mt-4 p-6 bg-gradient-to-br ${config.bgClasses} border rounded-lg shadow-sm`}>
        {/* Status Header */}
        <div className="flex items-start sm:items-center mb-4 gap-3 sm:gap-4">
          <div className="flex-shrink-0 mt-0.5 sm:mt-0">
            <div className={`w-8 h-8 sm:w-10 sm:h-10 ${config.iconBgClass} rounded-full flex items-center justify-center`}>
              <svg className={`w-4 h-4 sm:w-5 sm:h-5 ${config.iconColorClass}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d={config.iconPath}></path>
              </svg>
            </div>
          </div>
          <div className="flex-1 min-w-0">
            <h3 className={`text-base sm:text-lg font-semibold ${config.titleColorClass} leading-tight`}>
              {config.title}
            </h3>
            <p className={`text-xs sm:text-sm ${config.descColorClass} mt-1 leading-relaxed`}>
              {config.description}
            </p>
          </div>
        </div>

        {/* Payment Details */}
        {purchaseId && (
          <div className="mb-4 p-3 bg-white/60 rounded-md border border-green-100">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-1 sm:gap-2 text-sm">
              <span className="text-gray-600 font-medium">ID Pembayaran:</span>
              <span className="font-mono text-gray-800 break-all text-xs sm:text-sm">{purchaseId}</span>
            </div>
          </div>
        )}

        {/* Profile Management Section */}
        <div className="mb-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <div className="flex items-start space-x-3">
            <div className="flex-1 min-w-0">
              <h4 className="text-sm font-medium text-blue-800 mb-1">💡 Kelola dari Profil</h4>
              <p className="text-xs text-blue-700 mb-2">
                {config.profileMessage}
              </p>
              <Link
                href="/profile"
                className="inline-flex items-center text-xs font-medium text-blue-600 hover:text-blue-800 hover:underline"
                onClick={handleProfileNavigation}
              >
                <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                </svg>
                {config.profileLinkText}
              </Link>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`mt-4 p-6 bg-gradient-to-br ${config.bgClasses} border rounded-lg shadow-sm`}>
      {/* Status Header */}
      <div className="flex items-start sm:items-center mb-4 gap-3 sm:gap-4">
        <div className="flex-shrink-0 mt-0.5 sm:mt-0">
          <div className={`w-8 h-8 sm:w-10 sm:h-10 ${config.iconBgClass} rounded-full flex items-center justify-center`}>
            <svg className={`w-4 h-4 sm:w-5 sm:h-5 ${config.iconColorClass}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d={config.iconPath}></path>
            </svg>
          </div>
        </div>
        <div className="flex-1 min-w-0">
          <h3 className={`text-base sm:text-lg font-semibold ${config.titleColorClass} leading-tight`}>
            {config.title}
          </h3>
          <p className={`text-xs sm:text-sm ${config.descColorClass} mt-1 leading-relaxed`}>
            {status === 'error' && error ? error : config.description}
          </p>
        </div>
      </div>

      {/* Payment Details */}
      {purchaseId && (
        <div className="mb-4 p-3 bg-white/60 rounded-md border border-yellow-100">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-1 sm:gap-2 text-sm">
            <span className="text-gray-600 font-medium">ID Pembayaran:</span>
            <span className="font-mono text-gray-800 break-all text-xs sm:text-sm">{purchaseId}</span>
          </div>
        </div>
      )}

      {/* Profile Management Section */}
      <div className="mb-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
        <div className="flex items-start space-x-3">
          <div className="flex-1 min-w-0">
            <h4 className="text-sm font-medium text-blue-800 mb-1">💡 Kelola dari Profil</h4>
            <p className="text-xs text-blue-700 mb-2">
              {config.profileMessage}
            </p>
            <Link
              href="/profile"
              className="inline-flex items-center text-xs font-medium text-blue-600 hover:text-blue-800 hover:underline"
              onClick={handleProfileNavigation}
            >
              <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
              </svg>
              {config.profileLinkText}
            </Link>
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex flex-col sm:flex-row gap-3">
        {status === 'pending' && invoiceUrl ? (
          <a
            href={invoiceUrl}
            target="_blank"
            rel="noopener noreferrer"
            className={`flex-1 inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-md text-white ${config.buttonBgClass} transition-colors shadow-sm`}
            onClick={handlePrimaryAction}
          >
            <svg className="-ml-1 mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d={config.primaryActionIcon}></path>
            </svg>
            {config.primaryActionText}
          </a>
        ) : status === 'error' && onRetry ? (
          <button
            onClick={handlePrimaryAction}
            className={`flex-1 inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-md text-white ${config.buttonBgClass} transition-colors shadow-sm`}
          >
            <svg className="-ml-1 mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d={config.primaryActionIcon}></path>
            </svg>
            {config.primaryActionText}
          </button>
        ) : null}
      </div>
    </div>
  );
};

export default React.memo(PaymentStatusCard);