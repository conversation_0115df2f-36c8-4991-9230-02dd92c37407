"use client";

import { FC, useCallback, useEffect, useRef, useState } from "react";
import { useShadowDOM } from "@/hooks/useShadowDOM";

interface ResumePreviewProps {
  htmlContent: string;
  onOverflowDetected?: (isOverflowing: boolean) => void;
  onPageCountDetected?: (pageCount: number) => void;
}

const ResumePreview: FC<ResumePreviewProps> = ({ htmlContent, onOverflowDetected, onPageCountDetected }) => {
  const shadowContainerRef = useShadowDOM({ html: htmlContent });

  // Zoom state management
  const [zoomLevel, setZoomLevel] = useState<number>(100);
  const [isDragging, setIsDragging] = useState<boolean>(false);
  const [dragStart, setDragStart] = useState<{ x: number; y: number }>({ x: 0, y: 0 });
  const [scrollOffset, setScrollOffset] = useState<{ x: number; y: number }>({ x: 0, y: 0 });

  // Refs for touch gesture handling
  const containerRef = useRef<HTMLDivElement>(null);
  const lastTouchDistance = useRef<number>(0);
  const isGesturing = useRef<boolean>(false);

  // Multi-page detection state
  const [isOverflowing, setIsOverflowing] = useState<boolean>(false);
  const [currentPageCount, setCurrentPageCount] = useState<number>(1);
  const [currentViewPage, setCurrentViewPage] = useState<number>(1);
  const overflowCheckTimer = useRef<NodeJS.Timeout | null>(null);
  
  // Zoom constraints
  const MIN_ZOOM = 10;
  const MAX_ZOOM = 200;
  const ZOOM_STEP = 10;

  // Zoom control functions
  const zoomIn = useCallback(() => {
    setZoomLevel(prev => Math.min(prev + ZOOM_STEP, MAX_ZOOM));
  }, []);

  const zoomOut = useCallback(() => {
    setZoomLevel(prev => Math.max(prev - ZOOM_STEP, MIN_ZOOM));
  }, []);

  const resetZoom = useCallback(() => {
    setZoomLevel(100);
    setScrollOffset({ x: 0, y: 0 });
  }, []);

  // Page navigation functions
  const goToPage = useCallback((pageNumber: number) => {
    if (pageNumber >= 1 && pageNumber <= currentPageCount) {
      setCurrentViewPage(pageNumber);
      // Scroll to the specific page
      const pageOffset = (pageNumber - 1) * 1123; // A4 height in pixels
      if (containerRef.current) {
        containerRef.current.scrollTo({
          top: pageOffset * (zoomLevel / 100),
          behavior: 'smooth'
        });
      }
    }
  }, [currentPageCount, zoomLevel]);

  const goToNextPage = useCallback(() => {
    if (currentViewPage < currentPageCount) {
      goToPage(currentViewPage + 1);
    }
  }, [currentViewPage, currentPageCount, goToPage]);

  const goToPrevPage = useCallback(() => {
    if (currentViewPage > 1) {
      goToPage(currentViewPage - 1);
    }
  }, [currentViewPage, goToPage]);

  // Touch gesture handlers for pinch-to-zoom
  const getTouchDistance = (touches: React.TouchList): number => {
    if (touches.length < 2) return 0;
    const touch1 = touches[0];
    const touch2 = touches[1];
    return Math.sqrt(
      Math.pow(touch2.clientX - touch1.clientX, 2) +
      Math.pow(touch2.clientY - touch1.clientY, 2)
    );
  };

  const handleTouchStart = useCallback((e: React.TouchEvent) => {
    if (e.touches.length === 2) {
      e.preventDefault();
      isGesturing.current = true;
      lastTouchDistance.current = getTouchDistance(e.touches);
    } else if (e.touches.length === 1 && zoomLevel > 100) {
      // Enable dragging when zoomed in
      setIsDragging(true);
      setDragStart({
        x: e.touches[0].clientX - scrollOffset.x,
        y: e.touches[0].clientY - scrollOffset.y
      });
    }
  }, [zoomLevel, scrollOffset]);

  const handleTouchMove = useCallback((e: React.TouchEvent) => {
    if (e.touches.length === 2 && isGesturing.current) {
      e.preventDefault();
      const currentDistance = getTouchDistance(e.touches);
      if (lastTouchDistance.current > 0) {
        const scale = currentDistance / lastTouchDistance.current;
        const newZoom = Math.min(Math.max(zoomLevel * scale, MIN_ZOOM), MAX_ZOOM);
        setZoomLevel(newZoom);
      }
      lastTouchDistance.current = currentDistance;
    } else if (e.touches.length === 1 && isDragging && zoomLevel > 100) {
      e.preventDefault();
      setScrollOffset({
        x: e.touches[0].clientX - dragStart.x,
        y: e.touches[0].clientY - dragStart.y
      });
    }
  }, [zoomLevel, isDragging, dragStart]);

  const handleTouchEnd = useCallback(() => {
    isGesturing.current = false;
    lastTouchDistance.current = 0;
    setIsDragging(false);
  }, []);

  // Mouse wheel zoom support
  const handleWheel = useCallback((e: React.WheelEvent) => {
    if (e.ctrlKey || e.metaKey) {
      e.preventDefault();
      const delta = e.deltaY > 0 ? -ZOOM_STEP : ZOOM_STEP;
      setZoomLevel(prev => Math.min(Math.max(prev + delta, MIN_ZOOM), MAX_ZOOM));
    }
  }, []);

  // Multi-page detection function
  const checkForOverflow = useCallback(() => {
    if (!shadowContainerRef.current || !htmlContent) return;

    // Clear existing timer
    if (overflowCheckTimer.current) {
      clearTimeout(overflowCheckTimer.current);
    }

    // Debounce the overflow check to avoid excessive calculations
    overflowCheckTimer.current = setTimeout(() => {
      try {
        const container = shadowContainerRef.current;
        if (!container) return;

        // Check if shadow DOM is being used
        const shadowRoot = container.shadowRoot;
        let resumeContainer: Element | null = null;

        if (shadowRoot) {
          // Shadow DOM is available
          resumeContainer = shadowRoot.querySelector('.resume-container');
          if (!resumeContainer) {
            resumeContainer = shadowRoot.querySelector('body') || shadowRoot.querySelector('html') || shadowRoot.firstElementChild;
          }
        } else {
          // Fallback to regular DOM (when shadow DOM is not supported)
          resumeContainer = container.querySelector('.resume-container');
          if (!resumeContainer) {
            // If no resume-container, use the container itself
            resumeContainer = container;
          }
        }

        if (!resumeContainer) return;

        // A4 dimensions in pixels at 96 DPI (standard web DPI)
        // 210mm = 794px, 297mm = 1123px
        const A4_HEIGHT_PX = 1123;

        // Get the actual content height
        const contentHeight = resumeContainer.scrollHeight;

        // Calculate number of pages needed
        const pageCount = Math.ceil(contentHeight / A4_HEIGHT_PX);

        // Check if content overflows the first page (legacy support)
        const overflowing = contentHeight > A4_HEIGHT_PX;

        // Update state if overflow status changed
        if (overflowing !== isOverflowing) {
          setIsOverflowing(overflowing);
          onOverflowDetected?.(overflowing);
        }

        // Update local page count state
        if (pageCount !== currentPageCount) {
          setCurrentPageCount(pageCount);
          // Reset to first page if current view page is beyond new page count
          if (currentViewPage > pageCount) {
            setCurrentViewPage(1);
          }
        }

        // Notify about page count
        onPageCountDetected?.(pageCount);
      } catch (error) {
        console.warn('Error checking for overflow:', error);
      }
    }, 300); // 300ms debounce
  }, [htmlContent, isOverflowing, currentPageCount, onOverflowDetected, onPageCountDetected]);

  // Check for overflow when content changes
  useEffect(() => {
    if (htmlContent) {
      // Longer delay to ensure shadow DOM is fully rendered and styled
      setTimeout(checkForOverflow, 500);
    }
  }, [htmlContent, checkForOverflow]);

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if ((e.ctrlKey || e.metaKey) && e.key === '0') {
        e.preventDefault();
        resetZoom();
      } else if ((e.ctrlKey || e.metaKey) && e.key === '=') {
        e.preventDefault();
        zoomIn();
      } else if ((e.ctrlKey || e.metaKey) && e.key === '-') {
        e.preventDefault();
        zoomOut();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [zoomIn, zoomOut, resetZoom]);

  // Cleanup timer on unmount
  useEffect(() => {
    return () => {
      if (overflowCheckTimer.current) {
        clearTimeout(overflowCheckTimer.current);
      }
    };
  }, []);

  return (
    <div className="w-fit max-w-full mx-auto bg-gray-100 rounded-lg">
      {/* Page Navigation Controls - Only show for multi-page content */}
      {currentPageCount > 1 && (
        <div className="flex items-center justify-between p-3 bg-blue-50 border-b border-blue-200">
          <div className="flex items-center gap-2">
            <button
              onClick={goToPrevPage}
              disabled={currentViewPage <= 1}
              className="flex items-center justify-center w-8 h-8 sm:w-10 sm:h-10 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors touch-manipulation"
              title="Previous Page"
            >
              <svg className="w-4 h-4 sm:w-5 sm:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </button>

            <div className="flex items-center justify-center min-w-[60px] sm:min-w-[80px] px-2 py-1 text-xs sm:text-sm font-medium bg-white border border-gray-300 rounded-lg">
              {currentViewPage} / {currentPageCount}
            </div>

            <button
              onClick={goToNextPage}
              disabled={currentViewPage >= currentPageCount}
              className="flex items-center justify-center w-8 h-8 sm:w-10 sm:h-10 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors touch-manipulation"
              title="Next Page"
            >
              <svg className="w-4 h-4 sm:w-5 sm:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </button>
          </div>

          {/* Page Jump Buttons for quick navigation */}
          <div className="flex items-center gap-1">
            {Array.from({ length: Math.min(currentPageCount, 5) }, (_, index) => {
              const pageNum = index + 1;
              return (
                <button
                  key={pageNum}
                  onClick={() => goToPage(pageNum)}
                  className={`w-6 h-6 sm:w-8 sm:h-8 text-xs font-medium rounded transition-colors touch-manipulation ${
                    currentViewPage === pageNum
                      ? 'bg-blue-500 text-white'
                      : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                  }`}
                  title={`Go to Page ${pageNum}`}
                >
                  {pageNum}
                </button>
              );
            })}
            {currentPageCount > 5 && (
              <span className="text-xs text-gray-500 ml-1">...</span>
            )}
          </div>
        </div>
      )}

      {/* Zoom Controls */}
      <div className="flex items-center justify-center p-3 bg-gray-50 border-b border-gray-200 rounded-t-lg">
        <div className="flex items-center gap-2">
          <button
            onClick={zoomOut}
            disabled={zoomLevel <= MIN_ZOOM}
            className="flex items-center justify-center w-8 h-8 sm:w-10 sm:h-10 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors touch-manipulation"
            title="Zoom Out (Ctrl+-)"
          >
            <svg className="w-4 h-4 sm:w-5 sm:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 12H4" />
            </svg>
          </button>
          
          <div className="flex items-center justify-center min-w-[60px] sm:min-w-[80px] px-2 py-1 text-xs sm:text-sm font-medium bg-white border border-gray-300 rounded-lg">
            {zoomLevel}%
          </div>
          
          <button
            onClick={zoomIn}
            disabled={zoomLevel >= MAX_ZOOM}
            className="flex items-center justify-center w-8 h-8 sm:w-10 sm:h-10 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors touch-manipulation"
            title="Zoom In (Ctrl+=)"
          >
            <svg className="w-4 h-4 sm:w-5 sm:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
            </svg>
          </button>
          
          <button
            onClick={resetZoom}
            className="ml-2 px-3 py-1 sm:px-4 sm:py-2 text-xs sm:text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors touch-manipulation"
            title="Reset Zoom (Ctrl+0)"
          >
            Reset
          </button>
        </div>
      </div>

      {/* Multi-Page Preview Container */}
      <div
        ref={containerRef}
        className="relative w-fit max-w-full bg-gray-100 shadow-lg overflow-auto"
        style={{
          touchAction: zoomLevel > 100 ? 'none' : 'auto'
        }}
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
        onWheel={handleWheel}
      >
        {/* Zoomable Content Container */}
        <div
          className="relative origin-top-left transition-transform duration-200 ease-out"
          style={{
            transform: `scale(${zoomLevel / 100}) translate(${scrollOffset.x}px, ${scrollOffset.y}px)`,
            cursor: zoomLevel > 100 ? (isDragging ? 'grabbing' : 'grab') : 'default'
          }}
        >
          {/* Multi-Page Layout */}
          {htmlContent ? (
            <div className="space-y-4">
              {/* Single continuous content with page break indicators */}
              <div className="relative bg-white shadow-sm">
                {/* Main content container */}
                <div ref={shadowContainerRef} className="text-left relative" />

                {/* Page break overlays for multi-page content */}
                {currentPageCount > 1 && Array.from({ length: currentPageCount - 1 }, (_, breakIndex) => (
                  <div
                    key={`page-break-${breakIndex}`}
                    className="absolute left-0 right-0 pointer-events-none"
                    style={{
                      top: `${(breakIndex + 1) * 1123}px`,
                      height: '2px',
                      background: 'linear-gradient(90deg, #ef4444 0%, #ef4444 50%, transparent 50%, transparent 100%)',
                      backgroundSize: '20px 2px',
                      zIndex: 10
                    }}
                  >
                    {/* Page break label */}
                    <div className="absolute -top-6 left-1/2 transform -translate-x-1/2 bg-red-100 text-red-600 text-xs px-2 py-1 rounded-full font-medium">
                      Halaman {breakIndex + 2}
                    </div>
                  </div>
                ))}

                {/* Page number indicators on the side */}
                {currentPageCount > 1 && Array.from({ length: currentPageCount }, (_, pageIndex) => (
                  <div
                    key={`page-number-${pageIndex}`}
                    className="absolute -left-12 text-xs text-gray-500 font-medium bg-gray-100 px-2 py-1 rounded"
                    style={{
                      top: `${pageIndex * 1123 + 20}px`
                    }}
                  >
                    {pageIndex + 1}
                  </div>
                ))}
              </div>
            </div>
          ) : (
            /* Show fallback message only when no content */
            <div className="flex items-center justify-center h-96 w-96 bg-white shadow-sm">
              <span className="text-gray-400">Pratinjau belum tersedia</span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ResumePreview;
