const path = require('path');
const fs = require('fs');
const handlebars = require('handlebars');

// Custom plugin to compile Handlebars templates
class HandlebarsCompilerPlugin {
  constructor(options) {
    this.options = options || {};
  }

  apply(compiler) {
    compiler.hooks.emit.tapAsync('HandlebarsCompilerPlugin', (compilation, callback) => {
      const templatesDir = path.resolve(__dirname, 'utils/handlebars-templates/letters');
      const outputDir = path.resolve(__dirname, 'utils/handlebars-templates/compiled');

      // Ensure output directory exists
      if (!fs.existsSync(outputDir)) {
        fs.mkdirSync(outputDir, { recursive: true });
      }

      // Template files to compile
      const templateFiles = [
        'plain-text.hbs',
        'classic-blue.hbs',
        'professional-classic.hbs',
        'minimalist-sidebar.hbs',
        'minimalist-border-frame.hbs',
        'minimalist-accent.hbs',
        'minimalist-circular-accents.hbs'
      ];

      templateFiles.forEach(templateFile => {
        const templatePath = path.join(templatesDir, templateFile);
        const outputPath = path.join(outputDir, templateFile.replace('.hbs', '.js'));

        if (fs.existsSync(templatePath)) {
          try {
            // Read template content
            const templateContent = fs.readFileSync(templatePath, 'utf8');
            
            // Compile template
            const compiledTemplate = handlebars.compile(templateContent);
            
            // Create JavaScript module content
            const moduleContent = `// Auto-generated from ${templateFile}
// Do not edit this file directly - edit the .hbs source file instead

const Handlebars = require('handlebars');

// Register common helpers
Handlebars.registerHelper('unless', function(conditional, options) {
  if (!conditional) {
    return options.fn(this);
  } else {
    return options.inverse(this);
  }
});

// Compiled template function
const compiledTemplate = ${compiledTemplate.toString()};

module.exports = compiledTemplate;
`;

            // Write compiled template to output directory
            fs.writeFileSync(outputPath, moduleContent, 'utf8');
            
            // Add to webpack assets for tracking
            compilation.assets[path.relative(compilation.outputOptions.path || '', outputPath)] = {
              source: () => moduleContent,
              size: () => moduleContent.length
            };

            console.log(`✓ Compiled ${templateFile} -> ${path.basename(outputPath)}`);
          } catch (error) {
            console.error(`✗ Error compiling ${templateFile}:`, error.message);
            compilation.errors.push(new Error(`HandlebarsCompilerPlugin: ${templateFile} - ${error.message}`));
          }
        } else {
          console.warn(`⚠ Template file not found: ${templateFile}`);
        }
      });

      callback();
    });
  }
}

module.exports = {
  mode: 'development',
  entry: './utils/handlebars-templates/index.js', // We'll create this entry point
  output: {
    path: path.resolve(__dirname, 'utils/handlebars-templates/compiled'),
    filename: 'index.js',
    libraryTarget: 'commonjs2'
  },
  target: 'node',
  resolve: {
    extensions: ['.js', '.hbs']
  },
  module: {
    rules: [
      {
        test: /\.hbs$/,
        use: [
          {
            loader: 'handlebars-loader',
            options: {
              helperDirs: [
                path.join(__dirname, 'utils/handlebars-helpers')
              ],
              partialDirs: [
                path.join(__dirname, 'utils/handlebars-templates/partials')
              ]
            }
          }
        ]
      }
    ]
  },
  plugins: [
    new HandlebarsCompilerPlugin()
  ],
  externals: {
    'handlebars': 'commonjs handlebars'
  }
};