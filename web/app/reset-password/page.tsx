'use client';

import { useState, useEffect, Suspense } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import Link from 'next/link';
import NavbarMinimal from '@/components/NavbarMinimal';
import { useAuth } from '@/hooks/useAuth';
import { AuthError, AuthWeakPasswordError } from '@supabase/supabase-js';

// Component that uses useSearchParams
function ResetPasswordForm({ onSuccess }: { onSuccess: () => void }) {
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [message, setMessage] = useState<string | null>(null);
  const searchParams = useSearchParams();
  const { updatePassword } = useAuth();

  useEffect(() => {
    // Check if we have the necessary parameters in the URL
    if (!searchParams.get('code')) {
      setError('Link reset password tidak valid atau telah kedaluwarsa.');
    }
  }, [searchParams]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (password !== confirmPassword) {
      setError('Password tidak cocok');
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      const result = await updatePassword(password);
      
      if (result.error) {
        if (result.message) {
          setError(result.message);
          return;
        } else {
          throw result.error;
        }
      }
      
      setMessage('Password berhasil diubah. Mengarahkan ke halaman utama');

      setTimeout(() => {
        onSuccess();
      }, 2000);
    } catch (error: any) {
      if (error instanceof AuthWeakPasswordError) {
        setError('Kata sandi terlalu lemah. Harus terdiri dari minimal 8 karakter, termasuk huruf besar, huruf kecil, angka, dan simbol');
      } else {
        setError('Terjadi kesalahan saat mengubah password');
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg relative" role="alert">
          <span className="block sm:inline">{error}</span>
        </div>
      )}
      
      {message && (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg relative" role="alert">
          <span className="block sm:inline">{message}</span>
        </div>
      )}
      
      {!searchParams.get('code') ? (
        <div className="text-center">
          <p className="text-red-600">Link reset password tidak valid atau telah kedaluwarsa.</p>
          <div className="mt-4">
            <Link href="/forgot-password" className="font-medium text-primary hover:text-primary-dark">
              Kirim ulang link reset password
            </Link>
          </div>
        </div>
      ) : (
        <div className="space-y-4">
          <div>
            <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
              Password Baru
            </label>
            <input
              id="password"
              name="password"
              type="password"
              required
              className="appearance-none rounded-lg relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary focus:z-10 sm:text-sm"
              placeholder="Password Baru"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
            />
          </div>
          <div>
            <label htmlFor="confirm-password" className="block text-sm font-medium text-gray-700 mb-1">
              Konfirmasi Password Baru
            </label>
            <input
              id="confirm-password"
              name="confirm-password"
              type="password"
              required
              className="appearance-none rounded-lg relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary focus:z-10 sm:text-sm"
              placeholder="Konfirmasi Password Baru"
              value={confirmPassword}
              onChange={(e) => setConfirmPassword(e.target.value)}
            />
          </div>

          <div>
            <button
              type="submit"
              disabled={isSubmitting}
              className="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors"
            >
              {isSubmitting ? 'Memproses...' : 'Ubah Password'}
            </button>
          </div>
        </div>
      )}
      
      <div className="flex items-center justify-center mt-4">
        <div className="text-sm">
          <Link href="/login" className="font-medium text-primary hover:text-primary-dark">
            Kembali ke Halaman Login
          </Link>
        </div>
      </div>
    </form>
  );
}

export default function ResetPassword() {
  const router = useRouter();

  const handleSuccess = () => {
    router.push('/');
  };

  return (
    <div className="min-h-screen flex flex-col bg-gray-50">
      {/* Navigation Bar */}
      <NavbarMinimal />
      
      {/* Reset Password Form Container */}
      <div className="flex-grow flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-md w-full bg-white rounded-xl shadow-lg p-8 space-y-8">
          <div className="text-center">
            {/* Gigsta Logo */}
            <div className="mx-auto mb-4 flex justify-center">
              <img src="/images/logo.svg" alt="Gigsta Logo" className="h-20 invert" />
            </div>
            <h2 className="text-3xl font-extrabold text-gray-900">Reset Password</h2>
            <p className="mt-2 text-sm text-gray-600">
              Masukkan password baru untuk akun Anda
            </p>
          </div>
          
          <Suspense fallback={<div>Loading...</div>}>
            <ResetPasswordForm onSuccess={handleSuccess} />
          </Suspense>
        </div>
      </div>
    </div>
  );
}
