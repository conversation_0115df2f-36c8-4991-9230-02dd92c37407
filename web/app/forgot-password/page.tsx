'use client';

import { useState } from 'react';
import Link from 'next/link';
import { useAuth } from '@/hooks/useAuth';
import NavbarMinimal from '@/components/NavbarMinimal';

export default function ForgotPassword() {
  const [email, setEmail] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [message, setMessage] = useState<string | null>(null);
  const { resetPassword } = useAuth();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError(null);
    setMessage(null);

    try {
      const { error } = await resetPassword(email);

      if (error) {
        throw error;
      }
      
      setMessage('Jika alamat email terdaftar, Anda akan menerima email tautan untuk mereset password. <PERSON><PERSON><PERSON> perik<PERSON> email Anda, termasuk folder spam.');
      setEmail('');
    } catch (error: any) {
      setError('<PERSON><PERSON><PERSON><PERSON> k<PERSON><PERSON>han saat mengirim permintaan reset password');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen flex flex-col bg-gray-50">
      {/* Navigation Bar */}
      <NavbarMinimal />
      
      {/* Forgot Password Form Container */}
      <div className="flex-grow flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-md w-full bg-white rounded-xl shadow-lg p-8 space-y-8">
          <div className="text-center">
            {/* Gigsta Logo */}
            <div className="mx-auto mb-4 flex justify-center">
              <img src="/images/logo.svg" alt="Gigsta Logo" className="h-20 invert" />
            </div>
            <h2 className="text-3xl font-extrabold text-gray-900">Lupa Password</h2>
            <p className="mt-2 text-sm text-gray-600">
              Masukkan alamat email Anda untuk reset password
            </p>
          </div>
          
          <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
            {error && (
              <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg relative" role="alert">
                <span className="block sm:inline">{error}</span>
              </div>
            )}
            
            {message && (
              <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg relative" role="alert">
                <span className="block sm:inline">{message}</span>
              </div>
            )}
            
            <div className="space-y-4">
              <div>
                <label htmlFor="email-address" className="block text-sm font-medium text-gray-700 mb-1">
                  Alamat Email
                </label>
                <input
                  id="email-address"
                  name="email"
                  type="email"
                  autoComplete="email"
                  required
                  className="appearance-none rounded-lg relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary focus:z-10 sm:text-sm"
                  placeholder="Alamat Email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                />
              </div>
            </div>

            <div>
              <button
                type="submit"
                disabled={isSubmitting}
                className="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors"
              >
                {isSubmitting ? 'Memproses...' : 'Kirim Link Reset Password'}
              </button>
            </div>
            
            <div className="flex items-center justify-center mt-4">
              <div className="text-sm">
                <Link href="/login" className="font-medium text-primary hover:text-primary-dark">
                  Kembali ke Halaman Login
                </Link>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
