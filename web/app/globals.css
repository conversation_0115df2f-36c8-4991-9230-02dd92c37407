@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 249, 250, 251;
  --background-end-rgb: 255, 255, 255;
}

body {
  color: rgb(var(--foreground-rgb));
  background: linear-gradient(
      to bottom,
      transparent,
      rgb(var(--background-end-rgb))
    )
    rgb(var(--background-start-rgb));
}

@layer components {
  .btn-primary {
    @apply bg-primary hover:bg-blue-700 text-white font-bold py-2 px-4 rounded transition-colors;
  }
  
  .btn-secondary {
    @apply bg-secondary hover:bg-green-700 text-white font-bold py-2 px-4 rounded transition-colors;
  }
  
  .input-field {
    @apply border border-gray-300 rounded-md p-2 w-full focus:outline-none focus:ring-2 focus:ring-primary;
  }
  
  .card {
    @apply bg-white shadow-md rounded-lg p-6;
  }

  /* Blog content formatting */
  .article-content h1 { @apply text-3xl font-bold text-gray-900 mb-6 mt-8 first:mt-0; }
  .article-content h2 { @apply text-2xl font-bold text-gray-900 mb-4 mt-8; }
  .article-content h3 { @apply text-xl font-semibold text-gray-900 mb-3 mt-6; }
  .article-content h4 { @apply text-lg font-semibold text-gray-900 mb-2 mt-4; }
  .article-content p { @apply mb-4 text-gray-700 leading-relaxed; }
  .article-content strong { @apply font-semibold text-gray-900; }
  .article-content em { @apply italic; }
  .article-content ul { @apply list-disc list-inside mb-6 space-y-2 ml-4; }
  .article-content ol { @apply list-decimal list-inside mb-6 space-y-2 ml-4; }
  .article-content li { @apply text-gray-700; }
  .article-content pre { @apply bg-gray-100 p-4 rounded-lg overflow-x-hidden my-6 border whitespace-pre-wrap break-words; }
  .article-content code { @apply bg-gray-100 px-2 py-1 rounded text-sm font-mono text-gray-800 break-words; }
  .article-content blockquote { @apply border-l-4 border-blue-500 pl-4 py-2 my-4 bg-blue-50 italic text-gray-700; }
  
  /* Ensure all content wraps properly */
  .article-content * {
    word-wrap: break-word;
    overflow-wrap: break-word;
    word-break: break-word;
    max-width: 100%;
  }
  
  .article-content img {
    max-width: 100%;
    height: auto;
  }
  
  .article-content table {
    width: 100%;
    overflow-x: auto;
    display: block;
    white-space: nowrap;
  }
}

@layer utilities {
  .line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }
}
