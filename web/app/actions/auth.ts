'use server'

import { revalidatePath } from 'next/cache'
import { createClient } from '@/lib/supabase-server'
import { AuthApiError, AuthWeakPasswordError, Provider } from '@supabase/supabase-js'
import mixpanel from '@/lib/mixpanel'

type AuthResultType = {
  error: Error | null;
  success: boolean;
  message?: string;
}

/**
 * Track user identity in Mixpanel
 */
function trackUserIdentity(userId: string, email: string, fullName: string = '') {
  mixpanel.identify(userId)
  mixpanel.people.set({
    $email: email,
    $name: fullName || '',
    $last_login: new Date().toISOString()
  })
}

/**
 * Track authentication errors in Mixpanel
 */
function trackAuthError(action: string, method: string, errorMessage: string) {
  mixpanel.track(`${action} Error`, {
    method,
    error: errorMessage
  })
}

/**
 * Sign in with email and password
 */
export async function signIn(formData: FormData): Promise<AuthResultType> {
  const supabase = await createClient()

  try {
    const email = formData.get('email') as string
    const password = formData.get('password') as string

    const { error, data } = await supabase.auth.signInWithPassword({
      email,
      password,
    })

    if (error) {
      trackAuthError('Login', 'email', error.message);
      const message = error instanceof AuthApiError ? 'Email atau kata sandi salah' : 'Terjadi kesalahan saat login';
      return { 
        error: error, 
        success: false,
        message: message
      }
    }

    if (data.user) {
      mixpanel.identify(data.user.id)
      mixpanel.track('User Logged In', {
        method: 'email',
        user_id: data.user.id,
        email: data.user.email
      })
      
      trackUserIdentity(
        data.user.id, 
        data.user.email || '', 
        data.user.user_metadata?.full_name
      )
    }

    revalidatePath('/', 'layout')
    return { error: null, success: true }
  } catch (error: any) {
    trackAuthError('Login', 'email', error.message)
    return { 
      error: error as Error, 
      success: false,
      message: 'Terjadi kesalahan saat login'
    }
  }
}

/**
 * Sign up with email and password
 */
export async function signUp(formData: FormData): Promise<AuthResultType> {
  const supabase = await createClient()

  try {
    const email = formData.get('email') as string
    const password = formData.get('password') as string
    const redirectTo = formData.get('redirectTo') as string || `${process.env.NEXT_PUBLIC_SITE_URL}/auth/callback`

    const { error, data } = await supabase.auth.signUp({
      email,
      password,
      options: {
        emailRedirectTo: redirectTo,
      },
    })

    if (error) {
      trackAuthError('Signup', 'email', error.message);
      const message = error instanceof AuthWeakPasswordError ? 'Kata sandi terlalu lemah. Harus terdiri dari minimal 8 karakter, termasuk huruf besar, huruf kecil, angka, dan simbol' : 'Terjadi kesalahan saat mendaftar'
      return { 
        error: error, 
        success: false,
        message: message
      }
    }

    if (data.user) {
      mixpanel.identify(data.user.id)
      mixpanel.track('User Signed Up', {
        method: 'email',
        user_id: data.user.id,
        email: data.user.email
      })
      
      mixpanel.people.set({
        $email: data.user.email,
        $created: new Date().toISOString()
      })
    }

    revalidatePath('/', 'layout')
    return { error: null, success: true }
  } catch (error: any) {
    trackAuthError('Signup', 'email', error.message)
    return { 
      error: error as Error, 
      success: false,
      message: 'Terjadi kesalahan saat mendaftar'
    }
  }
}

/**
 * Sign in with OAuth provider (Google, Facebook, etc.)
 */
export async function signInWithOAuth(formData: FormData): Promise<AuthResultType> {
  const supabase = await createClient()

  let provider: Provider = 'google'

  try {
    provider = formData.get('provider') as Provider
    const redirectTo = formData.get('redirectTo') as string || `${process.env.NEXT_PUBLIC_SITE_URL}/auth/callback`
    
    mixpanel.track('Social Login Attempt', { provider })
    
    const { data, error } = await supabase.auth.signInWithOAuth({
      provider,
      options: {
        redirectTo,
        queryParams: {
            access_type: 'offline',
            prompt: 'consent',
          },
      },
    })
    
    if (error) {
      mixpanel.track('Social Login Failed', {
        provider,
        error: error.message
      })
      return { 
        error: error, 
        success: false,
        message: `Gagal login dengan ${provider}`
      }
    }

    return { error: null, success: true, message: data.url }
  } catch (error: any) {
    trackAuthError('Social Login', provider, error.message);
    return { 
      error: error as Error, 
      success: false,
      message: `Gagal login dengan ${provider}`
    }
  }
}

/**
 * Sign out the current user
 */
export async function signOut(): Promise<AuthResultType> {
  const supabase = await createClient()
  
  try {
    await supabase.auth.signOut() // TODO: cek dokumentasi
    mixpanel.track('User Logged Out')
    mixpanel.reset()
    
    revalidatePath('/', 'layout')
    return { error: null, success: true }
  } catch (error: any) {
    return { 
      error: error as Error, 
      success: false,
      message: 'Gagal keluar dari akun'
    }
  }
}

/**
 * Reset password for email
 */
export async function resetPassword(formData: FormData): Promise<AuthResultType> {
  const supabase = await createClient()
  
  try {
    const email = formData.get('email') as string
    const redirectTo = formData.get('redirectTo') as string || `${process.env.NEXT_PUBLIC_SITE_URL}/reset-password`
    
    const { error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: redirectTo,
    })
    
    if (error) {
      mixpanel.track('Password Reset Failed', {
        email,
        error: error.message
      })
      return { 
        error: error, 
        success: false,
        message: 'Gagal reset password'
      }
    }
    
    mixpanel.track('Password Reset Requested', { email })
    return { error: null, success: true }
  } catch (error: any) {
    trackAuthError('Password Reset', 'email', error.message)
    return { 
      error: error as Error, 
      success: false,
      message: 'Terjadi kesalahan saat reset password'
    }
  }
}

/**
 * Update user password
 */
export async function updatePassword(formData: FormData): Promise<AuthResultType> {
  const supabase = await createClient()
  
  try {
    const password = formData.get('password') as string
    
    const { error } = await supabase.auth.updateUser({
      password: password,
    })
    
    if (error) {
      mixpanel.track('Password Update Failed', {
        error: error.message
      })
      const message = error instanceof AuthWeakPasswordError ? 'Kata sandi terlalu lemah. Harus terdiri dari minimal 8 karakter, termasuk huruf besar, huruf kecil, angka, dan simbol' : 'Terjadi kesalahan saat mengubah password'
      return { 
        error: error, 
        success: false,
        message: message
      }
    }
    
    mixpanel.track('Password Updated Successfully')
    revalidatePath('/', 'layout')
    return { error: null, success: true }
  } catch (error: any) {
    trackAuthError('Password Update', 'form', error.message);
    return { 
      error: error as Error, 
      success: false,
      message: 'Terjadi kesalahan saat mengubah password'
    }
  }
}
