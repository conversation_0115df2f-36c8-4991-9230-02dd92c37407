'use client';

import { apiFetch } from '@/lib/apiFetch';

import { useState, useEffect } from 'react';
import { useAnalytics } from '@/hooks/useAnalytics';
import Navbar from '@/components/Navbar';
import { useAuth } from '@/hooks/useAuth';
import Footer from '@/components/Footer';
import Seo from '@/components/Seo';

export default function FeedbackPage() {
  const auth = useAuth();
  const { trackEvent } = useAnalytics();
  const [rating, setRating] = useState<1|2|3|4|5|null>(null);
  const [comment, setComment] = useState('');
  const [email, setEmail] = useState('');
  const [featureType, setFeatureType] = useState<'general' | 'email-application' | 'application-letter' | 'job-match'>('general');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [error, setError] = useState('');
  
  // Track page view
  useEffect(() => {
    trackEvent('Feedback Page Viewed');
  }, []);

  // Handle feature selection change
  const handleFeatureChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setFeatureType(e.target.value as any);
  };

  // Handle rating selection
  const handleRatingClick = (value: 1|2|3|4|5) => {
    setRating(value);
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!rating) {
      setError('Harap pilih rating terlebih dahulu');
      return;
    }
    
    setError('');
    setIsSubmitting(true);
    
    try {
      const response = await apiFetch('/api/feedback', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          feature: featureType,
          rating,
          comment,
          email: email || null,
        }),
      });
      
      const data = await response.json();
      
      if (data.success) {
        setIsSubmitted(true);
        trackEvent('Feedback Submitted', {
          feature: featureType,
          rating,
          comment: comment || null,
          email: email || null,
        });
        // Reset form
        setRating(null);
        setComment('');
        setEmail('');
      } else {
        setError(data.error || 'Terjadi kesalahan saat mengirim masukan');
        trackEvent('Feedback Submission Failed', {
          feature: featureType,
          error: data.error || 'unknown_error'
        });
      }
    } catch (err) {
      console.error('Error submitting feedback:', err);
      setError('Gagal mengirim masukan. Silakan coba lagi.');
      trackEvent('Feedback Submission Error', {
        feature: featureType,
        error: err instanceof Error ? err.message : 'Unknown error'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <main className="min-h-screen flex flex-col pt-16">
      <Seo 
        title="Beri Masukan | Gigsta" 
        description="Beri masukan Anda untuk membantu kami meningkatkan layanan Gigsta"
      />
      <Navbar auth={auth} />
      
      <section className="py-12 flex-grow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-8">
            <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Beri Masukan Anda</h1>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              Kami menghargai pendapat Anda untuk terus meningkatkan dan mengembangkan layanan Gigsta. 
              Masukan Anda sangat berarti bagi kami.
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 gap-8">
            <div className="bg-white shadow-md rounded-lg p-6">
              <h2 className="text-xl font-semibold mb-4">Formulir Masukan</h2>
              {!isSubmitted && (
                <p className="text-gray-600 mb-6">
                  Silakan pilih fitur yang ingin Anda berikan masukan, lalu beri rating dan komentar Anda.
                </p>
              )}
              
              {isSubmitted ? (
                <div className="text-center py-8">
                  <svg className="w-16 h-16 text-green-500 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                  <h3 className="text-lg font-semibold mb-2">Terima Kasih!</h3>
                  <p className="text-gray-600 mb-4">Masukan Anda sangat berharga. Kami akan berusaha sebaik mungkin untuk meningkatkan layanan Gigsta berdasarkan masukan Anda.</p>
                  <button
                    type="button"
                    onClick={() => setIsSubmitted(false)}
                    className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark transition-colors"
                  >
                    Kirim Masukan Lainnya
                  </button>
                </div>
              ) : (
                <form onSubmit={handleSubmit}>
                  <div className="mb-6">
                    <label htmlFor="feature-select" className="block text-sm font-medium text-gray-700 mb-2">
                      Pilih Fitur
                    </label>
                    <div className="relative">
                      <select
                        id="feature-select"
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary appearance-none"
                        value={featureType}
                        onChange={handleFeatureChange}
                      >
                        <option value="general">Umum / Keseluruhan Aplikasi</option>
                        <option value="email-application">Email Lamaran</option>
                        <option value="application-letter">Surat Lamaran</option>
                        <option value="job-match">Kecocokan Lowongan</option>
                      </select>
                      <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
                        <svg className="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                          <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z" />
                        </svg>
                      </div>
                    </div>
                  </div>
                  
                  {error && (
                    <div className="mb-4 p-2 bg-red-50 text-red-600 rounded-md text-sm">
                      {error}
                    </div>
                  )}
                  
                  <div className="mb-6">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Rating
                    </label>
                    <div className="flex flex-wrap space-x-3 items-center">
                      {[1, 2, 3, 4, 5].map((value) => (
                        <button
                          key={value}
                          type="button"
                          onClick={() => handleRatingClick(value as 1|2|3|4|5)}
                          className={`w-10 h-10 rounded-full flex items-center justify-center ${
                            rating === value ? 'bg-primary text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                          } transition-colors`}
                        >
                          {value}
                        </button>
                      ))}
                      <span className="text-sm text-gray-500 ml-2">
                        {rating ? (
                          rating === 1 ? 'Sangat Kurang Baik' : 
                          rating === 2 ? 'Kurang Baik' : 
                          rating === 3 ? 'Cukup' : 
                          rating === 4 ? 'Baik' : 
                          rating === 5 ? 'Sangat Baik' : 'Pilih Rating'
                        ) : 'Pilih Rating'}
                      </span>
                    </div>
                  </div>
                  
                  <div className="mb-6">
                    <label htmlFor="comment" className="block text-sm font-medium text-gray-700 mb-2">
                      Komentar (Opsional)
                    </label>
                    <textarea
                      id="comment"
                      rows={4}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary"
                      placeholder="Bagikan saran atau komentar Anda..."
                      value={comment}
                      onChange={(e) => setComment(e.target.value)}
                    />
                  </div>
                  
                  <div className="mb-6">
                    <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                      Email untuk Follow-up (Opsional)
                    </label>
                    <input
                      type="email"
                      id="email"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary"
                      placeholder="<EMAIL>"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                    />
                    <p className="mt-1 text-xs text-gray-500">
                      Kami hanya akan menghubungi Anda jika perlu informasi lebih lanjut tentang masukan Anda
                    </p>
                  </div>
                  
                  <div className="flex justify-end">
                    <button
                      type="submit"
                      disabled={isSubmitting}
                      className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark transition-colors disabled:opacity-50"
                    >
                      {isSubmitting ? 'Mengirim...' : 'Kirim Masukan'}
                    </button>
                  </div>
                </form>
              )}
            </div>
            
            <div className="bg-white shadow-md rounded-lg p-6">
              <h2 className="text-xl font-semibold mb-4">Manfaat Masukan</h2>
              <div className="space-y-4">
                <div className="flex items-start">
                  <div className="flex-shrink-0 h-6 w-6 text-primary">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <p className="ml-3 text-gray-600">Membantu kami meningkatkan layanan Gigsta</p>
                </div>
                <div className="flex items-start">
                  <div className="flex-shrink-0 h-6 w-6 text-primary">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <p className="ml-3 text-gray-600">Menyampaikan kebutuhan dan harapan Anda</p>
                </div>
                <div className="flex items-start">
                  <div className="flex-shrink-0 h-6 w-6 text-primary">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <p className="ml-3 text-gray-600">Berkontribusi pada pengembangan fitur baru</p>
                </div>
                <div className="flex items-start">
                  <div className="flex-shrink-0 h-6 w-6 text-primary">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <p className="ml-3 text-gray-600">Membantu kami memberikan pengalaman yang lebih baik</p>
                </div>
              </div>
              
              <div className="mt-8 pt-6 border-t border-gray-200">
                <h3 className="text-lg font-medium mb-3">Kami Mendengarkan</h3>
                <p className="text-gray-600">
                  Setiap masukan yang Anda berikan akan kami tinjau dengan serius. Kami berkomitmen untuk terus meningkatkan layanan berdasarkan kebutuhan pengguna kami.                  
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>
      
      <Footer />
    </main>
  );
}
