import { Metadata } from 'next';
import { blogPosts, blogCategories } from '@/lib/blog-data';
import BlogPageClient from './BlogPageClient';

export const metadata: Metadata = {
  title: 'Blog Karir & Tips Kerja | Gigsta',
  description: 'Temukan tips karir, panduan menulis CV, strategi interview, dan insight industri terbaru untuk mengembangkan karir profesional Anda.',
  keywords: 'blog karir, tips kerja, CV, interview, surat lamaran, pengembangan karir, AI tools',
  openGraph: {
    title: 'Blog Karir & Tips Kerja | Gigsta',
    description: 'Temukan tips karir, panduan menulis CV, strategi interview, dan insight industri terbaru untuk mengembangkan karir profesional Anda.',
    type: 'website',
    url: 'https://gigsta.io/blog',
  }
};

export default function BlogPage() {
  const featuredPosts = blogPosts.filter(post => post.featured).slice(0, 3);

  return (
    <BlogPageClient
      blogPosts={blogPosts}
      blogCategories={blogCategories}
      featuredPosts={featuredPosts}
    />
  );
}
