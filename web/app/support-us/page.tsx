'use client';

import Navbar from '@/components/Navbar';
import { useAuth } from '@/hooks/useAuth';
import Footer from '@/components/Footer';
import Seo from '@/components/Seo';
import Link from 'next/link';
import { useCallback, useEffect } from 'react';
import { useAnalytics } from '@/hooks/useAnalytics';

export default function DukungKami() {
  const auth = useAuth();
  const { trackEvent } = useAnalytics();

  // Track page view when component mounts
  useEffect(() => {
    trackEvent('Support Us Page Viewed');
  }, [trackEvent]);
  
  const scrollToSupportOptions = useCallback((e: React.MouseEvent<HTMLAnchorElement>) => {
    e.preventDefault();
    const section = document.getElementById('support-options');
    if (section) {
      section.scrollIntoView({ behavior: 'smooth' });
    }
    trackEvent('Support Options Button Clicked');
  }, [trackEvent]);
  return (
    <main className="min-h-screen flex flex-col pt-16">
      <Seo 
        title="Dukung Kami - Gigsta"
        description="Dukung pengembangan Gigsta, platform AI untuk membuat surat lamaran, email lamaran, dan analisis kecocokan lowongan kerja."
        canonical="https://gigsta.io/dukung-kami"
      />
      <Navbar auth={auth} />
      
      {/* Hero Section - Split layout with side-by-side design */}
      <section className="bg-white py-12 border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 items-center">
            <div className="flex flex-col justify-center">
              <h2 className="text-3xl md:text-4xl font-bold mb-4 text-gray-900">
                Dukung Kami Memberikan <span className="text-primary">Layanan Terbaik</span>
              </h2>
              <p className="text-lg text-gray-600 mb-6">
                Kontribusi Anda membantu Gigsta mengembangkan teknologi AI yang lebih baik untuk membantu lebih banyak pencari kerja di Indonesia meraih impian mereka.
              </p>
              <div className="flex flex-col md:flex-row gap-4">
                <a
                  href="#support-options"
                  onClick={scrollToSupportOptions}
                  className="inline-block bg-primary hover:bg-blue-700 text-white text-center px-6 py-3 text-base font-semibold rounded-lg shadow-md transition-colors cursor-pointer"
                >
                  Cara Mendukung
                </a>
              </div>
            </div>
            <div className="relative w-full max-w-md mx-auto overflow-hidden rounded-lg shadow-xl bg-blue-50 py-8 px-6 border border-blue-100">
              <div className="absolute -right-16 -top-16 w-32 h-32 bg-blue-200 rounded-full opacity-50"></div>
              <div className="absolute -left-16 -bottom-16 w-32 h-32 bg-blue-200 rounded-full opacity-50"></div>
              <div className="z-10 text-center">
                <div className="inline-flex items-center justify-center w-20 h-20 rounded-full bg-primary bg-opacity-10 mb-4">
                  <svg className="w-10 h-10 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                  </svg>
                </div>
                <h3 className="text-2xl font-bold text-gray-900 mb-2">Bersama Kita Bisa</h3>
                <p className="text-gray-600">
                  Setiap dukungan, sekecil apapun, membantu kami meningkatkan layanan dan membantu lebih banyak pencari kerja di Indonesia.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Support Options Section */}
      <section id="support-options" className="py-16 bg-blue-50 scroll-mt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <span className="inline-block px-4 py-1 bg-blue-100 text-primary rounded-full text-sm font-semibold mb-2">Dukungan</span>
            <h2 className="text-3xl font-bold text-gray-900">Cara Anda Dapat Berkontribusi</h2>
            <p className="mt-4 text-lg text-gray-600 max-w-3xl mx-auto">Setiap bentuk dukungan membantu kami membangun ekosistem pekerjaan yang lebih baik di Indonesia</p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            {/* Donation Option */}
            <div className="bg-white rounded-xl p-8 shadow-md hover:shadow-xl transition-all border-t-4 border-primary">
              <div className="flex items-start mb-6">
                <div className="bg-blue-100 rounded-full p-3 mr-4">
                  <svg className="w-8 h-8 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                </div>
                <div className="text-left">
                  <h3 className="text-2xl font-semibold mb-2">Donasi</h3>
                  <p className="text-gray-500 text-sm">Dukung pengembangan teknologi Gigsta</p>
                </div>
              </div>
              <p className="text-gray-600 mb-6 text-left">
                Donasi Anda akan membantu kami meningkatkan kualitas layanan, memperluas fitur, dan membuat Gigsta dapat diakses oleh lebih banyak pencari kerja di Indonesia.
              </p>
              <div className="bg-gray-50 p-4 rounded-lg mb-6">
                <p className="text-sm text-gray-600 italic">"Setiap dukungan finansial membantu kami mengembangkan teknologi AI yang lebih baik untuk membantu pencari kerja."</p>
              </div>
              <a 
                href="https://trakteer.id/gigsta" 
                target="_blank" 
                rel="noopener noreferrer"
                className="inline-block bg-primary hover:bg-blue-700 text-white text-center px-6 py-3 text-base font-semibold rounded-lg shadow-md transition-colors cursor-pointer w-full"
                onClick={() => trackEvent('Donation Button Clicked', { location: 'support_options_section' })}
              >
                <span className="flex items-center justify-center">
                  <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                  </svg>
                  Donasi Sekarang
                </span>
              </a>
            </div>
            
            {/* Feedback Option */}
            <div className="bg-white rounded-xl p-8 shadow-md hover:shadow-xl transition-all border-t-4 border-blue-300">
              <div className="flex items-start mb-6">
                <div className="bg-blue-100 rounded-full p-3 mr-4">
                  <svg className="w-8 h-8 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z"></path>
                  </svg>
                </div>
                <div className="text-left">
                  <h3 className="text-2xl font-semibold mb-2">Beri Masukan</h3>
                  <p className="text-gray-500 text-sm">Bantu kami meningkatkan layanan</p>
                </div>
              </div>
              <p className="text-gray-600 mb-6 text-left">
                Bagikan pendapat, saran, atau pengalaman Anda menggunakan Gigsta. Masukan Anda sangat berharga bagi kami untuk terus meningkatkan layanan sesuai kebutuhan pengguna.
              </p>
              <div className="bg-gray-50 p-4 rounded-lg mb-6">
                <p className="text-sm text-gray-600 italic">"Masukan Anda membantu kami memahami kebutuhan pengguna dan mengembangkan fitur yang lebih baik."</p>
              </div>
              <Link
                href="/feedback"
                className="inline-block bg-white border border-gray-300 hover:bg-gray-50 text-gray-800 text-center px-6 py-3 text-base font-semibold rounded-lg shadow-sm transition-colors cursor-pointer w-full"
                onClick={() => trackEvent('Feedback Link Clicked', { location: 'support_options_section' })}
              >
                <span className="flex items-center justify-center">
                  <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                  </svg>
                  Kirim Masukan
                </span>
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Impact Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <span className="inline-block px-4 py-1 bg-blue-100 text-primary rounded-full text-sm font-semibold mb-2">Dampak</span>
            <h2 className="text-3xl font-bold text-gray-900">Dampak Dukungan Anda</h2>
            <p className="mt-4 text-lg text-gray-600 max-w-3xl mx-auto">Dengan dukungan Anda, kami dapat mencapai tujuan-tujuan berikut</p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* Impact 1 */}
            <div className="bg-blue-50 rounded-xl p-6 border border-blue-100 hover:shadow-md transition-all">
              <div className="flex items-center justify-center mb-4">
                <div className="w-14 h-14 relative">
                  <div className="absolute inset-0 bg-primary rounded-full opacity-10"></div>
                  <div className="absolute inset-2 bg-primary rounded-full opacity-20"></div>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <svg className="w-8 h-8 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                    </svg>
                  </div>
                </div>
              </div>
              <h3 className="text-xl font-semibold text-center mb-2 text-gray-900">Meningkatkan Teknologi AI</h3>
              <p className="text-gray-600 text-center">
                Mengembangkan algoritma AI yang lebih canggih untuk menghasilkan dokumen lamaran yang lebih relevan dan personal sesuai kebutuhan pengguna.
              </p>
              <div className="mt-4 text-center">
                <span className="inline-block bg-blue-100 text-primary text-xs px-3 py-1 rounded-full">Teknologi Lebih Baik</span>
              </div>
            </div>
            
            {/* Impact 2 */}
            <div className="bg-blue-50 rounded-xl p-6 border border-blue-100 hover:shadow-md transition-all">
              <div className="flex items-center justify-center mb-4">
                <div className="w-14 h-14 relative">
                  <div className="absolute inset-0 bg-primary rounded-full opacity-10"></div>
                  <div className="absolute inset-2 bg-primary rounded-full opacity-20"></div>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <svg className="w-8 h-8 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                    </svg>
                  </div>
                </div>
              </div>
              <h3 className="text-xl font-semibold text-center mb-2 text-gray-900">Memperluas Fitur</h3>
              <p className="text-gray-600 text-center">
                Mengembangkan fitur-fitur baru yang inovatif untuk meningkatkan manfaat dan pengalaman bagi para pencari kerja di Indonesia.
              </p>
              <div className="mt-4 text-center">
                <span className="inline-block bg-blue-100 text-primary text-xs px-3 py-1 rounded-full">Fitur Baru</span>
              </div>
            </div>
            
            {/* Impact 3 */}
            <div className="bg-blue-50 rounded-xl p-6 border border-blue-100 hover:shadow-md transition-all">
              <div className="flex items-center justify-center mb-4">
                <div className="w-14 h-14 relative">
                  <div className="absolute inset-0 bg-primary rounded-full opacity-10"></div>
                  <div className="absolute inset-2 bg-primary rounded-full opacity-20"></div>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <svg className="w-8 h-8 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                    </svg>
                  </div>
                </div>
              </div>
              <h3 className="text-xl font-semibold text-center mb-2 text-gray-900">Mendukung Pencari Kerja</h3>
              <p className="text-gray-600 text-center">
                Menjaga Gigsta tetap dapat diakses oleh semua orang dan membantu lebih banyak pencari kerja di Indonesia mendapatkan pekerjaan impian mereka.
              </p>
              <div className="mt-4 text-center">
                <span className="inline-block bg-blue-100 text-primary text-xs px-3 py-1 rounded-full">Akses Lebih Luas</span>
              </div>
            </div>
          </div>
        </div>
      </section>
      
      {/* Call To Action */}
      <section className="py-12 bg-gradient-to-r from-blue-600 to-indigo-700 text-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-2xl md:text-3xl font-bold mb-4">Bersama Kita Wujudkan Perubahan</h2>
          <p className="mb-8 text-blue-100">Setiap bentuk dukungan akan membantu menciptakan peluang karir yang lebih baik bagi jutaan pencari kerja di Indonesia</p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a 
              href="https://trakteer.id/gigsta" 
              target="_blank" 
              rel="noopener noreferrer"
              className="inline-block bg-white text-blue-700 hover:bg-blue-50 text-center px-6 py-3 text-base font-semibold rounded-lg shadow-md transition-colors cursor-pointer"
              onClick={() => trackEvent('Donation Button Clicked', { location: 'cta_section' })}
            >
              Donasi Sekarang
            </a>
            <Link
              href="/feedback"
              className="inline-block bg-blue-500 border border-white hover:bg-blue-600 text-white text-center px-6 py-3 text-base font-semibold rounded-lg shadow-md transition-colors cursor-pointer"
              onClick={() => trackEvent('Feedback Link Clicked', { location: 'cta_section' })}
            >
              Kirim Masukan
            </Link>
          </div>
        </div>
      </section>

      <Footer />
    </main>
  );
}
