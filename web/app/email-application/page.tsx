'use client';

import { useState, useEffect, useRef } from 'react';
import { useForm } from '@/hooks/useForm';
import { useResume } from '@/hooks/useResume';
import { useAnalytics } from '@/hooks/useAnalytics';
import { apiPost } from '@/lib/apiFetch';
import { useFeedbackTracker } from '@/hooks/useFeedbackTracker';
import Navbar from '@/components/Navbar';
import Footer from '@/components/Footer';
import Seo from '@/components/Seo';
import Toast from '@/components/Toast';
import JobInfoInput from '@/components/JobInfoInput';
import FeedbackForm from '@/components/FeedbackForm';
import IndependenceDayBanner from '@/components/IndependenceDayBanner';
import { EmailApplicationResult } from '@/utils/ai-generators/emailApplicationGenerator';
import { useAuth } from '@/hooks/useAuth';
import { InputMethod } from '@/types/InputMethod';
import { createToast } from '@/utils/notificationUtils';
import { useEmailHistory } from '@/hooks/useEmailHistory';
import { useStepNavigation, ThreeSteps } from '@/hooks/useStepNavigation';

// Icon Components
const CheckIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
  </svg>
);

const ArrowRightIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
  </svg>
);

const ArrowLeftIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h17" />
  </svg>
);

const XIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
  </svg>
);

// Step Indicator Component
function StepIndicator({ step, currentStep, label }: { step: number; currentStep: number; label: string }) {
  const isActive = step === currentStep;
  const isCompleted = step < currentStep;

  return (
    <div className="flex flex-col items-center gap-1 sm:gap-2 relative text-center min-w-0 flex-1">
      <div
        className={`w-8 h-8 sm:w-10 sm:h-10 flex items-center justify-center rounded-full border-2 font-bold transition-all duration-300 flex-shrink-0 text-xs sm:text-base ${
          isCompleted ? "bg-primary border-primary text-white" : isActive ? "border-primary text-primary scale-110" : "border-gray-300 text-gray-400"
        }`}
      >
        {isCompleted ? <CheckIcon className="w-4 h-4 sm:w-6 sm:h-6" /> : step}
      </div>
      <span className={`font-medium text-[10px] sm:text-xs md:text-sm leading-tight text-center max-w-[60px] sm:max-w-none ${isActive || isCompleted ? "text-gray-900" : "text-gray-500"}`}>{label}</span>
    </div>
  );
}

// Email History Modal Component
function EmailHistoryModal({ isOpen, onClose }: { isOpen: boolean; onClose: () => void }) {
  const auth = useAuth();
  const { emails: emailHistory, isLoading: isLoadingHistory, error: historyError, refetch: refetchHistory } = useEmailHistory(auth);
  const [expandedEmails, setExpandedEmails] = useState<Set<string>>(new Set());
  const [currentHistoryPage, setCurrentHistoryPage] = useState(1);
  
  const emailsPerPage = 5;
  const totalHistoryPages = Math.ceil(emailHistory.length / emailsPerPage);
  const startIndex = (currentHistoryPage - 1) * emailsPerPage;
  const endIndex = startIndex + emailsPerPage;
  const currentEmails = emailHistory.slice(startIndex, endIndex);

  const handlePageChange = (newPage: number) => {
    setCurrentHistoryPage(newPage);
  };

  const toggleEmailExpansion = (emailId: string) => {
    setExpandedEmails(prev => {
      const newSet = new Set(prev);
      if (newSet.has(emailId)) {
        newSet.delete(emailId);
      } else {
        newSet.add(emailId);
      }
      return newSet;
    });
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto bg-black bg-opacity-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden flex flex-col">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex flex-row items-center justify-between gap-4">
            <h3 className="text-lg font-semibold text-gray-900">Riwayat Email Lamaran</h3>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100 p-2"
            >
              <XIcon className="w-6 h-6" />
            </button>
          </div>
        </div>

        <div className="flex-1 overflow-y-auto p-6">
          {isLoadingHistory ? (
            <div className="flex justify-center py-8">
              <svg className="animate-spin h-8 w-8 text-primary" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 714 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
            </div>
          ) : historyError ? (
            <div className="text-center py-8 text-red-600">
              <p>Gagal memuat riwayat email: {historyError}</p>
              <button
                onClick={refetchHistory}
                className="mt-2 text-primary hover:text-primary-dark text-sm underline"
              >
                Coba lagi
              </button>
            </div>
          ) : emailHistory.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <svg className="w-12 h-12 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
              </svg>
              <p>Belum ada email lamaran yang dibuat</p>
              <p className="text-sm mt-1">Email yang Anda buat akan muncul di sini</p>
            </div>
          ) : (
            <>
              <div className="space-y-4">
                {currentEmails.map((email) => {
                  const isExpanded = expandedEmails.has(email.id);
                  const previewLength = 120;
                  const shouldShowMore = email.body.length > previewLength;
                  const displayText = isExpanded ? email.body : email.body.substring(0, previewLength);

                  return (
                    <div key={email.id} className="bg-gradient-to-r from-slate-50 to-gray-50 border-l-4 border-l-blue-400 rounded-r-lg p-4 hover:shadow-sm transition-all duration-200 hover:from-slate-100 hover:to-gray-100">
                      {/* Header with date */}
                      <div className="flex items-center justify-between mb-4">
                        <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                          <svg className="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                          </svg>
                        </div>
                        <p className="text-xs text-gray-500">
                          {new Date(email.createdAt).toLocaleDateString('id-ID', {
                            day: '2-digit',
                            month: 'short',
                            year: 'numeric',
                            hour: '2-digit',
                            minute: '2-digit'
                          })}
                        </p>
                      </div>

                      {/* Subject Section */}
                      <div className="mb-3">
                        <div className="flex justify-between items-center mb-2">
                          <label className="text-xs font-medium text-gray-600 uppercase tracking-wide">Subjek:</label>
                          <button
                            className="px-2 py-1 text-gray-500 hover:text-blue-600 hover:bg-blue-50 rounded transition-colors flex items-center gap-1"
                            onClick={() => {
                              navigator.clipboard.writeText(email.subject);
                              alert('Subjek email disalin ke clipboard!');
                            }}
                            title="Salin Subjek"
                          >
                            <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3"></path>
                            </svg>
                            <span className="text-xs font-medium">Salin</span>
                          </button>
                        </div>
                        <div className="bg-white/70 backdrop-blur-sm p-2.5 rounded border border-blue-200/50 text-sm font-medium text-gray-800">
                          {email.subject}
                        </div>
                      </div>

                      {/* Body Section */}
                      <div>
                        <div className="flex justify-between items-center mb-2">
                          <label className="text-xs font-medium text-gray-600 uppercase tracking-wide">Isi Email:</label>
                          <button
                            className="px-2 py-1 text-gray-500 hover:text-blue-600 hover:bg-blue-50 rounded transition-colors flex items-center gap-1"
                            onClick={() => {
                              navigator.clipboard.writeText(email.body);
                              alert('Isi email disalin ke clipboard!');
                            }}
                            title="Salin Isi Email"
                          >
                            <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3"></path>
                            </svg>
                            <span className="text-xs font-medium">Salin</span>
                          </button>
                        </div>
                        <div className="bg-white/70 backdrop-blur-sm p-3 rounded border border-blue-200/50 min-h-[100px]">
                          <pre className="whitespace-pre-wrap text-xs text-gray-700 leading-relaxed">
                            {displayText}
                            {!isExpanded && shouldShowMore && (
                              <span className="text-gray-400">...</span>
                            )}
                          </pre>
                          {shouldShowMore && (
                            <button
                              onClick={() => toggleEmailExpansion(email.id)}
                              className="mt-2 text-xs text-blue-600 hover:text-blue-800 font-medium focus:outline-none flex items-center gap-1"
                            >
                              {isExpanded ? (
                                <>
                                  <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 15l7-7 7 7"></path>
                                  </svg>
                                  Tampilkan lebih sedikit
                                </>
                              ) : (
                                <>
                                  <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7"></path>
                                  </svg>
                                  Tampilkan selengkapnya
                                </>
                              )}
                            </button>
                          )}
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>

              {/* Pagination Controls */}
              {totalHistoryPages > 1 && (
                <div className="mt-6 pt-4 border-t border-gray-200">
                  <div className="text-sm text-gray-600 text-center mb-4">
                    Menampilkan {startIndex + 1}-{Math.min(endIndex, emailHistory.length)} dari {emailHistory.length} email
                  </div>
                  <div className="flex items-center justify-center space-x-1">
                    <button
                      onClick={() => handlePageChange(Math.max(currentHistoryPage - 1, 1))}
                      disabled={currentHistoryPage === 1}
                      className="px-2 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      ‹
                    </button>

                    {Array.from({ length: totalHistoryPages }, (_, i) => i + 1).map(page => (
                      <button
                        key={page}
                        onClick={() => handlePageChange(page)}
                        className={`px-3 py-2 text-sm font-medium rounded-md ${
                          currentHistoryPage === page
                            ? 'bg-primary text-white'
                            : 'text-gray-500 bg-white border border-gray-300 hover:bg-gray-50'
                        }`}
                      >
                        {page}
                      </button>
                    ))}

                    <button
                      onClick={() => handlePageChange(Math.min(currentHistoryPage + 1, totalHistoryPages))}
                      disabled={currentHistoryPage === totalHistoryPages}
                      className="px-2 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      ›
                    </button>
                  </div>
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  );
}

export default function EmailApplicationPage() {
  const auth = useAuth();
  const { trackEvent, logError } = useAnalytics();
  const { user, signIn, signUp, signInWithGoogle } = auth;
  
  // Wizard state
  const [showHistoryModal, setShowHistoryModal] = useState(false);
  
  // Ref for step indicator area
  const stepIndicatorRef = useRef<HTMLDivElement>(null);
  
  // Step navigation with browser history support
  const stepNav = useStepNavigation<ThreeSteps>({
    initialStep: 1,
    totalSteps: 3,
    stepParam: 'email-step',
    onStepValidation: async (currentStep, targetStep) => {
      if (currentStep === 1 && targetStep === 2) {
        // Validate resume upload before proceeding to step 2
        if (!uploadSuccess || !existingResume) {
          setApplicationError('Harap unggah resume terlebih dahulu');
          return false;
        }
      }
      if (currentStep === 2 && targetStep === 3) {
        // Validate job info before proceeding to step 3
        const { jobDescription, jobImage } = form.values;
        if (inputMethod === InputMethod.TEXT && !jobDescription) {
          setApplicationError('Harap isi informasi lowongan');
          return false;
        }
        if (inputMethod === InputMethod.IMAGE && !jobImage) {
          setApplicationError('Harap unggah poster lowongan');
          return false;
        }
      }
      return true;
    },
    onStepChange: (newStep, prevStep) => {
      // Clear any previous errors when changing steps
      setApplicationError('');
      
      // Track step navigation
      trackEvent('Step Navigation', {
        from: prevStep,
        to: newStep,
        feature: 'email-application'
      });
      
      // Scroll to step indicator if user is scrolled below it
      if (stepIndicatorRef.current) {
        const stepIndicatorRect = stepIndicatorRef.current.getBoundingClientRect();
        const isStepIndicatorAboveViewport = stepIndicatorRect.top < 0;
        
        if (isStepIndicatorAboveViewport) {
          stepIndicatorRef.current.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
          });
        }
      }
    },
  });
  
  // Track page view
  useEffect(() => {
    trackEvent('Email Application Page Viewed');
  }, []);
  
  // Use the resume hook
  const {
    isUploading,
    uploadSuccess,
    error,
    toast,
    existingResume,
    isLoading,
    isDeleting,
    isGettingResumeUrl,
    setToast,
    handleResumeUpload,
    handleViewResume,
    handleDeleteResume
  } = useResume(auth);
  
  // Form state
  const form = useForm<{
    jobDescription: string;
    jobImage: File | null;
  }>({
    jobDescription: '',
    jobImage: null,
  });
  
  const [emailSubject, setEmailSubject] = useState('');
  const [originalEmailSubject, setOriginalEmailSubject] = useState('');
  const [isEmailSubjectEditable, setIsEmailSubjectEditable] = useState(false);
  const [emailBody, setEmailBody] = useState('');
  const [originalEmailBody, setOriginalEmailBody] = useState('');
  const [isEmailBodyEditable, setIsEmailBodyEditable] = useState(false);
  const [currentEmailId, setCurrentEmailId] = useState<string | null>(null);
  const [isSavingEmail, setIsSavingEmail] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);
  const [inputMethod, setInputMethod] = useState<InputMethod>(InputMethod.TEXT);
  
  // Create a local state variable for application-specific errors
  const [applicationError, setApplicationError] = useState('');
  
  // Use feedback tracker
  const { shouldShowFeedback, markFeedbackShown, incrementFeatureUsage } = useFeedbackTracker('email-application');
  const [showFeedback, setShowFeedback] = useState(false);

  // Login/Register modal state
  const [showLoginModal, setShowLoginModal] = useState(false);
  const [loginMode, setLoginMode] = useState<'login' | 'register'>('register');
  const [loginForm, setLoginForm] = useState({ email: '', password: '', confirmPassword: '' });
  const [loginError, setLoginError] = useState('');
  const [loginSuccess, setLoginSuccess] = useState('');
  const [isLoggingIn, setIsLoggingIn] = useState(false);


  // Handle user login completion (for OAuth flow)
  useEffect(() => {
    if (user && showLoginModal) {
      // User just logged in, close modal
      setShowLoginModal(false);
      setLoginForm({ email: '', password: '', confirmPassword: '' });
      setLoginError('');
      setLoginSuccess('');
      // Show success toast when OAuth login completes
      setToast(createToast('Berhasil masuk', 'success'));
    }
  }, [user, showLoginModal]);

  // Handle login form submission
  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoggingIn(true);
    setLoginError('');
    setLoginSuccess('');

    try {
      // For registration, check if passwords match
      if (loginMode === 'register' && loginForm.password !== loginForm.confirmPassword) {
        setLoginError('Password tidak cocok');
        setIsLoggingIn(false);
        return;
      }

      const result = loginMode === 'login'
        ? await signIn(loginForm.email, loginForm.password)
        : await signUp(loginForm.email, loginForm.password);

      if (result.success) {
        if (loginMode === 'register') {
          // Show email confirmation message and switch to login mode
          setLoginMode('login');
          setLoginForm({ email: loginForm.email, password: '', confirmPassword: '' });
          // Show success message for registration
          setLoginSuccess('Pendaftaran berhasil! Silakan periksa email Anda untuk konfirmasi, lalu kembali ke tab ini untuk masuk');
        } else {
          // For login success
          setShowLoginModal(false);
          setLoginForm({ email: '', password: '', confirmPassword: '' });
          // Show success toast
          setToast(createToast('Berhasil masuk', 'success'));
        }
      } else {
        setLoginError(result.message || 'Terjadi kesalahan saat masuk');
      }
    } catch (err) {
      setLoginError('Terjadi kesalahan saat masuk');
    } finally {
      setIsLoggingIn(false);
    }
  };

  // Handle Google login
  const handleGoogleLogin = async () => {
    setIsLoggingIn(true);
    try {
      const result = await signInWithGoogle(encodeURIComponent(window.location.pathname));
      if (result.success && result.message) {
        // Open Google OAuth in new tab to preserve context
        window.open(result.message, '_blank');
        setLoginSuccess('Silakan selesaikan login di tab baru, lalu kembali ke halaman ini');
      }
      if (result.error) {
        if (result.message) {
          setLoginError(result.message);
        } else {
          throw result.error;
        }
      }
    } catch (error: any) {
      setLoginError('Gagal login dengan Google');
    } finally {
      setIsLoggingIn(false);
    }
  };

  // Handle saving email changes in the result section
  const saveCurrentEmailChanges = async () => {
    if (!user || !currentEmailId) return;

    setIsSavingEmail(true);
    try {
      const response = await apiPost('/api/update-email', {
        emailId: currentEmailId,
        subject: emailSubject,
        body: emailBody
      });

      const data = await response.json();
      if (data.success) {
        // Update original values to reflect saved state
        setOriginalEmailSubject(emailSubject);
        setOriginalEmailBody(emailBody);

        // Show success message
        setToast(createToast('Email berhasil diperbarui', 'success'));

        // Track successful update
        trackEvent('Email Updated Successfully', {
          emailId: currentEmailId,
          subject_length: emailSubject.length,
          body_length: emailBody.length
        });
      } else {
        setToast(createToast(data.error || 'Gagal memperbarui email', 'error'));

        // Track failed update
        trackEvent('Email Update Failed', {
          emailId: currentEmailId,
          error: data.error || 'unknown_error'
        });
      }
    } catch (err) {
      console.error('Error updating email:', err);
      setToast(createToast('Gagal memperbarui email. Silakan coba lagi.', 'error'));

      // Track error
      trackEvent('Email Update Exception', {
        emailId: currentEmailId,
        error: err instanceof Error ? err.message : 'Unknown error'
      });
    } finally {
      setIsSavingEmail(false);
    }
  };

  // Handle job poster image upload
  const handleJobImageChange = (file: File | null) => {
    // Store the image file reference
    form.setValues(prev => ({ ...prev, jobImage: file }));

    // Clear any previous errors
    setApplicationError('');
  };

  // Step navigation functions - now using the hook
  const goNext = stepNav.goNext;
  const goBack = stepNav.goBack;

  const generateEmail = async () => {
    // Check if user is logged in first
    if (!user) {
      setLoginMode('register');
      setShowLoginModal(true);
      return;
    }

    // Increment feature usage count
    incrementFeatureUsage();

    // Set feedback form visibility based on usage count
    setShowFeedback(shouldShowFeedback);

    // Reset the email results when submitting new request
    setEmailSubject('');
    setEmailBody('');

    // Reset editability state
    setIsEmailSubjectEditable(false);
    setIsEmailBodyEditable(false);

    if (!existingResume) {
      setApplicationError('Harap unggah resume terlebih dahulu');
      trackEvent('Email Application Error', { error: 'no_resume' });
      return;
    }

    const { jobDescription, jobImage } = form.values;
    // Check that either a job description text or job image is provided
    if (inputMethod === InputMethod.TEXT && !jobDescription) {
      setApplicationError('Harap isi informasi lowongan');
      trackEvent('Job Match Error', { error: 'incomplete_form_text' });
      return;
    }
    if (inputMethod === InputMethod.IMAGE && !jobImage) {
      setApplicationError('Harap unggah poster lowongan');
      trackEvent('Job Match Error', { error: 'incomplete_form_image' });
      return;
    }
    
    // Track email generation attempt
    trackEvent('Email Application Generation Attempt', {
      description_length: jobDescription.length,
      used_image: !!jobImage
    });

    setApplicationError('');

    // Move to step 3 immediately, then start generating
    await stepNav.goToStep(3);
    setIsGenerating(true);

    try {
      const body = {
        ...(inputMethod === InputMethod.TEXT ? { jobDescription } : {}),
        ...(inputMethod === InputMethod.IMAGE ? { jobImage } : {}),
        ...(existingResume?.unauthenticatedResumeFile && existingResume?.fileName && { 
          unauthenticatedResumeFile: existingResume.unauthenticatedResumeFile,
          unauthenticatedResumeFileName: existingResume.fileName
        })
      };

      const response = await apiPost('/api/generate-email-application', body);

      const data = await response.json();
      if (data.success) {
        const emailApplication: EmailApplicationResult = data.emailApplication;
        setEmailSubject(emailApplication.subject);
        setEmailBody(emailApplication.body);
        setCurrentEmailId(data.emailId || null);

        // Track successful generation
        trackEvent('Email Application Generated Successfully', {
          subject_length: emailApplication.subject.length,
          body_length: emailApplication.body.length,
          used_image: !!jobImage
        });
      } else {
        setApplicationError(data.error || 'Terjadi kesalahan saat membuat email lamaran');
        
        // Track failed generation
        trackEvent('Email Application Generation Failed', {
          error: data.error || 'unknown_error'
        });
      }
    } catch (err) {
      setApplicationError('Gagal membuat email lamaran. Silakan coba lagi.');
      console.error(err);
      
      // Log error to Rollbar
      logError(err instanceof Error ? err : new Error('Failed to generate email application'), {
        feature: 'email_application',
        action: 'generate',
        description_length: form.values.jobDescription.length
      }, 'error');
      
      // Track detailed analytics about the failure
      trackEvent('Email Application Generation Exception', {
        error: err instanceof Error ? err.message : 'Unknown error',
        error_type: err instanceof Error ? err.constructor.name : 'Unknown'
      });
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <main className="min-h-screen flex flex-col pt-16">
      <Toast 
        show={toast.show} 
        message={toast.message} 
        type={toast.type} 
        onClose={() => setToast({ ...toast, show: false })} 
      />
      <Seo 
        title="Buat Email Lamaran Kerja Otomatis dengan AI"
        description="Permudah proses lamaran kerja online dengan email lamaran kerja yang dipersonalisasi. Tingkatkan respon HR dan dapatkan undangan interview lebih cepat dengan Gigsta."
        canonical="https://gigsta.io/email-application"
      />
      <Navbar auth={auth} />

      {/* Sticky Banner for Unauthenticated Users */}
      {!auth.loading && !user && (
        <div className="sticky top-[4.5rem] z-10 bg-gradient-to-r from-purple-600 via-pink-600 to-red-600 text-white py-2 sm:py-3 px-4 shadow-lg">
          <div className="max-w-7xl mx-auto flex items-center justify-between gap-3">
            <div className="flex items-center space-x-2 sm:space-x-3 min-w-0 flex-1">
              <div className="flex items-center space-x-1 sm:space-x-2 flex-shrink-0">
                <div className="relative">
                  <svg className="w-4 h-4 sm:w-5 sm:h-5 text-yellow-300 animate-spin" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 2L13.09 8.26L22 9L13.09 9.74L12 16L10.91 9.74L2 9L10.91 8.26L12 2Z"/>
                  </svg>
                  <div className="absolute inset-0 w-4 h-4 sm:w-5 sm:h-5 bg-yellow-300 rounded-full opacity-20 animate-ping"></div>
                </div>
                <span className="font-bold text-sm sm:text-base">🎉</span>
              </div>
              <div className="flex flex-col sm:flex-row sm:items-center sm:space-x-2 min-w-0">
                <span className="font-bold text-xs sm:text-sm md:text-base leading-tight">
                  BONUS 10 TOKEN GRATIS!
                </span>
                <span className="text-xs sm:text-sm opacity-90 leading-tight">
                  untuk pengguna baru yang mendaftar
                </span>
              </div>
            </div>
            <button
              onClick={() => {
                setLoginMode('register');
                setShowLoginModal(true)
              }}
              className="bg-white text-purple-600 hover:bg-yellow-100 font-bold py-1.5 px-3 sm:py-2 sm:px-4 rounded-full text-xs sm:text-sm text-center transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl flex-shrink-0"
            >
              Daftar Sekarang
            </button>
          </div>
        </div>
      )}

      {/* Independence Day Banner for Authenticated Users */}
      <IndependenceDayBanner />

      {/* Feedback Modal */}
      {showFeedback && (
        <div className="fixed inset-0 z-50 overflow-y-auto bg-black bg-opacity-50 flex items-center justify-center p-4">
          <FeedbackForm
            featureType="email-application"
            onClose={() => {
              setShowFeedback(false);
              markFeedbackShown();
            }}
          />
        </div>
      )}

      {/* Login/Register Modal */}
      {showLoginModal && (
        <div className="fixed inset-0 z-50 overflow-y-auto bg-black bg-opacity-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
            <div className="px-6 py-4 border-b border-gray-200">
              <div className="flex flex-row items-center justify-between gap-4">
                <h3 className="text-lg font-medium text-gray-900">
                  {loginMode === 'login' ? 'Masuk ke Akun' : 'Daftar Akun Baru'}
                </h3>
                <button
                  onClick={() => {
                    setShowLoginModal(false);
                    setLoginError('');
                    setLoginSuccess('');
                    setLoginForm({ email: '', password: '', confirmPassword: '' });
                  }}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <XIcon className="w-6 h-6" />
                </button>
              </div>
            </div>

            <div className="px-6 py-4">
              <p className="text-sm text-gray-600 mb-4">
                {loginMode === 'login'
                  ? 'Masuk untuk membuat email lamaran Anda'
                  : 'Daftar untuk membuat email lamaran Anda'
                }
              </p>

              {loginSuccess && (
                <div className="mb-4 p-3 bg-green-100 border border-green-200 text-green-700 rounded-md text-sm">
                  {loginSuccess}
                </div>
              )}

              {loginError && (
                <div className="mb-4 p-3 bg-red-100 border border-red-200 text-red-700 rounded-md text-sm">
                  {loginError}
                </div>
              )}

              <form onSubmit={handleLogin} className="space-y-4">
                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                    Email
                  </label>
                  <input
                    type="email"
                    id="email"
                    value={loginForm.email}
                    onChange={(e) => setLoginForm(prev => ({ ...prev, email: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    required
                    disabled={isLoggingIn}
                  />
                </div>

                <div>
                  <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
                    Password
                  </label>
                  <input
                    type="password"
                    id="password"
                    value={loginForm.password}
                    onChange={(e) => setLoginForm(prev => ({ ...prev, password: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    required
                    disabled={isLoggingIn}
                  />
                </div>

                {loginMode === 'register' && (
                  <div>
                    <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700 mb-1">
                      Konfirmasi Password
                    </label>
                    <input
                      type="password"
                      id="confirmPassword"
                      value={loginForm.confirmPassword}
                      onChange={(e) => setLoginForm(prev => ({ ...prev, confirmPassword: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      required
                      disabled={isLoggingIn}
                    />
                  </div>
                )}

                <button
                  type="submit"
                  disabled={isLoggingIn}
                  className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isLoggingIn ? (
                    <>
                      <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      {loginMode === 'login' ? 'Masuk...' : 'Mendaftar...'}
                    </>
                  ) : (
                    loginMode === 'login' ? 'Masuk' : 'Daftar'
                  )}
                </button>
              </form>

              <div className="mt-4">
                <div className="relative">
                  <div className="absolute inset-0 flex items-center">
                    <div className="w-full border-t border-gray-300" />
                  </div>
                  <div className="relative flex justify-center text-sm">
                    <span className="px-2 bg-white text-gray-500">atau</span>
                  </div>
                </div>

                <button
                  onClick={handleGoogleLogin}
                  disabled={isLoggingIn}
                  className="mt-3 w-full flex justify-center items-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <svg className="w-5 h-5 mr-2" viewBox="0 0 24 24">
                    <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                    <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                    <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                    <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                  </svg>
                  Masuk dengan Google
                </button>
              </div>

              <div className="mt-4 text-center">
                <button
                  onClick={() => {
                    setLoginMode(loginMode === 'login' ? 'register' : 'login');
                    setLoginError('');
                    setLoginSuccess('');
                  }}
                  className="text-sm text-blue-600 hover:text-blue-500"
                  disabled={isLoggingIn}
                >
                  {loginMode === 'login'
                    ? 'Belum punya akun? Daftar di sini'
                    : 'Sudah punya akun? Masuk di sini'
                  }
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Email History Modal */}
      <EmailHistoryModal isOpen={showHistoryModal} onClose={() => setShowHistoryModal(false)} />

      <section className="py-12 flex-grow">
        <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-gray-900">Buat Email Lamaran</h1>
            <p className="mt-4 text-lg text-gray-600">
              Dapatkan email lamaran kerja profesional yang dipersonalisasi berdasarkan CV/resume dan informasi pekerjaan Anda—tingkatkan peluang Anda untuk lolos seleksi dengan satu klik!
            </p>
          </div>

          {/* Step Indicator */}
          <div ref={stepIndicatorRef} className="flex items-start justify-center mb-6 sm:mb-8 relative px-4">
            <div className="flex items-start justify-between w-full max-w-2xl">
              <StepIndicator step={1} currentStep={stepNav.currentStep} label="Upload Resume" />
              <div className={`flex-1 h-0.5 mx-1 sm:mx-2 mt-4 sm:mt-5 ${stepNav.currentStep > 1 ? 'bg-primary' : 'bg-gray-300'}`}></div>
              <StepIndicator step={2} currentStep={stepNav.currentStep} label="Info Pekerjaan" />
              <div className={`flex-1 h-0.5 mx-1 sm:mx-2 mt-4 sm:mt-5 ${stepNav.currentStep > 2 ? 'bg-primary' : 'bg-gray-300'}`}></div>
              <StepIndicator step={3} currentStep={stepNav.currentStep} label="Hasil Email" />
            </div>
          </div>
          
          <div className="bg-white shadow-md rounded-lg p-6 mb-8">
            {/* Step 1: Resume Upload */}
            {stepNav.currentStep === 1 && (
              <div>
                <h2 className="text-xl font-semibold mb-4">1. Upload CV/Resume Anda</h2>
                
                {isLoading ? (
                  <div className="flex justify-center py-8">
                    <svg className="animate-spin h-8 w-8 text-primary" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                  </div>
                ) : uploadSuccess && existingResume ? (
                  <div className="border-2 border-green-200 rounded-lg p-4 sm:p-6 bg-green-50">
                    <div className="flex items-center mb-3">
                      <svg className="w-6 h-6 text-green-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                      </svg>
                      <h3 className="font-medium text-green-700">Resume aktif tersedia!</h3>
                    </div>
                    
                    <div className="mb-4">
                      <p className="text-gray-700 mb-1 break-words">
                        File: <span className="font-mono text-sm">{existingResume.fileName.split('_').pop()}</span>
                      </p>
                      <p className="text-sm text-gray-500">
                        Diunggah pada: {new Date(existingResume.uploadedAt).toLocaleDateString('id-ID', { 
                          day: 'numeric', 
                          month: 'long', 
                          year: 'numeric',
                          hour: '2-digit',
                          minute: '2-digit'
                        })}
                      </p>
                    </div>
                    
                    <div className="flex flex-col sm:flex-row gap-3 sm:gap-4">
                      {isGettingResumeUrl ? (
                        <div className="inline-flex items-center text-sm text-primary w-fit">
                          <svg className="animate-spin h-4 w-4 mr-1 flex-shrink-0" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                          Memuat...
                        </div>
                      ) : (
                        <button 
                          onClick={handleViewResume}
                          className="inline-flex items-center text-sm text-primary hover:underline bg-transparent border-0 p-0 cursor-pointer w-fit"
                        >
                          <svg className="h-4 w-4 mr-1 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                          </svg>
                          Lihat Resume
                        </button>
                      )}
                      
                      {isUploading ? (
                        <div className="inline-flex items-center text-sm text-gray-800 w-fit">
                          <svg className="animate-spin h-4 w-4 mr-1 flex-shrink-0" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                          Mengunggah...
                        </div>
                      ) : (
                        <label
                          htmlFor="resume-existing"
                          className="inline-flex items-center text-sm text-gray-600 hover:text-gray-800 hover:underline cursor-pointer w-fit"
                        >
                          <svg className="h-4 w-4 mr-1 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12"></path>
                          </svg>
                          Unggah Resume Baru
                        </label>
                      )}
                      
                      {isDeleting ? (
                        <div className="inline-flex items-center text-sm text-red-600 w-fit">
                          <svg className="animate-spin h-4 w-4 mr-1 flex-shrink-0" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                          Menghapus...
                        </div>
                      ) : (
                        <button
                          onClick={handleDeleteResume}
                          className="inline-flex items-center text-sm text-red-600 hover:text-red-800 hover:underline w-fit"
                          type="button"
                        >
                          <svg className="h-4 w-4 mr-1 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                          </svg>
                          Hapus Resume
                        </button>
                      )}
                      
                      <input
                        type="file"
                        id="resume-existing"
                        accept=".pdf,.docx,.png,.jpg,.jpeg"
                        className="hidden"
                        onChange={handleResumeUpload}
                        ref={(input) => {
                          if (input) {
                            input.value = '';
                          }
                        }}
                      />
                    </div>
                  </div>
                ) : (
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                    <input
                      type="file"
                      id="resume-new"
                      accept=".pdf,.docx,.png,.jpg,.jpeg"
                      className="hidden"
                      onChange={handleResumeUpload}
                      disabled={isUploading}
                      ref={(input) => {
                        if (input) {
                          input.value = '';
                        }
                      }}
                    />
                    <label
                      htmlFor="resume-new"
                      className={`cursor-pointer flex flex-col items-center justify-center`}
                    >
                      {isUploading ? (
                        <svg className="animate-spin h-12 w-12 text-primary mb-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                      ) : (
                        <svg className="w-12 h-12 text-gray-400 mb-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                        </svg>
                      )}
                      {isUploading ? (
                        <p className="text-gray-700 font-medium">Mengunggah Resume...</p>
                      ) : (
                        <>
                          <p className="text-gray-700 font-medium">Klik untuk mengunggah resume Anda</p>
                          <p className="text-sm text-gray-500 mt-1">PDF, DOCX, PNG, JPG, atau JPEG (Maks. 5MB)</p>
                        </>
                      )}
                    </label>
                  </div>
                )}
                
                {error && (
                  <div className="mt-4 p-3 bg-red-100 text-red-700 rounded-lg">
                    {error}
                  </div>
                )}

                {/* Info catatan jika user tidak login dan sudah upload resume */}
                {!user && existingResume && (
                  <p className="mt-2 text-xs text-gray-500">
                    *File CV/resume Anda disimpan secara lokal dan akan hilang jika halaman di-refresh. <b>Masuk</b> agar CV/resume tetap tersimpan sehingga tidak perlu mengunggah ulang
                  </p>
                )}

                {/* Navigation */}
                <div className="flex justify-end mt-6">
                  <button
                    onClick={goNext}
                    className="inline-flex items-center justify-center gap-2 bg-primary hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-semibold shadow-sm transition-colors"
                  >
                    <span>Selanjutnya</span>
                    <ArrowRightIcon className="w-5 h-5" />
                  </button>
                </div>

                {/* Error message */}
                {applicationError && (
                  <div className="p-3 mt-6 text-sm bg-red-100 border border-red-200 text-red-800 rounded-md">
                    <div className="flex">
                      <svg className="h-5 w-5 text-red-600 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                      </svg>
                      <span>{applicationError}</span>
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* Step 2: Job Information Input */}
            {stepNav.currentStep === 2 && (
              <div>
                <h2 className="text-xl font-semibold mb-4">2. Masukkan Informasi Lowongan</h2>
                <JobInfoInput
                  jobDescription={form.values.jobDescription}
                  onJobDescriptionChange={(value) => form.setValues(prev => ({ ...prev, jobDescription: value }))}
                  jobImage={form.values.jobImage}
                  onJobImageChange={handleJobImageChange}
                  onInputMethodChange={setInputMethod}
                  onError={setApplicationError}
                />

                {/* Navigation */}
                <div className="flex justify-between mt-6">
                  <button
                    onClick={goBack}
                    className="inline-flex items-center justify-center gap-2 border border-gray-300 hover:bg-gray-100 text-gray-800 px-6 py-3 rounded-lg font-semibold shadow-sm transition-colors"
                  >
                    <ArrowLeftIcon className="w-5 h-5" />
                    <span className="hidden sm:inline">Kembali</span>
                  </button>
                  <button
                    className="btn-primary rounded-lg"
                    onClick={generateEmail}
                    disabled={isUploading || isLoading}
                  >
                    <span className="flex items-center justify-center">
                      Buat Email -
                      <svg className="w-4 h-4 ml-0.5" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M21 6.375C21 9.06739 16.9706 11.25 12 11.25C7.02944 11.25 3 9.06739 3 6.375C3 3.68261 7.02944 1.5 12 1.5C16.9706 1.5 21 3.68261 21 6.375Z" />
                        <path d="M12 12.75C14.6852 12.75 17.1905 12.1637 19.0784 11.1411C19.7684 10.7673 20.4248 10.3043 20.9747 9.75674C20.9915 9.87831 21 10.0011 21 10.125C21 12.8174 16.9706 15 12 15C7.02944 15 3 12.8174 3 10.125C3 10.0011 3.00853 9.8783 3.02529 9.75674C3.57523 10.3043 4.23162 10.7673 4.92161 11.1411C6.80949 12.1637 9.31481 12.75 12 12.75Z" />
                        <path d="M12 16.5C14.6852 16.5 17.1905 15.9137 19.0784 14.8911C19.7684 14.5173 20.4248 14.0543 20.9747 13.5067C20.9915 13.6283 21 13.7511 21 13.875C21 16.5674 16.9706 18.75 12 18.75C7.02944 18.75 3 16.5674 3 13.875C3 13.7511 3.00853 13.6283 3.02529 13.5067C3.57523 14.0543 4.23162 14.5173 4.92161 14.8911C6.80949 15.9137 9.31481 16.5 12 16.5Z" />
                        <path d="M12 20.25C14.6852 20.25 17.1905 19.6637 19.0784 18.6411C19.7684 18.2673 20.4248 17.8043 20.9747 17.2567C20.9915 17.3783 21 17.5011 21 17.625C21 20.3174 16.9706 22.5 12 22.5C7.02944 22.5 3 20.3174 3 17.625C3 17.5011 3.00853 17.3783 3.02529 17.2567C3.57523 17.8043 4.23162 18.2673 4.92161 18.6411C6.80949 19.6637 9.31481 20.25 12 20.25Z" />
                      </svg>
                      5
                    </span>
                  </button>
                </div>

                {/* Error message */}
                {applicationError && (
                  <div className="p-3 mt-6 text-sm bg-red-100 border border-red-200 text-red-800 rounded-md">
                    <div className="flex">
                      <svg className="h-5 w-5 text-red-600 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                      </svg>
                      <span>{applicationError}</span>
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* Step 3: Email Results & Preview */}
            {stepNav.currentStep === 3 && (
              <div>
                <h2 className="text-xl font-semibold mb-4">3. Email Lamaran Anda</h2>
                
                {/* Loading State */}
                {isGenerating && (
                  <div className="flex flex-col items-center justify-center py-12">
                    <svg className="animate-spin h-12 w-12 text-primary mb-4" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 714 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    <h3 className="text-lg font-semibold text-gray-800 mb-2">Membuat Email Lamaran...</h3>
                    <p className="text-gray-600 text-center max-w-md">
                      AI sedang menganalisis resume dan lowongan pekerjaan Anda untuk membuat email lamaran yang tepat
                    </p>
                  </div>
                )}

                {/* Email Preview */}
                {!isGenerating && (emailSubject || emailBody) && (
                  <div className="flex flex-col">
                    {/* Email Subject Field */}
                    <div className="mb-3">
                      <div className="flex justify-between items-center mb-1">
                        <div className="text-sm text-gray-600">Subjek:</div>
                        {emailSubject && (
                          <div className="flex flex-wrap items-center gap-2 justify-end">
                            {!isEmailSubjectEditable && (
                              <button
                                className="text-primary hover:text-blue-700 flex items-center text-xs font-medium"
                                onClick={() => {
                                  navigator.clipboard.writeText(emailSubject);
                                  alert('Subjek email disalin ke clipboard!');
                                }}
                              >
                                <svg className="h-3.5 w-3.5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3"></path>
                                </svg>
                                Salin
                              </button>
                            )}
                            {isEmailSubjectEditable ? (
                              <div className="flex items-center">
                                <button
                                  className="text-red-600 hover:text-red-800 flex items-center text-xs font-medium"
                                  onClick={() => {
                                    setEmailSubject(originalEmailSubject);
                                    setIsEmailSubjectEditable(false);
                                  }}
                                >
                                  <XIcon className="h-3.5 w-3.5 mr-1" />
                                  Batal
                                </button>
                                <button
                                  className="text-primary hover:text-blue-700 flex items-center text-xs font-medium ml-2"
                                  onClick={async () => {
                                    if (currentEmailId && user) {
                                      await saveCurrentEmailChanges();
                                    }
                                    setIsEmailSubjectEditable(false);
                                  }}
                                  disabled={isSavingEmail}
                                >
                                  {isSavingEmail ? (
                                    <>
                                      <svg className="animate-spin h-3.5 w-3.5 mr-1" fill="none" viewBox="0 0 24 24">
                                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 714 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                      </svg>
                                      Menyimpan...
                                    </>
                                  ) : (
                                    <>
                                      <CheckIcon className="h-3.5 w-3.5 mr-1" />
                                      Simpan
                                    </>
                                  )}
                                </button>
                              </div>
                            ) : (
                              <button
                                className="text-primary hover:text-blue-700 flex items-center text-xs font-medium"
                                onClick={() => {
                                  setOriginalEmailSubject(emailSubject);
                                  setIsEmailSubjectEditable(true);
                                }}
                              >
                                <svg className="h-3.5 w-3.5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"></path>
                                </svg>
                                Edit
                              </button>
                            )}
                          </div>
                        )}
                      </div>
                      {emailSubject ? (
                        isEmailSubjectEditable ? (
                          <textarea
                            className="bg-white p-3 rounded border border-blue-300 whitespace-pre-wrap font-serif text-sm w-full focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                            value={emailSubject}
                            onChange={(e) => setEmailSubject(e.target.value)}
                            rows={1}
                            style={{ resize: 'vertical' }}
                          />
                        ) : (
                          <div className="bg-gray-50 p-3 rounded border border-gray-200 whitespace-pre-wrap font-serif text-sm">
                            {emailSubject}
                          </div>
                        )
                      ) : (
                        <div className="bg-gray-50 p-3 rounded border border-gray-200 whitespace-pre-wrap font-serif text-sm text-gray-400 italic text-center">
                          Subjek akan muncul di sini
                        </div>
                      )}
                    </div>
                    
                    {/* Email Body Field */}
                    <div>
                      <div className="flex justify-between items-center mb-1">
                        <div className="text-sm text-gray-600">Isi email:</div>
                        {emailBody && (
                          <div className="flex flex-wrap items-center gap-2 justify-end">
                            {!isEmailBodyEditable && (
                              <button
                                className="text-primary hover:text-blue-700 flex items-center text-xs font-medium"
                                onClick={() => {
                                  navigator.clipboard.writeText(emailBody);
                                  alert('Isi email disalin ke clipboard!');
                                }}
                              >
                                <svg className="h-3.5 w-3.5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3"></path>
                                </svg>
                                Salin
                              </button>
                            )}
                            {isEmailBodyEditable ? (
                              <div className="flex items-center">
                                <button
                                  className="text-red-600 hover:text-red-800 flex items-center text-xs font-medium"
                                  onClick={() => {
                                    setEmailBody(originalEmailBody);
                                    setIsEmailBodyEditable(false);
                                  }}
                                >
                                  <XIcon className="h-3.5 w-3.5 mr-1" />
                                  Batal
                                </button>
                                <button
                                  className="text-primary hover:text-blue-700 flex items-center text-xs font-medium ml-2"
                                  onClick={async () => {
                                    if (currentEmailId && user) {
                                      await saveCurrentEmailChanges();
                                    }
                                    setIsEmailBodyEditable(false);
                                  }}
                                  disabled={isSavingEmail}
                                >
                                  {isSavingEmail ? (
                                    <>
                                      <svg className="animate-spin h-3.5 w-3.5 mr-1" fill="none" viewBox="0 0 24 24">
                                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 714 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                      </svg>
                                      Menyimpan...
                                    </>
                                  ) : (
                                    <>
                                      <CheckIcon className="h-3.5 w-3.5 mr-1" />
                                      Simpan
                                    </>
                                  )}
                                </button>
                              </div>
                            ) : (
                              <button
                                className="text-primary hover:text-blue-700 flex items-center text-xs font-medium"
                                onClick={() => {
                                  setOriginalEmailBody(emailBody);
                                  setIsEmailBodyEditable(true);
                                }}
                              >
                                <svg className="h-3.5 w-3.5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"></path>
                                </svg>
                                Edit
                              </button>
                            )}
                          </div>
                        )}
                      </div>
                      {emailBody ? (
                        isEmailBodyEditable ? (
                          <textarea
                            className="bg-white p-4 rounded border border-blue-300 whitespace-pre-wrap font-serif text-sm w-full focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                            value={emailBody}
                            onChange={(e) => setEmailBody(e.target.value)}
                            rows={15}
                            style={{ resize: 'vertical', minHeight: '360px' }}
                          />
                        ) : (
                          <div className="bg-gray-50 p-4 rounded border border-gray-200 min-h-[360px] whitespace-pre-wrap font-serif text-sm">
                            {emailBody}
                          </div>
                        )
                      ) : (
                        <div className="bg-gray-50 p-4 rounded border border-gray-200 min-h-[360px] whitespace-pre-wrap font-serif text-sm text-gray-400 italic text-center flex flex-col items-center justify-center h-full">
                          <svg className="h-12 w-12 mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                          </svg>
                          <p>Email lamaran Anda akan ditampilkan di sini setelah dibuat.</p>
                        </div>
                      )}
                    </div>
                    
                    {emailBody && (
                      <>
                        <div className="p-3 bg-blue-50 text-blue-800 rounded-lg border border-blue-200 text-xs mt-2">
                          <div className='flex gap-2'>
                            <svg className="w-5 h-5 text-blue-500 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M12 20a8 8 0 100-16 8 8 0 000 16z"></path>
                            </svg>
                            <span className='text-sm'>Jangan lupa untuk mengisi bagian yang diperlukan dan melampirkan dokumen tambahan yang disebutkan saat melamar.</span>
                          </div>
                        </div>
                        <p className="text-xs text-gray-500 italic mt-4">
                          *Email lamaran ini dibuat dengan AI dan mungkin tidak sepenuhnya akurat. Silakan periksa ulang dan edit jika diperlukan. Anda dapat membuat ulang berkali-kali untuk hasil yang lebih sesuai.
                        </p>
                      </>
                    )}
                  </div>
                )}

                {/* Navigation */}
                <div className="flex justify-between mt-6">
                  {!isGenerating && (
                    <button
                      onClick={goBack}
                      className="inline-flex items-center justify-center gap-2 border border-gray-300 hover:bg-gray-100 text-gray-800 px-6 py-3 rounded-lg font-semibold shadow-sm transition-colors"
                    >
                      <ArrowLeftIcon className="w-5 h-5" />
                      <span className="hidden sm:inline">Kembali</span>
                    </button>
                  )}
                  
                  {(emailSubject || emailBody) && (
                    <button
                      onClick={generateEmail}
                      className={`inline-flex items-center justify-center gap-2 bg-gray-100 hover:bg-gray-200 text-gray-700 px-6 py-3 rounded-lg font-semibold shadow-sm transition-colors ${isGenerating ? 'opacity-75 cursor-not-allowed' : ''}`}
                      disabled={isGenerating}
                    >
                      {isGenerating ? (
                        <>
                          <svg className="animate-spin h-4 w-4" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 714 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                          Membuat Ulang...
                        </>
                      ) : (
                        <>
                          <span className="flex items-center justify-center">
                            Buat Ulang -
                            <svg className="w-4 h-4 ml-0.5" fill="currentColor" viewBox="0 0 24 24">
                              <path d="M21 6.375C21 9.06739 16.9706 11.25 12 11.25C7.02944 11.25 3 9.06739 3 6.375C3 3.68261 7.02944 1.5 12 1.5C16.9706 1.5 21 3.68261 21 6.375Z" />
                              <path d="M12 12.75C14.6852 12.75 17.1905 12.1637 19.0784 11.1411C19.7684 10.7673 20.4248 10.3043 20.9747 9.75674C20.9915 9.87831 21 10.0011 21 10.125C21 12.8174 16.9706 15 12 15C7.02944 15 3 12.8174 3 10.125C3 10.0011 3.00853 9.8783 3.02529 9.75674C3.57523 10.3043 4.23162 10.7673 4.92161 11.1411C6.80949 12.1637 9.31481 12.75 12 12.75Z" />
                              <path d="M12 16.5C14.6852 16.5 17.1905 15.9137 19.0784 14.8911C19.7684 14.5173 20.4248 14.0543 20.9747 13.5067C20.9915 13.6283 21 13.7511 21 13.875C21 16.5674 16.9706 18.75 12 18.75C7.02944 18.75 3 16.5674 3 13.875C3 13.7511 3.00853 13.6283 3.02529 13.5067C3.57523 14.0543 4.23162 14.5173 4.92161 14.8911C6.80949 15.9137 9.31481 16.5 12 16.5Z" />
                              <path d="M12 20.25C14.6852 20.25 17.1905 19.6637 19.0784 18.6411C19.7684 18.2673 20.4248 17.8043 20.9747 17.2567C20.9915 17.3783 21 17.5011 21 17.625C21 20.3174 16.9706 22.5 12 22.5C7.02944 22.5 3 20.3174 3 17.625C3 17.5011 3.00853 17.3783 3.02529 17.2567C3.57523 17.8043 4.23162 18.2673 4.92161 18.6411C6.80949 19.6637 9.31481 20.25 12 20.25Z" />
                            </svg>
                            5
                          </span>
                        </>
                      )}
                    </button>
                  )}
                </div>

                {/* Error message */}
                {applicationError && (
                  <div className="p-3 mt-6 text-sm bg-red-100 border border-red-200 text-red-800 rounded-md">
                    <div className="flex">
                      <svg className="h-5 w-5 text-red-600 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                      </svg>
                      <div className="flex-1">
                        <span>{applicationError}</span>
                        {applicationError.includes('Token Anda tidak cukup') && (
                          <div className="mt-3">
                            <button
                              onClick={() => window.location.href = '/buy-tokens'}
                              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
                            >
                              Beli Token
                            </button>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Show History Button - only show for logged in users */}
          {user && (
            <div className="text-center mt-8">
              <button
                onClick={() => setShowHistoryModal(true)}
                className="inline-flex items-center gap-2 px-6 py-3 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg font-medium transition-colors"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                Lihat Riwayat Email
              </button>
            </div>
          )}
        </div>
      </section>

      <Footer />
    </main>
  );
}
