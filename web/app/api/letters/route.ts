export const dynamic = "force-dynamic";

import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase-server';
import { createApiHandler } from '@/utils/apiErrorHandler';
import { captureApiError } from '@/utils/errorMonitoring';
import { getTemplateById } from '@/utils/letter-templates/applicationLetterTemplates';

async function handleRequest(request: NextRequest) {
  try {
    // Create Supabase client
    const supabase = await createClient();

    const { data: { user }, error: userError } = await supabase.auth.getUser();

    if (userError || !user) {
      console.error('Error getting user:', userError);
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user's letters from database
    const { data: letters, error: fetchError } = await supabase
      .from('letters')
      .select('id, plain_text, design_html, template_id, created_at')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false });

    if (fetchError) {
      console.error('Error fetching letters:', fetchError);
      captureApiError('fetch-letters', fetchError);
      return NextResponse.json({ 
        error: 'Failed to fetch letters' 
      }, { status: 500 });
    }

    // Format the letters with template information
    const formattedLetters = letters.map(letter => {
      const template = getTemplateById(letter.template_id);
      return {
        id: letter.id,
        plainText: letter.plain_text,
        hasDesign: !!letter.design_html,
        templateId: letter.template_id,
        templateName: template?.name || 'Plain Text',
        createdAt: letter.created_at,
        // Don't send the full HTML to reduce payload size
        preview: letter.plain_text.substring(0, 200) + (letter.plain_text.length > 200 ? '...' : '')
      };
    });

    return NextResponse.json({
      success: true,
      data: formattedLetters
    });

  } catch (err) {
    console.error('Error in letters API:', err);
    captureApiError('letters-api', err as Error);
    return NextResponse.json({ 
      error: 'Internal server error' 
    }, { status: 500 });
  }
}

export const GET = createApiHandler('letters', handleRequest);
