export const dynamic = "force-dynamic";

import { NextRequest, NextResponse } from 'next/server';
import { createApiHandler } from '@/utils/apiErrorHandler';
import { captureApiError } from '@/utils/errorMonitoring';
import { createClient } from '@/lib/supabase-server';
import { trackApiUsage } from '@/lib/mixpanel-server';

async function handleRequest(request: NextRequest) {
  // Start timing the request
  const startTime = Date.now();
  let userId: string | undefined;

  try {
    // Parse FormData from request body
    const formData = await request.formData();
    const emailId = formData.get('emailId') as string;
    const subject = formData.get('subject') as string;
    const body = formData.get('body') as string;

    // Validate required fields
    if (!emailId || !subject || !body) {
      return NextResponse.json({
        success: false,
        error: 'Email ID, subject, and body are required'
      }, { status: 400 });
    }

    // Create Supabase client
    const supabase = await createClient();

    const { data: { user }, error: userError } = await supabase.auth.getUser();

    if (userError || !user) {
      console.error('Error getting user:', userError);
      return NextResponse.json({
        success: false,
        error: 'Authentication required'
      }, { status: 401 });
    }

    userId = user.id;

    // Update the email in the database
    const { data: updatedEmail, error: updateError } = await supabase
      .from('emails')
      .update({
        subject: subject.trim(),
        body: body.trim(),
        updated_at: new Date().toISOString()
      })
      .eq('id', emailId)
      .eq('user_id', userId) // Ensure user can only update their own emails
      .select('id, subject, body, created_at')
      .single();

    if (updateError) {
      console.error('Error updating email:', updateError);
      throw new Error('Failed to update email');
    }

    if (!updatedEmail) {
      return NextResponse.json({
        success: false,
        error: 'Email not found or you do not have permission to update it'
      }, { status: 404 });
    }

    // Calculate request duration
    const duration = Date.now() - startTime;

    // Track successful API usage in Mixpanel
    trackApiUsage(
      'update-email',
      'success',
      duration,
      {
        email_id: emailId,
        subject_length: subject.length,
        body_length: body.length,
        user_id: userId
      },
      userId
    );

    return NextResponse.json({
      success: true,
      email: updatedEmail
    });
    
  } catch (error) {
    console.error('Error updating email:', error);
    
    // Calculate request duration even for errors
    const duration = Date.now() - startTime;
    
    // Track failed API usage in Mixpanel
    trackApiUsage(
      'update-email', 
      'error',
      duration,
      {
        error_message: error instanceof Error ? error.message : 'Unknown error',
        user_id: userId
      },
      userId
    );
    
    // Capture error details with Rollbar
    captureApiError('update-email', error, {
      request_url: request.url,
      user_id: userId
    });
    
    return NextResponse.json({
      success: false,
      error: 'Failed to update email'
    }, { status: 500 });
  }
}

export const POST = createApiHandler('update-email', handleRequest);
