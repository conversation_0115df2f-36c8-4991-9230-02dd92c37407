import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase-server';

export async function GET(request: NextRequest) {
  try {
    const supabase = await createClient();

    // Get the current user
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get pagination parameters from URL
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '5');

    // Validate pagination parameters
    const validatedPage = Math.max(1, page);
    const validatedLimit = Math.min(Math.max(1, limit), 10); // Max 10 items per page
    const offset = (validatedPage - 1) * validatedLimit;

    // Get total count for pagination metadata
    const { count: totalCount, error: countError } = await supabase
      .from('purchases')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', user.id);

    if (countError) {
      console.error('Error counting purchases:', countError);
      return NextResponse.json(
        { error: 'Failed to count purchases' },
        { status: 500 }
      );
    }

    // Query purchases for the current user with pagination
    const { data: purchases, error: purchasesError } = await supabase
      .from('purchases')
      .select(`
        id,
        status,
        created_at,
        invoice_id,
        invoice_url,
        amount,
        currency,
        token_amount,
        token_package,
        payment_method,
        payment_completed_at
      `)
      .eq('user_id', user.id)
      .order('created_at', { ascending: false })
      .range(offset, offset + validatedLimit - 1);

    if (purchasesError) {
      console.error('Error fetching purchases:', purchasesError);
      return NextResponse.json(
        { error: 'Failed to fetch purchases' },
        { status: 500 }
      );
    }

    // Calculate pagination metadata
    const totalPages = Math.ceil((totalCount || 0) / validatedLimit);
    const hasNextPage = validatedPage < totalPages;
    const hasPreviousPage = validatedPage > 1;

    return NextResponse.json({
      success: true,
      purchases: purchases || [],
      pagination: {
        currentPage: validatedPage,
        totalPages,
        totalCount: totalCount || 0,
        limit: validatedLimit,
        hasNextPage,
        hasPreviousPage
      }
    });

  } catch (error) {
    console.error('Purchases API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}