import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase-server'; // Ensure this path is correct

// Midtrans configuration
const MIDTRANS_ENVIRONMENT = process.env.MIDTRANS_ENVIRONMENT || 'sandbox';
const MIDTRANS_SERVER_KEY =
  MIDTRANS_ENVIRONMENT === 'production'
    ? process.env.MIDTRANS_PRODUCTION_SERVER_KEY || ''
    : process.env.MIDTRANS_SANDBOX_SERVER_KEY || '';


// Helper function to verify Midtrans webhook signature
function verifyMidtransSignature(orderId: string, statusCode: string, grossAmount: string, serverKey: string, signatureKey: string): boolean {
  const crypto = require('crypto');

  try {
    // Midtrans signature verification using SHA512
    // Format: SHA512(order_id+status_code+gross_amount+ServerKey)
    const signatureString = orderId + statusCode + grossAmount + serverKey;
    const expectedSignature = crypto.createHash('sha512').update(signatureString, 'utf8').digest('hex');

    // Compare signatures (constant-time comparison)
    return crypto.timingSafeEqual(Buffer.from(signatureKey), Buffer.from(expectedSignature));
  } catch (error) {
    console.error('Error verifying Midtrans signature:', error);
    return false;
  }
}

export async function POST(req: NextRequest) {
  console.log('Midtrans webhook received a request.');

  // Get raw body for signature verification
  const rawBody = await req.text();

  let payload;
  try {
    payload = JSON.parse(rawBody);
    console.log('Midtrans webhook received');
  } catch (error: any) {
    console.error('Error parsing Midtrans webhook payload:', error);
    return NextResponse.json({ ok: false, error: 'Invalid JSON payload.' }, { status: 400 });
  }

  // Verify Midtrans signature for security
  if (!MIDTRANS_SERVER_KEY) {
    console.error('MIDTRANS_SERVER_KEY not configured');
    return NextResponse.json({ ok: false, error: 'Server configuration error.' }, { status: 500 });
  }

  // Extract required fields for signature verification
  const orderId = payload.order_id;
  const statusCode = payload.status_code;
  const grossAmount = payload.gross_amount;
  const signatureKey = payload.signature_key;

  if (!orderId || !statusCode || !grossAmount || !signatureKey) {
    console.warn('Missing required fields for Midtrans signature verification');
    return NextResponse.json({ ok: false, error: 'Missing required fields in payload.' }, { status: 400 });
  }

  if (!verifyMidtransSignature(orderId, statusCode, grossAmount, MIDTRANS_SERVER_KEY, signatureKey)) {
    console.warn('Invalid Midtrans webhook signature received');
    return NextResponse.json({ ok: false, error: 'Invalid webhook signature.' }, { status: 401 });
  }

  console.log('Midtrans webhook signature verified successfully');

  // According to Midtrans docs, for payment notifications:
  // - `order_id` is your purchaseId.
  // - `transaction_status` can be 'settlement', 'pending', 'deny', 'cancel', 'expire', 'capture'.
  // - `fraud_status` can be 'accept', 'deny', 'challenge' (for credit card transactions).

  const invoiceId = payload.order_id;
  const transactionStatus = payload.transaction_status; // e.g., 'settlement', 'pending', 'deny'
  const fraudStatus = payload.fraud_status; // e.g., 'accept', 'deny', 'challenge'
  const midtransTransactionId = payload.transaction_id; // Midtrans internal transaction ID

  if (!invoiceId || !transactionStatus) {
    console.error('Missing order_id or transaction_status in Midtrans webhook payload:', payload);
    return NextResponse.json({ ok: false, error: 'Missing required fields in payload.' }, { status: 400 });
  }

  const supabase = await createClient();

  try {
    // Handle successful payments (settlement for most payment methods, capture for credit cards)
    if (transactionStatus === 'settlement' || (transactionStatus === 'capture' && fraudStatus === 'accept')) {
      console.log('Processing PAID status for invoiceId:', invoiceId);

      // First, check if the purchase exists and is in pending status
      const { data: existingPurchase, error: selectError } = await supabase
        .from('purchases')
        .select('id, status, invoice_id, token_amount, user_id')
        .eq('invoice_id', invoiceId)
        .single();

      if (selectError) {
        console.error(`Error finding purchase with invoice_id ${invoiceId}:`, selectError);
        return NextResponse.json({ ok: false, error: 'Purchase not found.' }, { status: 404 });
      }

      // Check if purchase is already processed (idempotency protection)
      if (existingPurchase.status !== 'pending') {
        console.log(`Purchase ${invoiceId} already processed with status: ${existingPurchase.status}`);
        return NextResponse.json({ ok: true, message: 'Purchase already processed.' });
      }

      console.log('Found pending purchase:', existingPurchase);

      // Update the purchase with proper error handling and data return (only if currently pending)
      const { data: updatedPurchase, error } = await supabase
        .from('purchases')
        .update({
          status: 'paid',
          payment_completed_at: new Date().toISOString(),
          midtrans_transaction_id: midtransTransactionId
        })
        .eq('invoice_id', invoiceId)
        .eq('status', 'pending') // Only update if currently pending
        .select()
        .single();

      if (error) {
        console.error(`Supabase error updating purchase ${invoiceId} to paid:`, error);
        console.error('Error details:', JSON.stringify(error, null, 2));
        return NextResponse.json({ ok: false, error: 'Failed to update purchase status.' }, { status: 500 });
      }

      if (!updatedPurchase) {
        console.error(`No purchase was updated for invoice_id: ${invoiceId}`);
        return NextResponse.json({ ok: false, error: 'No purchase was updated.' }, { status: 404 });
      }

      console.log(`Purchase ${invoiceId} successfully updated to PAID:`, updatedPurchase);

      try {
        // Get current token balance
        const { data: profileData, error: profileError } = await supabase
          .from('profiles')
          .select('tokens')
          .eq('id', existingPurchase.user_id)
          .single();

        if (profileError) {
          console.error('Error fetching user profile for token update:', profileError);
        } else if (profileData) {
          const currentTokens = profileData.tokens ?? 0;
          const newTokens = currentTokens + existingPurchase.token_amount;

          const { data: updatedProfile, error: tokenUpdateError } = await supabase
            .from('profiles')
            .update({ tokens: newTokens })
            .eq('id', existingPurchase.user_id)
            .select()
            .single();

          if (tokenUpdateError) {
            console.error('Error updating user tokens:', tokenUpdateError);
          } else {
            console.log(`Successfully added ${existingPurchase.token_amount} tokens to user ${existingPurchase.user_id}. New balance: ${newTokens}`);
          }
        }
      } catch (tokenError) {
        console.error('Unexpected error while adding tokens:', tokenError);
      }
    } else if (transactionStatus === 'expire') {
      console.log('Processing EXPIRED status for invoiceId:', invoiceId);

      const { data: updatedPurchase, error } = await supabase
        .from('purchases')
        .update({
          status: 'expired',
          midtrans_transaction_id: midtransTransactionId
        })
        .eq('invoice_id', invoiceId)
        .eq('status', 'pending') // Only update if currently pending
        .select()
        .single();

      if (error) {
        console.error(`Supabase error updating purchase ${invoiceId} to expired:`, error);
        console.error('Error details:', JSON.stringify(error, null, 2));
        return NextResponse.json({ ok: false, error: 'Failed to update purchase status.' }, { status: 500 });
      }

      if (!updatedPurchase) {
        console.error(`No purchase was updated for invoice_id: ${invoiceId}`);
        return NextResponse.json({ ok: false, error: 'No purchase was updated.' }, { status: 404 });
      }

      console.log(`Purchase ${invoiceId} successfully updated to EXPIRED:`, updatedPurchase);

    } else if (transactionStatus === 'deny' || transactionStatus === 'cancel' || (transactionStatus === 'capture' && fraudStatus === 'deny')) {
      console.log('Processing FAILED status for invoiceId:', invoiceId);

      const { data: updatedPurchase, error } = await supabase
        .from('purchases')
        .update({
          status: 'failed',
          midtrans_transaction_id: midtransTransactionId
        })
        .eq('invoice_id', invoiceId)
        .eq('status', 'pending') // Only update if currently pending
        .select()
        .single();

      if (error) {
        console.error(`Supabase error updating purchase ${invoiceId} to failed:`, error);
        console.error('Error details:', JSON.stringify(error, null, 2));
        return NextResponse.json({ ok: false, error: 'Failed to update purchase status.' }, { status: 500 });
      }

      if (!updatedPurchase) {
        console.error(`No purchase was updated for invoice_id: ${invoiceId}`);
        return NextResponse.json({ ok: false, error: 'No purchase was updated.' }, { status: 404 });
      }

      console.log(`Purchase ${invoiceId} successfully updated to FAILED:`, updatedPurchase);

    } else if (transactionStatus === 'pending') {
      console.log(`Received pending status for purchase ${invoiceId} - no action needed`);
    } else {
      console.log(`Received unhandled Midtrans payment status: ${transactionStatus} for purchase ${invoiceId}`);
    }

    // Respond to Midtrans that the webhook was received and processed (or acknowledged)
    return NextResponse.json({ ok: true, message: 'Webhook received.' });

  } catch (error: any) {
    console.error('Error processing Midtrans webhook:', error);
    return NextResponse.json({ ok: false, error: 'Internal server error processing webhook.' }, { status: 500 });
  }
}
