import { NextRequest, NextResponse } from 'next/server';
import { v4 as uuidv4 } from 'uuid';
import { createClient } from '@/lib/supabase-server'; // Ensure this path is correct for your Supabase server client
import { getTemplateById } from '@/utils/letter-templates/applicationLetterTemplates';

// Midtrans Snap API configuration
// IMPORTANT: Ensure MIDTRANS_SERVER_KEY is set in your environment variables
const MIDTRANS_ENVIRONMENT = process.env.MIDTRANS_ENVIRONMENT || 'sandbox';
const MIDTRANS_SERVER_KEY =
  MIDTRANS_ENVIRONMENT === 'production'
    ? process.env.MIDTRANS_PRODUCTION_SERVER_KEY || ''
    : process.env.MIDTRANS_SANDBOX_SERVER_KEY || '';
const MIDTRANS_CLIENT_KEY =
  MIDTRANS_ENVIRONMENT === 'production'
    ? process.env.MIDTRANS_PRODUCTION_CLIENT_KEY || ''
    : process.env.MIDTRANS_SANDBOX_CLIENT_KEY || '';

// Initialize Midtrans client
const midtransClient = require('midtrans-client');
const snap = new midtransClient.Snap({
  isProduction: MIDTRANS_ENVIRONMENT === 'production',
  serverKey: MIDTRANS_SERVER_KEY,
  clientKey: MIDTRANS_CLIENT_KEY
});

export async function POST(req: NextRequest) {
  try {
    const supabase = await createClient(); // Initialize Supabase server client

    const protocol = req.headers.get('x-forwarded-proto') || 'http'; // Handle reverse proxies
    const host = req.headers.get('host');
    const baseUrl = `${protocol}://${host}`;

    const { data: { user }, error: userError } = await supabase.auth.getUser();

    if (userError || !user) {
      console.error('Error getting user:', userError);
      return NextResponse.json({ ok: false, error: 'Authentication required' }, { status: 401 });
    }

    // Parse multipart/form-data sent from the client
    const formData = await req.formData();
    const tokenPackage = formData.get('tokenPackage')?.toString();

    if (!tokenPackage) {
      return NextResponse.json({ ok: false, error: 'Missing token package.' }, { status: 400 });
    }

    // Generate a unique ID for this purchase attempt. This will be used as Midtrans order_id
    // and as the primary key for your 'purchases' table.
    const purchaseId = uuidv4();

    let amount: number;
    let itemName: string;
    let tokenAmount = 0;

    // TODO: ke depannya pake database, gitu juga dengan data paket di halaman payment
    switch (tokenPackage) {
      case 'coba':
        amount = 10000;
        tokenAmount = 20;
        itemName = 'Paket Eksplorasi - 20 Token';
        break;
      case 'hemat':
        amount = 20000;
        tokenAmount = 60; // 40 + 20 bonus (Independence Day Special)
        itemName = 'Paket Siap Melamar - 60 Token';
        break;
      case 'pro':
        amount = 50000;
        tokenAmount = 180; // 100 + 80 bonus (Independence Day Special)
        itemName = 'Paket Pejuang Karir - 180 Token';
        break;
      default:
        return NextResponse.json({ ok: false, error: 'Invalid token package.' }, { status: 400 });
    }

    // Prepare Midtrans Snap payload
    const midtransPayload = {
      transaction_details: {
        order_id: purchaseId,
        gross_amount: amount
      },
      item_details: [
        {
          id: tokenPackage,
          price: amount,
          quantity: 1,
          name: itemName
        }
      ],
      customer_details: {
        email: user.email || '',
      },
      callbacks: {
        finish: `${baseUrl}/profile`,
        unfinish: `${baseUrl}/profile`,
        error: `${baseUrl}/profile`
      },
      expiry: {
        duration: 60,
        unit: 'minutes'
      }
    };

    // Create Midtrans Snap transaction
    const midtransResult = await snap.createTransaction(midtransPayload);

    if (!midtransResult || !midtransResult.redirect_url) {
      console.error('Midtrans transaction creation failed. Response:', midtransResult);
      throw new Error('Failed to create Midtrans transaction. Please check Midtrans logs.');
    }

    const { error: insertError } = await supabase
      .from('purchases')
      .insert({
        id: purchaseId, // Use the same UUID for your table's PK
        user_id: user.id,
        invoice_id: purchaseId, // Use purchaseId as invoice_id for Midtrans
        invoice_url: midtransResult.redirect_url, // Store payment URL for continuing pending payments
        status: 'pending', // Initial status
        amount: amount,
        currency: 'IDR',
        payment_method: 'midtrans_snap',
        token_amount: tokenAmount,
        token_package: tokenPackage,
      });

    if (insertError) {
      console.error('Supabase insert error:', insertError);
      // Critical: If Supabase insert fails, you might have an orphaned Midtrans transaction.
      // Consider implementing a reconciliation process.
      // For now, we throw an error, which should be caught by the client and handled.
      throw new Error(`Failed to record purchase in database: ${insertError.message}`);
    }

    return NextResponse.json({
      ok: true,
      purchaseId, // Your internal purchase ID (also Midtrans order_id)
      invoice_url: midtransResult.redirect_url, // URL for Midtrans hosted payment page
      order_id: purchaseId, // Midtrans order_id (same as purchaseId)
    });

  } catch (error: any) {
    console.error('Create payment API error:', error.message);
    // Provide a more generic error message to the client for security
    const clientErrorMessage = error.message?.includes('Midtrans') || error.message?.includes('database')
                             ? 'Payment processing error. Please try again or contact support.'
                             : 'An unexpected error occurred.';
    return NextResponse.json({ ok: false, error: clientErrorMessage, details: error.message }, { status: 500 });
  }
}
