export const dynamic = "force-dynamic";

import { NextRequest, NextResponse } from "next/server";
import { createApiHandler } from "@/utils/apiErrorHandler";
import { createClient } from "@/lib/supabase-server";
import { StructuredResumeData } from "@/types/resume-structured";
import { getResumeTemplateById } from "@/utils/resume-templates/resumeTemplates";
import { fillResumeTemplate } from "@/utils/template-engine";

async function handlePostRequest(req: NextRequest) {
  const supabase = await createClient();

  // Get current user
  const { data: { user }, error: authError } = await supabase.auth.getUser();
  if (authError || !user) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const body = (await req.json()) as {
      structured_data: StructuredResumeData;
      template_id: string;
    };

    // Validate required data
    if (!body.structured_data) {
      return NextResponse.json({ error: "structured_data is required" }, { status: 400 });
    }

    if (!body.template_id) {
      return NextResponse.json({ error: "template_id is required" }, { status: 400 });
    }

    // Validate template exists
    const template = getResumeTemplateById(body.template_id);
    if (!template) {
      return NextResponse.json({ error: "Invalid template_id provided" }, { status: 400 });
    }

    // Check if user already has a manual resume (status = 'done' with empty or minimal structured_data)
    // This prevents duplicate empty manual resumes
    const { data: existingResumes, error: queryError } = await supabase
      .from('resumes')
      .select('id, structured_data, created_at')
      .eq('user_id', user.id)
      .eq('status', 'done')
      .order('created_at', { ascending: false })
      .limit(5); // Check last 5 resumes to avoid performance issues

    if (queryError) {
      console.error('Error checking existing resumes:', queryError);
      // Continue with creation if query fails - better to have duplicate than block user
    } else if (existingResumes && existingResumes.length > 0) {
      // Check if any of the recent resumes are essentially empty manual resumes
      /**
       * Determines if a resume is considered "empty" for duplicate prevention purposes.
       * A resume is considered empty if it lacks meaningful professional content.
       *
       * Business Logic:
       * - Required fields (professionalSummary, targetPosition) immediately make a resume non-empty
       * - Personal contact info alone (name, email, phone) is NOT sufficient to prevent "empty" status
       * - Only professional content (experience, education, skills, etc.) makes a resume functionally non-empty
       * - This prevents duplicate resumes that contain only contact details
       */
      const isEmptyResume = (resumeData: { structured_data?: StructuredResumeData } | null | undefined): boolean => {
        if (!resumeData?.structured_data) return true;

        const data = resumeData.structured_data;

        // Helper function to check if a string has content
        const hasContent = (str?: string): boolean => !!str?.trim();

        // Helper function to check if an array has any content
        const hasArrayContent = (arr?: string[]): boolean =>
          !!arr?.length && arr.some(item => hasContent(item));

        // Check personal info
        const hasPersonalInfo = !!(
          hasContent(data.personalInfo?.fullName) ||
          hasContent(data.personalInfo?.email) ||
          hasContent(data.personalInfo?.phone) ||
          hasContent(data.personalInfo?.linkedin) ||
          hasContent(data.personalInfo?.location) ||
          hasContent(data.personalInfo?.website) ||
          hasContent(data.personalInfo?.github)
        );

        // Check professional summary and target position
        const hasProfessionalInfo = !!(
          hasContent(data.professionalSummary) ||
          hasContent(data.targetPosition)
        );

        // Check experiences
        const hasExperiences = !!(
          data.experiences?.length &&
          data.experiences.some(exp =>
            hasContent(exp?.jobTitle) ||
            hasContent(exp?.company) ||
            hasContent(exp?.location) ||
            hasContent(exp?.startDate) ||
            hasContent(exp?.endDate) ||
            hasArrayContent(exp?.responsibilities)
          )
        );

        // Check education
        const hasEducation = !!(
          data.education?.length &&
          data.education.some(edu =>
            hasContent(edu?.degree) ||
            hasContent(edu?.institution) ||
            hasContent(edu?.location) ||
            hasContent(edu?.graduationDate) ||
            hasContent(edu?.gpa) ||
            hasArrayContent(edu?.relevantCoursework) ||
            hasArrayContent(edu?.honors)
          )
        );

        // Check skills (both structure types)
        const hasSkills = !!(
          // Categories structure
          (data.skills?.categories?.length &&
           data.skills.categories.some(cat =>
             hasContent(cat?.category) ||
             hasArrayContent(cat?.skills)
           )) ||
          // Alternative flat structure
          hasArrayContent(data.skills?.allSkills)
        );

        // Check certifications
        const hasCertifications = !!(
          data.certifications?.length &&
          data.certifications.some(cert =>
            hasContent(cert?.name) ||
            hasContent(cert?.issuer) ||
            hasContent(cert?.date) ||
            hasContent(cert?.credentialId)
          )
        );

        // Check projects
        const hasProjects = !!(
          data.projects?.length &&
          data.projects.some(proj =>
            hasContent(proj?.title) ||
            hasContent(proj?.description) ||
            hasContent(proj?.link) ||
            hasArrayContent(proj?.technologies) ||
            hasArrayContent(proj?.achievements)
          )
        );

        // Check languages
        const hasLanguages = !!(
          data.languages?.length &&
          data.languages.some(lang =>
            hasContent(lang?.language) ||
            hasContent(lang?.proficiency)
          )
        );

        // Check awards
        const hasAwards = !!(
          data.awards?.length &&
          data.awards.some(award =>
            hasContent(award?.title) ||
            hasContent(award?.issuer) ||
            hasContent(award?.date) ||
            hasContent(award?.description)
          )
        );

        // Resume is empty only if ALL sections are empty
        return !hasPersonalInfo && !hasProfessionalInfo && !hasExperiences &&
               !hasEducation && !hasSkills && !hasCertifications &&
               !hasProjects && !hasLanguages && !hasAwards;
      };

      // Find the most recent empty manual resume
      const recentEmptyResume = existingResumes.find(resume => isEmptyResume(resume));

      if (recentEmptyResume) {
        // Return the existing empty resume instead of creating a new one
        console.log(`Returning existing empty manual resume ${recentEmptyResume.id} for user ${user.id}`);
        return NextResponse.json({
          id: recentEmptyResume.id,
          success: true,
          message: "Using existing manual resume",
          template_id: body.template_id,
          template_name: template.name,
          html_generated: false,
          is_existing: true
        });
      }
    }

    // Generate HTML from structured data
    let html: string | null = null;
    try {
      html = fillResumeTemplate(template, body.structured_data);
    } catch (error) {
      console.log("Skipping HTML generation for empty manual resume:", error);
      // Continue without HTML - this is expected for empty manual resumes
    }

    // Create database record with 'done' status for manual resumes
    const { data: insertData, error: insertError } = await supabase
      .from('resumes')
      .insert({
        user_id: user.id,
        structured_data: body.structured_data,
        html: html, // Will be null if required fields are missing
        template_id: body.template_id,
        data: { buildMethod: 'manual', templateId: body.template_id }, // Store build method and template info
        status: 'done', // Manual resumes start as 'done' since they don't need AI processing
        tokens_deducted: false, // Initialize as false - tokens will be deducted on first download
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select('id')
      .single();

    if (insertError || !insertData) {
      console.error('Supabase insert error:', insertError);
      return NextResponse.json({ error: 'Failed to create manual resume record' }, { status: 500 });
    }

    const id = insertData.id as string;

    return NextResponse.json({
      id,
      success: true,
      message: "Manual resume created successfully",
      template_id: body.template_id,
      template_name: template.name,
      html_generated: !!html
    });
  } catch (error) {
    console.error('Manual resume creation error:', error);
    return NextResponse.json({
      error: 'Failed to create manual resume'
    }, { status: 500 });
  }
}

export const POST = createApiHandler(
  "resume-manual-create",
  handlePostRequest
);
