export const dynamic = "force-dynamic";

import { NextRequest, NextResponse } from "next/server";
import { createApiHandler } from "@/utils/apiErrorHandler";
import { createClient } from "@/lib/supabase-server";

interface GenerationRecord {
  status: "processing" | "done" | "error";
  pdfUrl?: string;
  suggestions?: string[];
  error?: string;
  startedAt: number;
  structuredData?: any;
}

async function handleGetRequest(
  _req: NextRequest,
  { params }: { params: { id: string } }
) {
  const { id } = await params;
  
  try {
    // Query the database for the resume generation status
    const supabase = await createClient();
    const { data: resumeData, error: fetchError } = await supabase
      .from('resumes')
      .select('*')
      .eq('id', id)
      .single();

    if (fetchError || !resumeData) {
      return NextResponse.json({ error: "ID tidak ditemukan" }, { status: 404 });
    }

    // Map database status to expected frontend format
    let record: GenerationRecord;
    
    if (resumeData.status === 'processing') {
      record = {
        status: "processing",
        startedAt: new Date(resumeData.created_at).getTime()
      };
    } else if (resumeData.status === 'done') {
      // Extract data from the data field
      const data = resumeData.data || {};
      
      record = {
        status: "done",
        suggestions: data.suggestions || [],
        pdfUrl: resumeData.pdf_url || '',
        structuredData: resumeData.structured_data || null,
        startedAt: new Date(resumeData.created_at).getTime()
      };
    } else if (resumeData.status === 'error') {
      record = {
        status: "error",
        error: resumeData.data?.error_message || 'Unknown error occurred',
        startedAt: new Date(resumeData.created_at).getTime()
      };
    } else {
      // Handle any other status as processing
      record = {
        status: "processing",
        startedAt: new Date(resumeData.created_at).getTime()
      };
    }

    return NextResponse.json(record, { status: 200 });
  } catch (error) {
    console.error('Error fetching resume generation status:', error);
    return NextResponse.json({
      error: "Terjadi kesalahan saat mengambil status"
    }, { status: 500 });
  }
}

export const GET = createApiHandler("resume-generate-status", handleGetRequest);
