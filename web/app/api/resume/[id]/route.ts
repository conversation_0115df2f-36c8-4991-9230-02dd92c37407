export const dynamic = "force-dynamic";

import { NextRequest, NextResponse } from "next/server";
import { createApiHandler } from "@/utils/apiErrorHandler";
import { createClient } from "@/lib/supabase-server";
import { getResumeTemplateById } from "@/utils/resume-templates/resumeTemplates";
import { fillResumeTemplate } from "@/utils/template-engine";

/**
 * Delete PDF from Supabase storage
 */
async function deletePdfFromStorage(supabase: any, resumeId: string): Promise<boolean> {
  try {
    const filePath = `${resumeId}.pdf`;
    const { error } = await supabase.storage
      .from('generated-resumes')
      .remove([filePath]);

    if (error) {
      console.error('Error deleting PDF from storage:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error deleting PDF from storage:', error);
    return false;
  }
}

async function handleGetRequest(req: NextRequest, { params }: { params: { id: string } }) {
  const supabase = await createClient();
  const { id } = await params;

  // Get current user
  const { data: { user }, error: authError } = await supabase.auth.getUser();
  if (authError || !user) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    // Get the resume from database
    const { data: resume, error: fetchError } = await supabase
      .from('resumes')
      .select('id, user_id, structured_data, html, template_id, data, status, tokens_deducted, created_at, updated_at')
      .eq('id', id)
      .eq('user_id', user.id) // Ensure user can only access their own resumes
      .single();

    if (fetchError || !resume) {
      console.error('Error fetching resume:', fetchError);
      return NextResponse.json({ error: 'Resume not found or access denied' }, { status: 404 });
    }

    return NextResponse.json(resume);
  } catch (error) {
    console.error('Resume fetch error:', error);
    return NextResponse.json({
      error: 'Failed to fetch resume'
    }, { status: 500 });
  }
}

async function handlePutRequest(req: NextRequest, { params }: { params: { id: string } }) {
  const supabase = await createClient();
  const { id } = await params;

  // Get current user
  const { data: { user }, error: authError } = await supabase.auth.getUser();
  if (authError || !user) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const body = await req.json();
    const { structured_data, template_id } = body;

    // Validate required data
    if (!structured_data) {
      return NextResponse.json({ error: "structured_data is required" }, { status: 400 });
    }

    if (!template_id) {
      return NextResponse.json({ error: "template_id is required" }, { status: 400 });
    }

    // Generate HTML from structured data using template engine
    const template = getResumeTemplateById(body.template_id);
    if (!template) {
      return NextResponse.json({ error: "Invalid template_id provided" }, { status: 400 });
    }

    // Generate HTML from structured data
    let html: string | null = null;
    try {
      html = fillResumeTemplate(template, body.structured_data);
    } catch (error) {
      console.log("Skipping HTML generation for empty manual resume:", error);
      // Continue without HTML - this is expected for empty manual resumes
    }

    // Update the resume
    const { data: updatedResume, error: updateError } = await supabase
      .from('resumes')
      .update({
        structured_data,
        template_id,
        html,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .eq('user_id', user.id) // Ensure user can only update their own resumes
      .select()
      .single();

    if (updateError || !updatedResume) {
      console.error('Error updating resume:', updateError);
      return NextResponse.json({ error: 'Failed to update resume' }, { status: 500 });
    }

    // Delete cached PDF from storage since the resume content has been updated
    const deletionResult = await deletePdfFromStorage(supabase, id);
    if (deletionResult) {
      console.log(`Cached PDF deleted for resume ${id}`);
    } else {
      console.warn(`Failed to delete cached PDF for resume ${id}`);
    }

    return NextResponse.json({
      success: true,
      message: "Resume updated successfully",
      html_generated: !!html,
      template_id: body.template_id,
      template_name: template.name
    });
  } catch (error) {
    console.error('Resume update error:', error);
    return NextResponse.json({
      error: 'Failed to update resume'
    }, { status: 500 });
  }
}

export const GET = createApiHandler("resume-fetch", handleGetRequest);
export const PUT = createApiHandler("resume-update", handlePutRequest);
