'use client';

import { useState, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import Link from 'next/link';
import { useAuth } from '@/hooks/useAuth';
import { FcGoogle } from "react-icons/fc";
import NavbarMinimal from '@/components/NavbarMinimal';
import Seo from '@/components/Seo';

// Component that uses useSearchParams
function RegisterForm({ onRegister, redirectPath }: { onRegister: (redirectPath: string | null) => Promise<void>; redirectPath: string | null }) {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [socialLoginError, setSocialLoginError] = useState<string | null>(null);
  const { signUp, signInWithGoogle } = useAuth();
  const router = useRouter();

  // Password validation function
  const validatePassword = (password: string) => {
    return {
      minLength: password.length >= 8,
      hasUppercase: /[A-Z]/.test(password),
      hasLowercase: /[a-z]/.test(password),
      hasNumber: /\d/.test(password),
      hasSymbol: /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)
    };
  };

  // Get password validation status
  const passwordValidation = validatePassword(password);

  const handleRegister = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    if (password !== confirmPassword) {
      setError('Password tidak cocok');
      setLoading(false);
      return;
    }

    try {
      let redirectTo = `${window.location.origin}/auth/callback`;
      if (redirectPath) {
        redirectTo = `${window.location.origin}/auth/callback?next=${encodeURIComponent(redirectPath)}`;
      }

      const result = await signUp(email, password, redirectTo);

      if (result.error) {
        if (result.message) {
          setError(result.message);
          return;
        } else {
          throw result.error;
        }
      }

      await onRegister(redirectPath);
    } catch (error: any) {
      setError('Terjadi kesalahan saat mendaftar');
    } finally {
      setLoading(false);
    }
  };

  return (
    <form className="mt-4 space-y-6" onSubmit={handleRegister}>
            {error && (
              <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg relative" role="alert">
                <span className="block sm:inline">{error}</span>
              </div>
            )}
            
            <div className="space-y-4">
              <div>
                <label htmlFor="email-address" className="block text-sm font-medium text-gray-700 mb-1">
                  Alamat Email
                </label>
                <input
                  id="email-address"
                  name="email"
                  type="email"
                  autoComplete="email"
                  required
                  className="appearance-none rounded-lg relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary focus:z-10 sm:text-sm"
                  placeholder="Alamat Email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                />
              </div>
              <div>
                <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
                  Password
                </label>
                <input
                  id="password"
                  name="password"
                  type="password"
                  autoComplete="new-password"
                  required
                  className="appearance-none rounded-lg relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary focus:z-10 sm:text-sm"
                  placeholder="Password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                />
                <div className="mt-2 space-y-1">
                  <div className="flex items-center space-x-2">
                    <div className={`w-2 h-2 rounded-full ${passwordValidation.minLength ? 'bg-green-500' : 'bg-gray-300'}`}></div>
                    <span className={`text-xs ${passwordValidation.minLength ? 'text-green-600' : 'text-gray-500'}`}>
                      Minimal 8 karakter
                    </span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className={`w-2 h-2 rounded-full ${passwordValidation.hasUppercase ? 'bg-green-500' : 'bg-gray-300'}`}></div>
                    <span className={`text-xs ${passwordValidation.hasUppercase ? 'text-green-600' : 'text-gray-500'}`}>
                      Huruf besar (A-Z)
                    </span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className={`w-2 h-2 rounded-full ${passwordValidation.hasLowercase ? 'bg-green-500' : 'bg-gray-300'}`}></div>
                    <span className={`text-xs ${passwordValidation.hasLowercase ? 'text-green-600' : 'text-gray-500'}`}>
                      Huruf kecil (a-z)
                    </span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className={`w-2 h-2 rounded-full ${passwordValidation.hasNumber ? 'bg-green-500' : 'bg-gray-300'}`}></div>
                    <span className={`text-xs ${passwordValidation.hasNumber ? 'text-green-600' : 'text-gray-500'}`}>
                      Angka (0-9)
                    </span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className={`w-2 h-2 rounded-full ${passwordValidation.hasSymbol ? 'bg-green-500' : 'bg-gray-300'}`}></div>
                    <span className={`text-xs ${passwordValidation.hasSymbol ? 'text-green-600' : 'text-gray-500'}`}>
                      Simbol (!@#$%^&*)
                    </span>
                  </div>
                </div>
              </div>
              <div>
                <label htmlFor="confirm-password" className="block text-sm font-medium text-gray-700 mb-1">
                  Konfirmasi Password
                </label>
                <input
                  id="confirm-password"
                  name="confirm-password"
                  type="password"
                  autoComplete="new-password"
                  required
                  className="appearance-none rounded-lg relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary focus:z-10 sm:text-sm"
                  placeholder="Konfirmasi Password"
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                />
              </div>
            </div>

            <div>
              <button
                type="submit"
                disabled={loading}
                className="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors"
              >
                {loading ? 'Memproses...' : 'Daftar'}
              </button>
            </div>
            
            <div className="mt-4">
              {socialLoginError && (
                <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg relative mb-4" role="alert">
                  <span className="block sm:inline">{socialLoginError}</span>
                </div>
              )}
              
              <div className="relative my-6">
                <div className="absolute inset-0 flex items-center">
                  <div className="w-full border-t border-gray-300"></div>
                </div>
                <div className="relative flex justify-center text-sm">
                  <span className="px-2 bg-white text-gray-500">Atau daftar dengan</span>
                </div>
              </div>
              
              <div className="flex justify-center mt-4">
                <button
                  type="button"
                  onClick={async () => {
                    setSocialLoginError(null);
                    try {
                      const result = await signInWithGoogle(redirectPath ? encodeURIComponent(redirectPath) : undefined);
                      if (result.success && result.message) {
                        router.push(result.message);
                      }
                      if (result.error) {
                        if (result.message) {
                          setSocialLoginError(result.message);
                        } else {
                          throw result.error;
                        }
                      }
                    } catch (error: any) {
                      setSocialLoginError(error.message || 'Gagal login dengan Google');
                    }
                  }}
                  className="w-full py-2.5 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary flex items-center justify-center"
                >
                  <span className="flex items-center justify-center w-full">
                    <FcGoogle size={20} />
                    <span className="ml-2">Google</span>
                  </span>
                </button>
              </div>
            </div>
          </form>
  );
}

function RegisterPage() {
  const searchParams = useSearchParams();
  const redirectPath = searchParams.get('redirect');

  const handleRegisterSuccess = async (redirectTo: string | null) => {
    // Show success message or redirect
    window.location.href = '/register-success';
  };

  return (
    <div className="min-h-screen flex flex-col bg-gray-50">
      <Seo
        title="Daftar Akun Baru | Gigsta"
        description="Buat akun Gigsta untuk mengakses platform pembuatan surat lamaran, email lamaran, dan analisis kecocokan CV dengan lowongan kerja."
        canonical="https://gigsta.io/register"
      />
      {/* Navigation Bar */}
      <NavbarMinimal />

      {/* Register Form Container */}
      <div className="flex-grow flex items-center justify-center p-4 sm:px-6 lg:px-8">
        <div className="max-w-md w-full bg-white rounded-xl shadow-lg p-8">
          <div className="text-center">
            {/* Gigsta Logo */}
            <div className="mx-auto mb-4 flex justify-center">
              <img src="/images/logo.svg" alt="Gigsta Logo" className="h-20 invert" />
            </div>
            <h2 className="text-3xl font-extrabold text-gray-900">Daftar Akun Baru</h2>
            <p className="mt-2 text-sm text-gray-600">
              Sudah punya akun?{' '}
              <Link href={`/login${redirectPath ? `?redirect=${encodeURIComponent(redirectPath)}` : ''}`} className="font-medium text-primary hover:text-primary-dark">
                Masuk di sini
              </Link>
            </p>

            {/* 10 Token Bonus Banner */}
            <div className="mt-4 bg-gradient-to-r from-purple-600 to-pink-600 text-white p-3 rounded-lg shadow-lg">
              <div className="flex items-center justify-center space-x-1 mb-1">
                <svg className="w-4 h-4 text-yellow-300 animate-pulse" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 2L13.09 8.26L22 9L13.09 9.74L12 16L10.91 9.74L2 9L10.91 8.26L12 2Z"/>
                </svg>
                <span className="font-bold text-sm">🎉 BONUS 10 TOKEN GRATIS!</span>
                <svg className="w-4 h-4 text-yellow-300 animate-pulse" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 2L13.09 8.26L22 9L13.09 9.74L12 16L10.91 9.74L2 9L10.91 8.26L12 2Z"/>
                </svg>
              </div>
              <p className="text-xs opacity-90 text-center">
                Dapatkan 10 token gratis langsung setelah mendaftar akun baru
              </p>
            </div>
          </div>

          <Suspense fallback={<div>Loading...</div>}>
            <RegisterForm onRegister={handleRegisterSuccess} redirectPath={redirectPath} />
          </Suspense>
        </div>
      </div>
    </div>
  );
}

export default function Register() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <RegisterPage />
    </Suspense>
  );
}
