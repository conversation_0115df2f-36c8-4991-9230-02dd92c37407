'use client';

import Navbar from '@/components/Navbar';
import { useAuth } from '@/hooks/useAuth';
import Footer from '@/components/Footer';
import Seo from '@/components/Seo';
import IndependenceDayBanner from '@/components/IndependenceDayBanner';
import Link from 'next/link';
import { useCallback } from 'react';

export default function Home() {
  const auth = useAuth();
  const scrollToFeatures = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    const featuresSection = document.getElementById('features');
    if (featuresSection) {
      featuresSection.scrollIntoView({ behavior: 'smooth' });
    }
  }, []);



  return (
    <main className="min-h-screen flex flex-col pt-16">
      <Seo
        title="Platform AI Terlengkap untuk Karir - CV ATS, Surat Lamaran & Analisis Pekerjaan | Gigsta"
        description="Buat CV ATS-friendly, surat lamaran profesional, email lamaran, dan analisis kecocokan pekerjaan dengan AI. Platform karir terlengkap untuk meningkatkan peluang kerja Anda hingga 3x lipat."
        canonical="https://gigsta.io"
      />
      <Navbar auth={auth} />

      {/* Sticky Banner for Unauthenticated Users */}
      {!auth.loading && !auth.user && (
        <div className="sticky top-[4.5rem] z-10 bg-gradient-to-r from-purple-600 via-pink-600 to-red-600 text-white py-2 sm:py-3 px-4 shadow-lg">
          <div className="max-w-7xl mx-auto flex items-center justify-between gap-3">
            <div className="flex items-center space-x-2 sm:space-x-3 min-w-0 flex-1">
              <div className="flex items-center space-x-1 sm:space-x-2 flex-shrink-0">
                <div className="relative">
                  <svg className="w-4 h-4 sm:w-5 sm:h-5 text-yellow-300 animate-spin" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 2L13.09 8.26L22 9L13.09 9.74L12 16L10.91 9.74L2 9L10.91 8.26L12 2Z"/>
                  </svg>
                  <div className="absolute inset-0 w-4 h-4 sm:w-5 sm:h-5 bg-yellow-300 rounded-full opacity-20 animate-ping"></div>
                </div>
                <span className="font-bold text-sm sm:text-base">🎉</span>
              </div>
              <div className="flex flex-col sm:flex-row sm:items-center sm:space-x-2 min-w-0">
                <span className="font-bold text-xs sm:text-sm md:text-base leading-tight">
                  BONUS 10 TOKEN GRATIS!
                </span>
                <span className="text-xs sm:text-sm opacity-90 leading-tight">
                  untuk pengguna baru yang mendaftar
                </span>
              </div>
            </div>
            <Link
              href="/register"
              className="bg-white text-purple-600 hover:bg-yellow-100 font-bold py-1.5 px-3 sm:py-2 sm:px-4 rounded-full text-xs sm:text-sm text-center transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl flex-shrink-0"
            >
              Daftar Sekarang
            </Link>
          </div>
        </div>
      )}

      {/* Independence Day Banner for Authenticated Users */}
      <IndependenceDayBanner />

      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 py-12 sm:py-16 lg:py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <div className="max-w-4xl mx-auto">
              <div className="mb-6">
                <span className="bg-gradient-to-r from-blue-600 to-purple-600 text-white text-sm font-semibold px-4 py-2 rounded-full inline-flex items-center shadow-lg">
                  <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 2L13.09 8.26L22 9L13.09 9.74L12 16L10.91 9.74L2 9L10.91 8.26L12 2Z"/>
                  </svg>
                  Platform AI Terlengkap untuk Karir
                </span>
              </div>
              <h1 className="text-3xl sm:text-4xl lg:text-5xl xl:text-6xl font-bold text-gray-900 mb-6 leading-tight">
                Tingkatkan Peluang Kerja Anda dengan
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600"> AI Canggih</span>
              </h1>
              <p className="text-lg sm:text-xl text-gray-600 mb-8 max-w-3xl mx-auto leading-relaxed">
                Platform karir terlengkap berbasis AI untuk membuat CV ATS-friendly, surat lamaran profesional, email lamaran, dan analisis kecocokan pekerjaan. Tingkatkan peluang diterima kerja hingga 3x lipat!
              </p>

              {/* CTA Buttons */}
              <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
                <Link
                  href="/resume-builder"
                  className="w-full sm:w-auto bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold px-8 py-4 rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105 text-center"
                >
                  🚀 Buat CV ATS Sekarang
                </Link>
                <button
                  onClick={scrollToFeatures}
                  className="w-full sm:w-auto bg-white text-gray-700 hover:text-gray-900 font-semibold px-8 py-4 rounded-lg border-2 border-gray-200 hover:border-gray-300 shadow-sm hover:shadow-md transition-all duration-200 text-center"
                >
                  Lihat Semua Fitur
                </button>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Featured Product - Resume Builder */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-2xl p-8 lg:p-12 relative overflow-hidden">
            {/* Background decoration */}
            <div className="absolute top-0 right-0 -mt-4 -mr-4 w-24 h-24 bg-gradient-to-br from-blue-200 to-purple-200 rounded-full opacity-20"></div>
            <div className="absolute bottom-0 left-0 -mb-8 -ml-8 w-32 h-32 bg-gradient-to-tr from-purple-200 to-blue-200 rounded-full opacity-20"></div>

            <div className="relative z-5">
              <div className="flex flex-col lg:flex-row items-center gap-8 lg:gap-12">
                <div className="flex-1 text-center lg:text-left">
                  <div className="mb-4">
                    <span className="bg-gradient-to-r from-blue-600 to-purple-600 text-white text-sm font-semibold px-3 py-1 rounded-full">
                      ⭐ Fitur Unggulan
                    </span>
                  </div>
                  <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
                    CV ATS Friendly Builder
                  </h2>
                  <p className="text-lg text-gray-600 mb-6 leading-relaxed">
                    Buat CV profesional yang mudah dibaca oleh sistem ATS (Applicant Tracking System) dengan bantuan AI. Pilih dari berbagai template modern dan isi data Anda dengan mudah.
                  </p>

                  {/* Key Features */}
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 mb-8">
                    <div className="flex items-center text-sm text-gray-700">
                      <svg className="w-5 h-5 text-green-500 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                      Template ATS-Friendly
                    </div>
                    <div className="flex items-center text-sm text-gray-700">
                      <svg className="w-5 h-5 text-green-500 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                      AI-Powered Content
                    </div>
                    <div className="flex items-center text-sm text-gray-700">
                      <svg className="w-5 h-5 text-green-500 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                      Real-time Preview
                    </div>
                    <div className="flex items-center text-sm text-gray-700">
                      <svg className="w-5 h-5 text-green-500 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                      Download PDF
                    </div>
                  </div>

                  <Link
                    href="/resume-builder"
                    className="inline-flex items-center bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold px-6 py-3 rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105"
                  >
                    <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    Buat CV Sekarang
                  </Link>
                </div>

                {/* Visual representation */}
                <div className="flex-shrink-0">
                  <div className="w-64 h-80 bg-white rounded-lg shadow-xl border border-gray-200 p-4 transform rotate-3 hover:rotate-0 transition-transform duration-300">
                    <div className="h-full bg-gradient-to-b from-gray-50 to-gray-100 rounded p-3 flex flex-col">
                      {/* Header - Name and Contact */}
                      <div className="mb-4">
                        <div className="h-3 bg-gray-800 rounded w-24 mb-1"></div>
                        <div className="h-1.5 bg-gray-400 rounded w-20 mb-0.5"></div>
                        <div className="h-1.5 bg-gray-400 rounded w-22"></div>
                      </div>

                      {/* Professional Summary */}
                      <div className="mb-3">
                        <div className="h-2 bg-blue-600 rounded w-16 mb-1.5"></div>
                        <div className="space-y-1">
                          <div className="h-1 bg-gray-300 rounded w-full"></div>
                          <div className="h-1 bg-gray-300 rounded w-5/6"></div>
                          <div className="h-1 bg-gray-300 rounded w-3/4"></div>
                        </div>
                      </div>

                      {/* Experience */}
                      <div className="mb-3">
                        <div className="h-2 bg-purple-600 rounded w-18 mb-1.5"></div>
                        <div className="mb-2">
                          <div className="h-1.5 bg-gray-500 rounded w-20 mb-0.5"></div>
                          <div className="h-1 bg-gray-400 rounded w-16 mb-1"></div>
                          <div className="space-y-0.5">
                            <div className="h-0.5 bg-gray-300 rounded w-full"></div>
                            <div className="h-0.5 bg-gray-300 rounded w-4/5"></div>
                            <div className="h-0.5 bg-gray-300 rounded w-3/4"></div>
                          </div>
                        </div>
                      </div>

                      {/* Skills */}
                      <div className="flex-1">
                        <div className="h-2 bg-green-600 rounded w-12 mb-1.5"></div>
                        <div className="space-y-0.5">
                          <div className="h-0.5 bg-gray-300 rounded w-3/4"></div>
                          <div className="h-0.5 bg-gray-300 rounded w-2/3"></div>
                          <div className="h-0.5 bg-gray-300 rounded w-5/6"></div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* All Features Section */}
      <section id="features" className="py-16 bg-gray-50 scroll-mt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">Semua Fitur Karir AI</h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              Platform lengkap berbasis kecerdasan buatan untuk membantu Anda di setiap tahap pencarian kerja
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {/* Resume Builder Feature */}
            <Link href="/resume-builder" className="block group">
              <div className="bg-white rounded-xl p-6 shadow-sm hover:shadow-lg transition-all duration-200 border border-gray-100 hover:border-blue-200 h-full">
                <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-200">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors">
                  CV ATS Friendly
                </h3>
                <p className="text-gray-600 text-sm mb-4 leading-relaxed">
                  Buat CV profesional yang mudah dibaca sistem ATS dengan template modern dan AI assistant.
                </p>
                <div className="flex items-center text-blue-600 text-sm font-medium group-hover:text-blue-700">
                  Mulai Buat CV
                  <svg className="w-4 h-4 ml-1 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                  </svg>
                </div>
              </div>
            </Link>

            {/* Application Letter Feature */}
            <Link href="/application-letter" className="block group">
              <div className="bg-white rounded-xl p-6 shadow-sm hover:shadow-lg transition-all duration-200 border border-gray-100 hover:border-green-200 h-full">
                <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-teal-500 rounded-lg flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-200">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2 group-hover:text-green-600 transition-colors">
                  Surat Lamaran
                </h3>
                <p className="text-gray-600 text-sm mb-4 leading-relaxed">
                  Dokumen lamaran profesional yang disesuaikan dengan posisi dan perusahaan target.
                </p>
                <div className="flex items-center text-green-600 text-sm font-medium group-hover:text-green-700">
                  Buat Surat Lamaran
                  <svg className="w-4 h-4 ml-1 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                  </svg>
                </div>
              </div>
            </Link>

            {/* Email Application Feature */}
            <Link href="/email-application" className="block group">
              <div className="bg-white rounded-xl p-6 shadow-sm hover:shadow-lg transition-all duration-200 border border-gray-100 hover:border-orange-200 h-full">
                <div className="w-12 h-12 bg-gradient-to-r from-orange-500 to-red-500 rounded-lg flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-200">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                  </svg>
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2 group-hover:text-orange-600 transition-colors">
                  Email Lamaran
                </h3>
                <p className="text-gray-600 text-sm mb-4 leading-relaxed">
                  Email lamaran yang menarik dan profesional untuk melamar kerja secara online.
                </p>
                <div className="flex items-center text-orange-600 text-sm font-medium group-hover:text-orange-700">
                  Tulis Email Lamaran
                  <svg className="w-4 h-4 ml-1 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                  </svg>
                </div>
              </div>
            </Link>

            {/* Job Match Feature */}
            <Link href="/job-match" className="block group">
              <div className="bg-white rounded-xl p-6 shadow-sm hover:shadow-lg transition-all duration-200 border border-gray-100 hover:border-purple-200 h-full">
                <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-200">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2 group-hover:text-purple-600 transition-colors">
                  Analisis Kecocokan
                </h3>
                <p className="text-gray-600 text-sm mb-4 leading-relaxed">
                  Analisis mendalam kecocokan CV Anda dengan lowongan pekerjaan yang diinginkan.
                </p>
                <div className="flex items-center text-purple-600 text-sm font-medium group-hover:text-purple-700">
                  Cek Kecocokan
                  <svg className="w-4 h-4 ml-1 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                  </svg>
                </div>
              </div>
            </Link>
          </div>
        </div>
      </section>
      

      {/* How It Works Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">Cara Kerja Platform Kami</h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              Proses sederhana dan efisien untuk meningkatkan peluang karir Anda dengan bantuan AI
            </p>
          </div>

          <div className="relative">
            {/* Connection lines for desktop */}
            <div className="hidden lg:block absolute top-6 left-0 right-0 h-0.5 bg-gradient-to-r from-blue-400 via-purple-400 to-orange-400 opacity-30"></div>

            <div className="grid grid-cols-1 lg:grid-cols-4 gap-8 lg:gap-4">
              {/* Step 1 */}
              <div className="relative flex flex-col items-center text-center">
                <div className="relative z-5 bg-gradient-to-r from-blue-500 to-purple-500 text-white rounded-full w-16 h-16 flex items-center justify-center mb-6 text-xl font-bold shadow-lg">
                  1
                </div>
                <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100 h-full">
                  <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                    </svg>
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-3">Pilih Fitur</h3>
                  <p className="text-gray-600 text-sm leading-relaxed">
                    Pilih fitur yang Anda butuhkan: CV ATS Friendly, Surat Lamaran, Email Lamaran, atau Analisis Kecocokan Pekerjaan.
                  </p>
                </div>
              </div>

              {/* Step 2 */}
              <div className="relative flex flex-col items-center text-center">
                <div className="relative z-5 bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-full w-16 h-16 flex items-center justify-center mb-6 text-xl font-bold shadow-lg">
                  2
                </div>
                <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100 h-full">
                  <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <svg className="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                    </svg>
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-3">Upload & Input</h3>
                  <p className="text-gray-600 text-sm leading-relaxed">
                    Upload CV/resume Anda dan masukkan informasi pekerjaan atau deskripsi lowongan yang ingin Anda lamar.
                  </p>
                </div>
              </div>

              {/* Step 3 */}
              <div className="relative flex flex-col items-center text-center">
                <div className="relative z-5 bg-gradient-to-r from-pink-500 to-red-500 text-white rounded-full w-16 h-16 flex items-center justify-center mb-6 text-xl font-bold shadow-lg">
                  3
                </div>
                <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100 h-full">
                  <div className="w-12 h-12 bg-pink-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <svg className="w-6 h-6 text-pink-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                    </svg>
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-3">AI Processing</h3>
                  <p className="text-gray-600 text-sm leading-relaxed">
                    AI kami menganalisis data Anda dan menghasilkan konten yang dipersonalisasi sesuai dengan kebutuhan spesifik Anda.
                  </p>
                </div>
              </div>

              {/* Step 4 */}
              <div className="relative flex flex-col items-center text-center">
                <div className="relative z-5 bg-gradient-to-r from-red-500 to-orange-500 text-white rounded-full w-16 h-16 flex items-center justify-center mb-6 text-xl font-bold shadow-lg">
                  4
                </div>
                <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100 h-full">
                  <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <svg className="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-3">Download & Apply</h3>
                  <p className="text-gray-600 text-sm leading-relaxed">
                    Dapatkan hasil berkualitas tinggi yang siap digunakan untuk melamar pekerjaan dan tingkatkan peluang Anda diterima.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
      
      {/* Benefits Section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">Mengapa Memilih Gigsta?</h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              Platform karir AI terdepan dengan teknologi canggih dan hasil terbukti
            </p>
          </div>

          <div className="mb-16">
            {/* Benefits list */}
            <div className="max-w-4xl mx-auto space-y-8">
              <div className="flex items-start">
                <div className="flex-shrink-0">
                  <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg flex items-center justify-center">
                    <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                    </svg>
                  </div>
                </div>
                <div className="ml-4">
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">AI Canggih & Terkini</h3>
                  <p className="text-gray-600 leading-relaxed">
                    Menggunakan model AI terbaru untuk menganalisis CV dan deskripsi pekerjaan, memberikan hasil yang akurat dan rekomendasi yang disesuaikan khusus untuk Anda.
                  </p>
                </div>
              </div>

              <div className="flex items-start">
                <div className="flex-shrink-0">
                  <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-teal-500 rounded-lg flex items-center justify-center">
                    <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                </div>
                <div className="ml-4">
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">Hemat Waktu & Efisien</h3>
                  <p className="text-gray-600 leading-relaxed">
                    Otomatisasi pembuatan CV, surat lamaran, dan email lamaran dalam hitungan menit. Fokus pada persiapan wawancara, bukan menulis dokumen.
                  </p>
                </div>
              </div>

              <div className="flex items-start">
                <div className="flex-shrink-0">
                  <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg flex items-center justify-center">
                    <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5.121 17.804A13.937 13.937 0 0112 16c2.5 0 4.847.655 6.879 1.804M15 10a3 3 0 11-6 0 3 3 0 016 0zm6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                </div>
                <div className="ml-4">
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">Personalisasi Cerdas</h3>
                  <p className="text-gray-600 leading-relaxed">
                    Setiap dokumen disesuaikan dengan pengalaman unik Anda dan kebutuhan spesifik posisi yang dilamar untuk hasil maksimal.
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Key Highlight */}
          <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl p-8 text-white text-center">
            <div className="max-w-3xl mx-auto">
              <h3 className="text-2xl lg:text-3xl font-bold mb-4">
                💡 Fakta Penting untuk Job Seekers
              </h3>
              <p className="text-lg lg:text-xl mb-6 opacity-90">
                Rekruter menghabiskan rata-rata hanya <strong>7 detik</strong> untuk meninjau CV.
                Surat lamaran yang disesuaikan khusus untuk setiap posisi dapat meningkatkan peluang wawancara Anda hingga <strong>3x lipat!</strong>
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link
                  href="/resume-builder"
                  className="bg-white text-blue-600 hover:bg-gray-100 font-semibold px-6 py-3 rounded-lg transition-colors"
                >
                  Buat CV ATS Sekarang
                </Link>
                <button
                  onClick={scrollToFeatures}
                  className="bg-transparent border-2 border-white text-white hover:bg-white hover:text-blue-600 font-semibold px-6 py-3 rounded-lg transition-colors"
                >
                  Lihat Semua Fitur
                </button>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Final CTA Section */}
      <section className="py-16 bg-gradient-to-r from-blue-600 to-purple-600 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl lg:text-4xl font-bold mb-6">
              Mulai Perjalanan Karir Impian Anda Hari Ini
            </h2>
            <p className="text-xl mb-8 opacity-90 leading-relaxed">
              Bergabunglah dengan ribuan profesional yang telah meningkatkan peluang karir mereka dengan platform AI terlengkap untuk kebutuhan pencarian kerja.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-8">
              <Link
                href="/resume-builder"
                className="w-full sm:w-auto bg-white text-blue-600 hover:bg-gray-100 font-semibold px-8 py-4 rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105 text-center"
              >
                🚀 Buat CV ATS Sekarang
              </Link>
              <Link
                href="/register"
                className="w-full sm:w-auto bg-transparent border-2 border-white text-white hover:bg-white hover:text-blue-600 font-semibold px-8 py-4 rounded-lg transition-all duration-200 text-center"
              >
                Daftar Gratis + 10 Token
              </Link>
            </div>

            <div className="flex flex-col sm:flex-row items-center justify-center gap-6 text-sm opacity-80">
              <div className="flex items-center">
                <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
                Tanpa Komitmen
              </div>
              <div className="flex items-center">
                <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
                Mulai dalam 2 Menit
              </div>
              <div className="flex items-center">
                <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
                Hasil Berkualitas Tinggi
              </div>
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </main>
  );
}
