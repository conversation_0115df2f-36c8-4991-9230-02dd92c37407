import { LetterDesignOutput } from '@/utils/letter-designs/letterDesignTypes';
import { StructuredLetterData } from './letter-structured';

/**
 * Generates an application letter using the gemini-2.0-flash model
 * @param resumeFile - The resume file buffer and mime type
 * @param jobDescription - The job description (optional if jobImage is provided)
 * @param jobImage - The job posting image file (optional if jobDescription is provided)
 * @returns The generated application letter text
 */
export interface ApplicationLetterOutput {
  plainText?: string; // Optional for structured generation mode
  design?: LetterDesignOutput;
  structuredData?: StructuredLetterData; // New structured data support
  letterId?: string;
}