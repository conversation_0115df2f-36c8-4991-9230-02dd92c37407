export interface StructuredResumeData {
  // Personal Information
  personalInfo: {
    fullName?: string;
    email?: string;
    phone?: string;
    linkedin?: string;
    location?: string;
    website?: string;
    github?: string;
  };

  // Professional Summary
  professionalSummary?: string;

  // Job Title/Target Position
  targetPosition?: string;
  
  // Work Experience
  experiences?: Array<{
    id: string;
    jobTitle: string;
    company: string;
    location: string;
    startDate: string;
    endDate: string; // "Present" for current job
    responsibilities: string[]; // Array of achievement bullets
  }>;

  // Education
  education?: Array<{
    id: string;
    degree: string;
    institution: string;
    location?: string;
    graduationDate: string;
    gpa?: string;
    relevantCoursework?: string[];
    honors?: string[];
  }>;

  // Skills
  skills?: {
    categories: Array<{
      category: string;
      skills: string[]; // Array of skills in this category
    }>;
    // Alternative flat structure for simpler templates
    allSkills?: string[];
  };
  
  // Optional Sections
  certifications?: Array<{
    id: string;
    name: string;
    issuer: string;
    date: string;
    credentialId?: string;
  }>;
  
  projects?: Array<{
    id: string;
    title: string;
    description: string;
    technologies: string[];
    link?: string;
    achievements?: string[];
  }>;
  
  languages?: Array<{
    language: string;
    proficiency: string;
  }>;
  
  awards?: Array<{
    id: string;
    title: string;
    issuer: string;
    date: string;
    description?: string;
  }>;
  
  // Metadata
  metadata?: {
    generatedAt: string;
    lastModified: string;
    templateId?: string;
    aiSuggestions?: string[];
  };
}