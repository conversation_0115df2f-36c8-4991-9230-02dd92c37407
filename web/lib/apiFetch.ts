/**
 * Utility function for making API requests
 */

/**
 * Makes a fetch request to an API endpoint
 *
 * @param url The API endpoint URL
 * @param options Additional fetch options
 * @returns The fetch response
 */
export async function apiFetch(url: string, options: RequestInit = {}): Promise<Response> {
  return fetch(url, options);
}

/**
 * Makes a GET request
 */
export async function apiGet(url: string, options: RequestInit = {}): Promise<Response> {
  return fetch(url, {
    ...options,
    method: 'GET'
  });
}

/**
 * Makes a POST request with FormData
 */
export async function apiPost(url: string, body: Record<string, any>, options: RequestInit = {}): Promise<Response> {
  const formData = new FormData();

  // Append all body fields to FormData
  Object.entries(body).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      formData.append(key, value);
    }
  });

  // Don't set Content-Type header, let the browser set it with the correct boundary
  const { headers: _, ...restOptions } = options;

  return fetch(url, {
    ...restOptions,
    method: 'POST',
    body: formData
  });
}

/**
 * Makes a DELETE request with JSON body
 */
export async function apiDelete(url: string, body: any = {}, options: RequestInit = {}): Promise<Response> {
  const headers = new Headers(options.headers || {});
  headers.set('Content-Type', 'application/json');

  return fetch(url, {
    ...options,
    method: 'DELETE',
    body: JSON.stringify(body),
    headers
  });
}

/**
 * Makes a POST request with FormData body
 */
export async function apiPostFormData(url: string, formData: FormData, options: RequestInit = {}): Promise<Response> {
  return fetch(url, {
    ...options,
    method: 'POST',
    body: formData
  });
}
