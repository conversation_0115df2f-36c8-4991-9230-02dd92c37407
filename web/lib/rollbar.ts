import Rollbar from 'rollbar';

// Token for different environments
const ROLLBAR_ACCESS_TOKEN = process.env.NEXT_PUBLIC_ROLLBAR_CLIENT_TOKEN || '';
const ROLLBAR_SERVER_TOKEN = process.env.NEXT_PUBLIC_ROLLBAR_SERVER_TOKEN || ROLLBAR_ACCESS_TOKEN; // Fallback to the same token if no specific server token
const ENVIRONMENT = process.env.NODE_ENV || 'development';

// Track initialization status
let rollbarInitialized = false;
let rollbarInstance: Rollbar | null = null;
let isTokenError = false;
let isLocalhost = false;

// Detect if we're in browser or server environment
const isBrowser = typeof window !== 'undefined';
const isServer = !isBrowser;

// Helper function to check if we're running on localhost
const checkIfLocalhost = (): boolean => {
  if (!isBrowser) return false;
  
  const hostname = window.location.hostname;
  return hostname === 'localhost' || 
         hostname === '127.0.0.1' || 
         hostname.startsWith('192.168.') ||
         hostname.startsWith('10.') ||
         hostname.includes('.local');
};

// Initialize Rollbar with appropriate token based on environment
const initRollbar = () => {
  // Skip if already attempted or there's no token
  if (rollbarInitialized || isTokenError) {
    return;
  }
  
  // Check if we're on localhost for client-side only
  if (isBrowser) {
    isLocalhost = checkIfLocalhost();
    if (isLocalhost) {
      console.log('Running on localhost, Rollbar error tracking disabled');
      return;
    }
  }
  
  rollbarInitialized = true;
  
  try {
    if (isServer && ROLLBAR_SERVER_TOKEN) {
      // Server-side initialization
      rollbarInstance = new Rollbar({
        accessToken: ROLLBAR_SERVER_TOKEN,
        environment: ENVIRONMENT,
        captureUncaught: true,
        captureUnhandledRejections: true
      });
    } else if (isBrowser && ROLLBAR_ACCESS_TOKEN) {
      // Client-side initialization - might still fail if token is server-side only
      try {
        rollbarInstance = new Rollbar({
          accessToken: ROLLBAR_ACCESS_TOKEN,
          environment: ENVIRONMENT,
          captureUncaught: true,
          captureUnhandledRejections: true,
          payload: {
            client: {
              javascript: {
                source_map_enabled: true,
                code_version: '1.0.0',
                guess_uncaught_frames: true
              }
            }
          },
          // Custom error handler to catch token privilege errors
          onSendCallback: (isErr, args) => {
            if (isErr) {
              const err = args[0];
              // Safer type checking for error messages
              if (err instanceof Error) {
                if (err.message && err.message.includes('insufficient privileges')) {
                  // Disable Rollbar if we detect a token privilege error
                  console.warn('Rollbar disabled in browser due to token privilege error');
                  isTokenError = true;
                  rollbarInstance = null;
                }
              } else if (typeof err === 'string' && err.includes('insufficient privileges')) {
                // Handle case where error is a string
                console.warn('Rollbar disabled in browser due to token privilege error');
                isTokenError = true;
                rollbarInstance = null;
              }
            }
            return true;
          }
        });
      } catch (error) {
        console.warn('Failed to initialize Rollbar in browser:', error);
        isTokenError = true;
      }
    }
  } catch (error) {
    console.error('Error initializing Rollbar:', error);
  }
};

// Check if Rollbar is usable before attempting to use it
const isRollbarUsable = () => {
  // Lazy initialize if not already done
  if (!rollbarInitialized) {
    initRollbar();
  }
  // Only use Rollbar if we have an instance, no token errors, and not on localhost
  return rollbarInstance !== null && !isTokenError && !isLocalhost;
};

// Log error
const logError = (error: Error | string, extraData?: Record<string, any>) => {
  // Check localhost in case it wasn't checked during initialization
  if (isBrowser && !isLocalhost) {
    isLocalhost = checkIfLocalhost();
  }
  
  if (isRollbarUsable()) {
    try {
      rollbarInstance!.error(error, extraData);
    } catch (e) {
      // If we get a token error here, disable Rollbar for future calls
      if (e instanceof Error && e.message.includes('insufficient privileges')) {
        isTokenError = true;
        rollbarInstance = null;
        console.warn('Rollbar disabled due to token privilege error');
      }
      // Fallback to console
      console.error(error);
    }
  } else if (typeof console !== 'undefined') {
    // Just log to console if Rollbar isn't usable
    console.error(error);
  }
};

// Log info
const logInfo = (message: string, extraData?: Record<string, any>) => {
  // Check localhost in case it wasn't checked during initialization
  if (isBrowser && !isLocalhost) {
    isLocalhost = checkIfLocalhost();
  }
  
  if (isRollbarUsable()) {
    try {
      rollbarInstance!.info(message, extraData);
    } catch (e) {
      // If we get a token error here, disable Rollbar for future calls
      if (e instanceof Error && e.message.includes('insufficient privileges')) {
        isTokenError = true;
        rollbarInstance = null;
        console.warn('Rollbar disabled due to token privilege error');
      }
      // Just log to console as fallback
      console.info(message);
    }
  } else if (typeof console !== 'undefined') {
    // Fallback to console
    console.info(message);
  }
};

// Log warning
const logWarning = (message: string, extraData?: Record<string, any>) => {
  // Check localhost in case it wasn't checked during initialization
  if (isBrowser && !isLocalhost) {
    isLocalhost = checkIfLocalhost();
  }
  
  if (isRollbarUsable()) {
    try {
      rollbarInstance!.warning(message, extraData);
    } catch (e) {
      // If we get a token error here, disable Rollbar for future calls
      if (e instanceof Error && e.message.includes('insufficient privileges')) {
        isTokenError = true;
        rollbarInstance = null;
        console.warn('Rollbar disabled due to token privilege error');
      }
      // Fallback to console
      console.warn(message);
    }
  } else if (typeof console !== 'undefined') {
    // Fallback to console
    console.warn(message);
  }
};

export default {
  initRollbar,
  logError,
  logInfo,
  logWarning,
};
