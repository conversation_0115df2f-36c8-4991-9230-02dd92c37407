import mixpanel from 'mixpanel-browser';

// Initialize Mixpanel with your project token
const MIXPANEL_TOKEN = process.env.NEXT_PUBLIC_MIXPANEL_TOKEN || '';

// Variables to track status
let isMixpanelInitialized = false;
let isAdBlockerDetected = false;
let initializationAttempted = false;
let isLocalhost = false;

// Helper function to check if we're running on localhost
const checkIfLocalhost = (): boolean => {
  if (typeof window === 'undefined') return false;
  
  const hostname = window.location.hostname;
  return hostname === 'localhost' || 
         hostname === '127.0.0.1' || 
         hostname.startsWith('192.168.') ||
         hostname.startsWith('10.') ||
         hostname.includes('.local');
};

/**
 * Check if an ad blocker is detected by trying to fetch a small test request to Mixpanel
 * Returns a promise that resolves to true if ad blocker is detected
 */
const detectAdBlocker = async (): Promise<boolean> => {
  if (typeof window === 'undefined') return false;
  
  try {
    // Try to fetch from Mixpanel with a tiny timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 1000);
    
    const response = await fetch('https://api.mixpanel.com/track', {
      method: 'HEAD',
      signal: controller.signal,
      // Ensure cache isn't used
      cache: 'no-store',
    });
    
    clearTimeout(timeoutId);
    return !response.ok; // If response failed, likely blocked
  } catch (error) {
    // If aborted or network error, likely blocked
    return true;
  }
};

// Only initialize if token exists and we're in a browser environment and not on localhost
const initMixpanel = async () => {
  // If already initialized or already attempted, don't try again
  if (isMixpanelInitialized || isAdBlockerDetected || initializationAttempted) {
    return isMixpanelInitialized;
  }
  
  initializationAttempted = true;
  
  if (typeof window === 'undefined' || !MIXPANEL_TOKEN) {
    return false;
  }
  
  // Check if we're on localhost
  isLocalhost = checkIfLocalhost();
  if (isLocalhost) {
    console.log('Running on localhost, Mixpanel analytics disabled');
    return false;
  }

  try {
    // First check for ad blocker
    isAdBlockerDetected = await detectAdBlocker();
    
    if (isAdBlockerDetected) {
      console.log('Ad blocker detected, disabling Mixpanel analytics');
      return false;
    }
    
    // Initialize Mixpanel if no ad blocker detected with optimized config
    mixpanel.init(MIXPANEL_TOKEN, {
      debug: process.env.NODE_ENV !== 'production',
      track_pageview: true,
      persistence: 'localStorage',
      // Batching config to prevent mutex issues
      batch_requests: true,           // Enable batching of requests
      batch_size: 10,                 // Number of events per batch
      batch_flush_interval_ms: 3000,  // Flush every 3 seconds
      // Prevent mutex lock timeouts
      cross_subdomain_cookie: false,  // Avoid cross-domain issues
      secure_cookie: true,            // Use secure cookies
      ip: false,                      // Don't track IPs
      property_blacklist: [],         // No property blacklist
      loaded: (mp: any) => {
        // Make sure we're properly loaded
        console.log('Mixpanel initialized successfully');
      },
      // Handle errors more gracefully
      api_host: "https://api.mixpanel.com",
      api_transport: "xhr" // Use XHR instead of sendBeacon for more reliability
    });
    
    // Set library storage lock timeout to prevent stuck mutexes
    try {
      // Access internal config if possible (not guaranteed to work on all versions)
      if (mixpanel._) {
        // @ts-ignore: Accessing internal property that might not be in typings
        mixpanel._.localStorage.LOCK_TIMEOUT = 2000; // 2s timeout instead of default 10s
      }
    } catch (configError) {
      console.log('Could not configure Mixpanel lock timeout, using defaults');
    }
    
    isMixpanelInitialized = true;
    return true;
  } catch (error) {
    console.error('Failed to initialize Mixpanel:', error);
    isAdBlockerDetected = true; // Assume ad blocker to prevent further attempts
    return false;
  }
};

// Check if Mixpanel is ready and initialized
const isMixpanelReady = () => {
  return typeof window !== 'undefined' &&
         MIXPANEL_TOKEN &&
         isMixpanelInitialized &&
         !isAdBlockerDetected &&
         !isLocalhost &&
         typeof mixpanel !== 'undefined' &&
         mixpanel.track &&
         typeof mixpanel.track === 'function';
};

// Track event safely
const track = async (eventName: string, properties?: Record<string, any>) => {
  // Skip if ad blocker is already detected or on localhost
  if (isAdBlockerDetected || isLocalhost) {
    return;
  }
  
  try {
    // Try to initialize if not already done or attempted
    if (!isMixpanelInitialized && !initializationAttempted) {
      await initMixpanel();
    }
    
    // Only track if initialized successfully
    if (isMixpanelReady()) {
      try {
        mixpanel.track(eventName, properties);
      } catch (error) {
        // If we get an error here, it's likely due to an ad blocker
        if (error instanceof Error && error.message.includes('Bad HTTP status')) {
          isAdBlockerDetected = true;
          console.log('Ad blocker detected during tracking, disabling Mixpanel analytics');
        } else {
          console.error(`Failed to track event '${eventName}':`, error);
        }
      }
    }
  } catch (initError) {
    console.warn('Failed to initialize or track event:', eventName, initError);
    isAdBlockerDetected = true; // Prevent further attempts
  }
};

// Identify user safely
const identify = async (userId: string, userProperties?: Record<string, any>) => {
  // Skip if ad blocker is already detected or on localhost
  if (isAdBlockerDetected || isLocalhost) {
    return;
  }
  
  try {
    // Try to initialize if not already done or attempted
    if (!isMixpanelInitialized && !initializationAttempted) {
      await initMixpanel();
    }
    
    // Only identify if initialized successfully
    if (isMixpanelReady()) {
      try {
        mixpanel.identify(userId);
        if (userProperties) {
          mixpanel.people.set(userProperties);
        }
      } catch (error) {
        // If we get an error here, it's likely due to an ad blocker
        if (error instanceof Error && error.message.includes('Bad HTTP status')) {
          isAdBlockerDetected = true;
          console.log('Ad blocker detected during identify, disabling Mixpanel analytics');
        } else {
          console.error(`Failed to identify user '${userId}':`, error);
        }
      }
    }
  } catch (initError) {
    console.warn('Failed to initialize or identify user:', userId, initError);
    isAdBlockerDetected = true; // Prevent further attempts
  }
};

// Reset user safely
const reset = async () => {
  // Skip if ad blocker is already detected or on localhost
  if (isAdBlockerDetected || isLocalhost) {
    return;
  }
  
  try {
    // Only reset if initialized successfully
    if (isMixpanelReady()) {
      try {
        mixpanel.reset();
      } catch (error) {
        // If we get an error here, it's likely due to an ad blocker
        if (error instanceof Error && error.message.includes('Bad HTTP status')) {
          isAdBlockerDetected = true;
          console.log('Ad blocker detected during reset, disabling Mixpanel analytics');
        } else {
          console.error('Failed to reset user:', error);
        }
      }
    }
  } catch (initError) {
    console.warn('Failed to reset user:', initError);
    isAdBlockerDetected = true; // Prevent further attempts
  }
};

// People operations with ad blocker detection and error handling
const people = {
  set: async (properties: Record<string, any>) => {
    // Skip if ad blocker is already detected
    if (isAdBlockerDetected) {
      return;
    }
    
    // Try to initialize if not already done or attempted
    if (!isMixpanelInitialized && !initializationAttempted) {
      await initMixpanel();
    }
    
    // Only proceed if initialized successfully
    if (isMixpanelReady()) {
      try {
        mixpanel.people.set(properties);
      } catch (error) {
        // If we get an error here, it's likely due to an ad blocker
        if (error instanceof Error && error.message.includes('Bad HTTP status')) {
          isAdBlockerDetected = true;
          console.log('Ad blocker detected during people.set, disabling Mixpanel analytics');
        } else {
          console.error('Failed to set people properties:', error);
        }
      }
    }
  },
  
  increment: async (property: string, value: number = 1) => {
    // Skip if ad blocker is already detected
    if (isAdBlockerDetected) {
      return;
    }
    
    // Try to initialize if not already done or attempted
    if (!isMixpanelInitialized && !initializationAttempted) {
      await initMixpanel();
    }
    
    // Only proceed if initialized successfully
    if (isMixpanelReady()) {
      try {
        mixpanel.people.increment(property, value);
      } catch (error) {
        // If we get an error here, it's likely due to an ad blocker
        if (error instanceof Error && error.message.includes('Bad HTTP status')) {
          isAdBlockerDetected = true;
          console.log('Ad blocker detected during people.increment, disabling Mixpanel analytics');
        } else {
          console.error(`Failed to increment property '${property}':`, error);
        }
      }
    }
  },
  
  append: async (property: string, value: any) => {
    // Skip if ad blocker is already detected
    if (isAdBlockerDetected) {
      return;
    }
    
    // Try to initialize if not already done or attempted
    if (!isMixpanelInitialized && !initializationAttempted) {
      await initMixpanel();
    }
    
    // Only proceed if initialized successfully
    if (isMixpanelReady()) {
      try {
        mixpanel.people.append(property, value);
      } catch (error) {
        // If we get an error here, it's likely due to an ad blocker
        if (error instanceof Error && error.message.includes('Bad HTTP status')) {
          isAdBlockerDetected = true;
          console.log('Ad blocker detected during people.append, disabling Mixpanel analytics');
        } else {
          console.error(`Failed to append to property '${property}':`, error);
        }
      }
    }
  },
  
  union: async (property: string, values: any[]) => {
    // Skip if ad blocker is already detected
    if (isAdBlockerDetected) {
      return;
    }
    
    // Try to initialize if not already done or attempted
    if (!isMixpanelInitialized && !initializationAttempted) {
      await initMixpanel();
    }
    
    // Only proceed if initialized successfully
    if (isMixpanelReady()) {
      try {
        mixpanel.people.union(property, values);
      } catch (error) {
        // If we get an error here, it's likely due to an ad blocker
        if (error instanceof Error && error.message.includes('Bad HTTP status')) {
          isAdBlockerDetected = true;
          console.log('Ad blocker detected during people.union, disabling Mixpanel analytics');
        } else {
          console.error(`Failed to union property '${property}':`, error);
        }
      }
    }
  },
};

export default {
  initMixpanel,
  track,
  identify,
  reset,
  people,
};
