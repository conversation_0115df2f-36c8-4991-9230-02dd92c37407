import Mixpanel from 'mixpanel';

// Initialize Mixpanel with your project token
const MIXPANEL_TOKEN = process.env.NEXT_PUBLIC_MIXPANEL_TOKEN || '';

// Create a singleton instance
let mixpanelInstance: Mixpanel.Mixpanel | null = null;

// Track if we're running on localhost
let isLocalhost = false;

// Helper function to check if we're running on localhost
const checkIfLocalhost = (): boolean => {
  if (typeof window === 'undefined') {
    // For server-side detection, check NODE_ENV or hostname
    // During development with Next.js, we're typically on localhost
    return process.env.NODE_ENV === 'development';
  }
  
  // For client-side detection (fallback)
  const hostname = window.location.hostname;
  return hostname === 'localhost' || 
         hostname === '127.0.0.1' || 
         hostname.startsWith('192.168.') ||
         hostname.startsWith('10.') ||
         hostname.includes('.local');
};

/**
 * Get the Mixpanel instance for server-side tracking
 * This creates a singleton to avoid multiple instances
 */
export const getMixpanelInstance = (): Mixpanel.Mixpanel | null => {
  // Check if we're on localhost - we'll only do this once
  isLocalhost = checkIfLocalhost();
  
  // Don't initialize if no token or on localhost
  if (!MIXPANEL_TOKEN) {
    console.warn('No Mixpanel token found');
    return null;
  }
  
  // Skip tracking on localhost
  if (isLocalhost) {
    console.log('Running on localhost, Mixpanel server-side analytics disabled');
    return null;
  }

  // Return existing instance if already created
  if (mixpanelInstance) {
    return mixpanelInstance;
  }

  try {
    // Initialize Mixpanel
    mixpanelInstance = Mixpanel.init(MIXPANEL_TOKEN, {
      debug: process.env.NODE_ENV !== 'production',
    });
    return mixpanelInstance;
  } catch (error) {
    console.error('Failed to initialize server-side Mixpanel:', error);
    return null;
  }
};

/**
 * Track an event with Mixpanel server-side
 * @param eventName Name of the event to track
 * @param properties Additional properties to track with the event
 * @param distinctId Optional distinct ID for the user
 */
export const trackEvent = (
  eventName: string, 
  properties: Record<string, any> = {}, 
  distinctId?: string
): void => {
  // Skip tracking on localhost
  if (isLocalhost) {
    console.log(`[Mixpanel Server] Skipped event tracking on localhost: ${eventName}`, properties);
    return;
  }
  
  const mixpanel = getMixpanelInstance();
  
  if (!mixpanel) {
    return;
  }

  try {
    // If we have a distinct ID, use it
    if (distinctId) {
      mixpanel.track(eventName, { 
        ...properties,
        distinct_id: distinctId,
        source: 'server' 
      });
    } else {
      // Otherwise track without user association
      mixpanel.track(eventName, { 
        ...properties,
        source: 'server' 
      });
    }
  } catch (error) {
    console.error(`Failed to track event '${eventName}' on server:`, error);
  }
};

/**
 * Track API usage with standard properties
 * @param apiName Name of the API endpoint
 * @param status Success or error status
 * @param durationMs Time taken in milliseconds
 * @param additionalProperties Additional properties to track
 * @param userId User ID if available
 */
export const trackApiUsage = (
  apiName: string,
  status: 'success' | 'error',
  durationMs: number,
  additionalProperties: Record<string, any> = {},
  userId?: string
): void => {
  // Skip tracking on localhost
  if (isLocalhost) {
    console.log(`[Mixpanel Server] Skipped API usage tracking on localhost: ${apiName}`, {
      status,
      duration_ms: durationMs,
      ...additionalProperties
    });
    return;
  }
  
  trackEvent('API Call', {
    api_name: apiName,
    status,
    duration_ms: durationMs,
    ...additionalProperties
  }, userId);
};

export default {
  trackEvent,
  trackApiUsage,
  getMixpanelInstance
};
