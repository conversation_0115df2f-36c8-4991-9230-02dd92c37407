import { useState, useEffect, useRef } from 'react';
import { createClient } from '@/lib/supabase';
import { getTemplateById } from '@/utils/letter-templates/applicationLetterTemplates';
import { useAuth } from '@/hooks/useAuth';
import type { RealtimePostgresChangesPayload, RealtimeChannel } from '@supabase/supabase-js';

// TODO: 
// preview useless, olah lagi
// hasDesign ubah jadi design_html
export interface LetterHistoryItem {
  id: string;
  plainText: string;
  designHtml: string | null;
  templateId: string;
  templateName: string;
  createdAt: string;
}

export function useLetterHistory(auth: ReturnType<typeof useAuth>) {
  const { user, loading: authLoading } = auth;
  const [letters, setLetters] = useState<LetterHistoryItem[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const subscriptionRef = useRef<RealtimeChannel | null>(null);

  const formatLetter = (letter: any): LetterHistoryItem => {
    const template = getTemplateById(letter.template_id);

    return {
      id: letter.id,
      plainText: letter.plain_text,
      designHtml: letter.design_html,
      templateId: letter.template_id,
      templateName: template?.name || 'Plain Text',
      createdAt: letter.created_at
    };
  };

  const shouldIncludeLetter = (letter: any): boolean => {
    return letter.status === 'done' &&
           letter.design_html &&
           letter.plain_text &&
           letter.design_html.trim() !== '' &&
           letter.plain_text.trim() !== '';
  };

  const fetchLetters = async () => {
    if (!user) {
      setLetters([]);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const supabase = createClient();

      const { data: lettersData, error: fetchError } = await supabase
        .from('letters')
        .select('id, plain_text, design_html, template_id, created_at, status')
        .eq('user_id', user.id)
        .eq('status', 'done')
        .not('design_html', 'is', null)
        .not('plain_text', 'is', null)
        .neq('design_html', '')
        .neq('plain_text', '')
        .order('created_at', { ascending: false });

      if (fetchError) {
        throw fetchError;
      }

      const formattedLetters = lettersData?.map(formatLetter) || [];
      setLetters(formattedLetters);
    } catch (err) {
      console.error('Error fetching letters:', err);
      setError('Failed to fetch letters');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    // Cleanup previous subscription if it exists
    if (subscriptionRef.current) {
      const supabase = createClient();
      supabase.removeChannel(subscriptionRef.current);
      subscriptionRef.current = null;
    }

    if (!user) {
      setLetters([]);
      return;
    }

    // Initial fetch
    fetchLetters();

    // Set up realtime subscription
    const supabase = createClient();

    const subscription = supabase
      .channel('letters_changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'letters',
          filter: `user_id=eq.${user.id}`
        },
        (payload: RealtimePostgresChangesPayload<any>) => {
          console.log('Realtime letter change:', payload);

          if (payload.eventType === 'INSERT') {
            // Only add new letter if it meets our criteria
            if (shouldIncludeLetter(payload.new)) {
              const newLetter = formatLetter(payload.new);
              setLetters(prev => [newLetter, ...prev]);
            }
          } else if (payload.eventType === 'UPDATE') {
            // Handle update: either add, update, or remove based on criteria
            const meetsFilter = shouldIncludeLetter(payload.new);
            const updatedLetter = formatLetter(payload.new);

            setLetters(prev => {
              const existingIndex = prev.findIndex(letter => letter.id === updatedLetter.id);

              if (meetsFilter) {
                // Letter meets criteria
                if (existingIndex >= 0) {
                  // Update existing letter
                  return prev.map(letter =>
                    letter.id === updatedLetter.id ? updatedLetter : letter
                  );
                } else {
                  // Add new letter (it now meets criteria)
                  return [updatedLetter, ...prev];
                }
              } else {
                // Letter no longer meets criteria, remove it if it exists
                return existingIndex >= 0
                  ? prev.filter(letter => letter.id !== updatedLetter.id)
                  : prev;
              }
            });
          } else if (payload.eventType === 'DELETE') {
            // Remove deleted letter
            setLetters(prev =>
              prev.filter(letter => letter.id !== payload.old.id)
            );
          }
        }
      )
      .subscribe();

    // Store subscription in ref
    subscriptionRef.current = subscription;

    // Cleanup subscription on unmount
    return () => {
      if (subscriptionRef.current) {
        supabase.removeChannel(subscriptionRef.current);
        subscriptionRef.current = null;
      }
    };
  }, [user]);

  return {
    letters,
    isLoading,
    error,
    refetch: fetchLetters
  };
}
