import React, { useEffect, useRef } from 'react';

interface UseShadowDOMOptions {
  html: string;
  isolateStyles?: boolean;
  containerStyle?: React.CSSProperties;
}

/**
 * Custom hook to render HTML content in a Shadow DOM for complete style isolation
 * This prevents CSS from resume templates from affecting other components
 */
export const useShadowDOM = ({ html, isolateStyles = true, containerStyle }: UseShadowDOMOptions) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const shadowRootRef = useRef<ShadowRoot | null>(null);

  useEffect(() => {
    if (!containerRef.current || !html) return;

    // Apply container styles if provided
    if (containerStyle) {
      Object.assign(containerRef.current.style, containerStyle);
    }

    // Clean up existing shadow root if it exists
    if (shadowRootRef.current) {
      shadowRootRef.current.innerHTML = '';
    }

    // Create shadow root if it doesn't exist
    if (!shadowRootRef.current) {
      try {
        shadowRootRef.current = containerRef.current.attachShadow({ mode: 'open' });
      } catch (error) {
        // Fallback for browsers that don't support Shadow DOM
        console.warn('Shadow DOM not supported, falling back to regular DOM');
        containerRef.current.innerHTML = html;
        return;
      }
    }

    // Render HTML content in shadow root
    if (shadowRootRef.current) {
      shadowRootRef.current.innerHTML = html;
    }

    // Cleanup function
    return () => {
      if (shadowRootRef.current) {
        shadowRootRef.current.innerHTML = '';
      }
    };
  }, [html, isolateStyles, containerStyle]);

  // Cleanup shadow root on unmount
  useEffect(() => {
    return () => {
      if (shadowRootRef.current) {
        shadowRootRef.current.innerHTML = '';
      }
    };
  }, []);

  return containerRef;
};

/**
 * React component wrapper for Shadow DOM rendering
 * Use this component when you need to render HTML with complete style isolation
 */
interface ShadowDOMRendererProps {
  html: string;
  className?: string;
  style?: React.CSSProperties;
  children?: React.ReactNode;
}

export const ShadowDOMRenderer: React.FC<ShadowDOMRendererProps> = ({
  html,
  className,
  style,
  children
}) => {
  const containerRef = useShadowDOM({ html });

  return (
    <div ref={containerRef} className={className} style={style}>
      {children}
    </div>
  );
};
