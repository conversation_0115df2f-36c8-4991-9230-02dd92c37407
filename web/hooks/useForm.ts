import { useState, useCallback } from 'react';

interface FormErrors {
  [key: string]: string;
}

/**
 * Custom hook untuk mengelola form state dan validasi
 * @param initialValues - <PERSON><PERSON> awal form
 * @param validate - <PERSON><PERSON><PERSON> validasi (opsional)
 */
export function useForm<T extends Record<string, any>>(
  initialValues: T,
  validate?: (values: T) => FormErrors
) {
  const [values, setValues] = useState<T>(initialValues);
  const [errors, setErrors] = useState<FormErrors>({});
  const [touched, setTouched] = useState<Record<string, boolean>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Handler untuk perubahan input
  const handleChange = useCallback((e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setValues(prev => ({ ...prev, [name]: value }));
    
    // Clear error when field is edited
    if (errors[name]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  }, [errors]);

  // Handler untuk file input
  const handleFileChange = useCallback((name: string, file: File | null) => {
    setValues(prev => ({ ...prev, [name]: file }));
    
    // Clear error when field is edited
    if (errors[name]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  }, [errors]);

  // Handler untuk blur event
  const handleBlur = useCallback((e: React.FocusEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name } = e.target;
    setTouched(prev => ({ ...prev, [name]: true }));
    
    // Validate field on blur if validation function exists
    if (validate) {
      const fieldErrors = validate(values);
      if (fieldErrors[name]) {
        setErrors(prev => ({ ...prev, [name]: fieldErrors[name] }));
      }
    }
  }, [validate, values]);

  // Handler untuk reset form
  const resetForm = useCallback(() => {
    setValues(initialValues);
    setErrors({});
    setTouched({});
    setIsSubmitting(false);
  }, [initialValues]);

  // Handler untuk submit form
  const handleSubmit = useCallback(
    async (
      onSubmit: (values: T) => Promise<void> | void,
      onError?: (errors: FormErrors) => void
    ) => {
      return async (e: React.FormEvent) => {
        e.preventDefault();
        setIsSubmitting(true);

        // Validate all fields before submit if validation function exists
        if (validate) {
          const formErrors = validate(values);
          const hasErrors = Object.keys(formErrors).length > 0;

          if (hasErrors) {
            setErrors(formErrors);
            setIsSubmitting(false);
            if (onError) onError(formErrors);
            return;
          }
        }

        try {
          await onSubmit(values);
        } catch (error) {
          console.error('Form submission error:', error);
        } finally {
          setIsSubmitting(false);
        }
      };
    },
    [validate, values]
  );

  return {
    values,
    errors,
    touched,
    isSubmitting,
    handleChange,
    handleFileChange,
    handleBlur,
    handleSubmit,
    resetForm,
    setValues
  };
}
