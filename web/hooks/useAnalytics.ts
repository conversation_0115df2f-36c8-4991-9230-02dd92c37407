'use client';

import { useCallback } from 'react';
import mixpanel from '../lib/mixpanel';
import rollbar from '../lib/rollbar';

// Severity levels for Rollbar
type RollbarSeverity = 'critical' | 'error' | 'warning' | 'info' | 'debug';

export function useAnalytics() {
  // Track event in Mixpanel with error handling
  const trackEvent = useCallback((eventName: string, properties?: Record<string, any>) => {
    try {
      mixpanel.track(eventName, properties);
    } catch (error) {
      console.warn('Failed to track event:', eventName, error);
    }
  }, []);

  // Identify user in Mixpanel with error handling
  const identifyUser = useCallback((userId: string, userProperties?: Record<string, any>) => {
    try {
      mixpanel.identify(userId, userProperties);
    } catch (error) {
      console.warn('Failed to identify user:', userId, error);
    }
  }, []);

  // Reset user in Mixpanel with error handling
  const resetUser = useCallback(() => {
    try {
      mixpanel.reset();
    } catch (error) {
      console.warn('Failed to reset user:', error);
    }
  }, []);

  // Log error to Rollbar with contextual information
  const logError = useCallback((error: Error | string, extraData?: Record<string, any>, severity: RollbarSeverity = 'error') => {
    try {
      // Capture additional context if available
      const context = {
        url: typeof window !== 'undefined' ? window.location.href : '',
        timestamp: new Date().toISOString(),
        ...extraData
      };
      
      // Also track error in Mixpanel for correlation
      const errorMessage = error instanceof Error ? error.message : error;
      try {
        mixpanel.track('Error Occurred', {
          error_message: errorMessage,
          severity,
          ...context
        });
      } catch (mixpanelError) {
        console.warn('Failed to track error in Mixpanel:', mixpanelError);
      }
      
      // Log to Rollbar based on severity
      switch(severity) {
        case 'critical':
          rollbar.logError(error, { ...context, severity: 'critical' });
          break;
        case 'warning':
          rollbar.logWarning(errorMessage, context);
          break;
        case 'info':
          rollbar.logInfo(errorMessage, context);
          break;
        case 'debug':
          rollbar.logInfo(`DEBUG: ${errorMessage}`, context);
          break;
        default: // 'error'
          rollbar.logError(error, context);
      }
    } catch (logError) {
      console.error('Failed to log error:', logError);
    }
  }, []);

  // Log info to Rollbar with error handling
  const logInfo = useCallback((message: string, extraData?: Record<string, any>) => {
    try {
      rollbar.logInfo(message, extraData);
    } catch (error) {
      console.warn('Failed to log info:', message, error);
    }
  }, []);

  // Log warning to Rollbar with error handling
  const logWarning = useCallback((message: string, extraData?: Record<string, any>) => {
    try {
      rollbar.logWarning(message, extraData);
    } catch (error) {
      console.warn('Failed to log warning:', message, error);
    }
  }, []);

  return {
    trackEvent,
    identifyUser,
    resetUser,
    logError,
    logInfo,
    logWarning
  };
}
