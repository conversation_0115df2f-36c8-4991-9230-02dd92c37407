import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/hooks/useAuth';
import { useAnalytics } from '@/hooks/useAnalytics';
import { 
  validateResumeFile, 
  uploadResume, 
  fetchExistingResume, 
  deleteResume, 
  getResumeUrl, 
  ResumeInfo 
} from '@/utils/resumeUtils';
import { createToast, defaultToast, Toast as ToastType } from '@/utils/notificationUtils';

// Create a singleton object to cache resume data across components
const resumeCache = {
  data: null as ResumeInfo | null,
  hasBeenFetched: false,
  lastFetchTime: 0,
  noResumeDetected: false, // Flag to track if we've confirmed no resume exists
  // Cache expiration time (30 minutes in milliseconds)
  cacheExpiration: 30 * 60 * 1000
};

export interface UseResumeReturn {
  isUploading: boolean;
  uploadSuccess: boolean;
  error: string;
  toast: ToastType;
  existingResume: ResumeInfo | null;
  isLoading: boolean;
  isDeleting: boolean;
  isGettingResumeUrl: boolean;
  setToast: (toast: ToastType) => void;
  handleResumeUpload: (e: React.ChangeEvent<HTMLInputElement>) => Promise<void>;
  handleViewResume: () => Promise<void>;
  handleDeleteResume: () => Promise<boolean>;
}

export function useResume(auth: ReturnType<typeof useAuth>): UseResumeReturn {
  const { user, loading: authLoading } = auth;
  const { trackEvent } = useAnalytics();
  
  const [isUploading, setIsUploading] = useState(false);
  const [uploadSuccess, setUploadSuccess] = useState(false);
  const [toast, setToast] = useState<ToastType>(defaultToast);
  const [error, setError] = useState('');
  const [existingResume, setExistingResume] = useState<ResumeInfo | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isDeleting, setIsDeleting] = useState(false);
  const [isGettingResumeUrl, setIsGettingResumeUrl] = useState(false);

  useEffect(() => {
    const checkExistingResume = async () => {
      try {
        const now = Date.now();
        const cacheIsValid = now - resumeCache.lastFetchTime < resumeCache.cacheExpiration;
        
        // Case 1: We have cached resume data
        if (resumeCache.hasBeenFetched && resumeCache.data && cacheIsValid) {
          setExistingResume(resumeCache.data);
          setUploadSuccess(true);
          setIsLoading(false);
          return;
        }
        
        // Case 2: We know there's no resume and cache is still valid
        if (resumeCache.hasBeenFetched && resumeCache.noResumeDetected && cacheIsValid) {
          setIsLoading(false);
          return;
        }
        
        if (user) {
          // Need to fetch from server
          setIsLoading(true);
        
          // If no cache or expired, fetch from server
          const result = await fetchExistingResume();

          if (result.success && result.hasResume && result.resumeInfo) {
            // Update both state and cache
            setExistingResume(result.resumeInfo);
            setUploadSuccess(true);
            
            // Update cache
            resumeCache.data = result.resumeInfo;
            resumeCache.hasBeenFetched = true;
            resumeCache.noResumeDetected = false;
            resumeCache.lastFetchTime = now;
          } else {
            // Clear cache if no resume found
            resumeCache.data = null;
            resumeCache.hasBeenFetched = true;
            resumeCache.noResumeDetected = true;
            resumeCache.lastFetchTime = now;
            
            setExistingResume(null);
            setUploadSuccess(false);
          }
        }
      } catch (err) {
        console.error('Error checking resume:', err);
      } finally {
        setIsLoading(false);
      }
    };
    
    if (!authLoading) {
      checkExistingResume();
    }
  }, [user, authLoading]);

  const handleViewResume = async () => {
    setIsGettingResumeUrl(true);
    
    // Track resume view attempt
    trackEvent('Resume View Attempt', {
      authenticated: !!user,
      hasExistingResume: !!existingResume
    });
    
    // Handle unauthenticated file view
    if (existingResume?.unauthenticatedResumeFile && existingResume?.publicUrl) {
      window.open(existingResume.publicUrl, '_blank');
      setIsGettingResumeUrl(false);
      
      // Track successful unauthenticated resume view
      trackEvent('Resume Viewed', {
        authenticated: false,
        success: true
      });
      return;
    }
    
    // Handle authenticated file view
    const result = await getResumeUrl(true);
    
    if (result.success && result.url) {
      window.location.href = result.url;
      
      // Track successful authenticated resume view
      trackEvent('Resume Viewed', {
        authenticated: true,
        success: true
      });
    } else {
      setError(result.error || 'Tidak dapat mengakses resume');
      
      // Track failed resume view
      trackEvent('Resume View Failed', {
        authenticated: true,
        error: result.error || 'Tidak dapat mengakses resume'
      });
    }
    
    setIsGettingResumeUrl(false);
  };

  const handleDeleteResume = async () => {
    if (!confirm("Apakah Anda yakin ingin menghapus resume ini?")) {
      return false;
    }
    
    setIsDeleting(true);
    setError('');
    
    // Track resume delete attempt
    trackEvent('Resume Delete Attempt', {
      authenticated: !!user,
      hasExistingResume: !!existingResume
    });
    
    // Handle unauthenticated file deletion
    if (existingResume?.unauthenticatedResumeFile) {
      setExistingResume(null);
      setUploadSuccess(false);
      setToast(createToast('Resume berhasil dihapus', 'info'));

      // Clear cache
      resumeCache.data = null;
      resumeCache.hasBeenFetched = true;
      resumeCache.lastFetchTime = Date.now();

      // Track successful unauthenticated resume delete
      trackEvent('Resume Deleted', {
        authenticated: false,
        success: true
      });

      setIsDeleting(false);
      return true;
    }
    
    // Handle authenticated file deletion
    const result = await deleteResume();
    
    if (result.success) {
      setExistingResume(null);
      setUploadSuccess(false);
      setToast(createToast('Resume berhasil dihapus', 'info'));
      
      // Clear cache
      resumeCache.data = null;
      resumeCache.hasBeenFetched = true;
      resumeCache.lastFetchTime = Date.now();
      
      // Track successful authenticated resume delete
      trackEvent('Resume Deleted', {
        authenticated: true,
        success: true
      });
      
      setIsDeleting(false);
      return true;
    } else {
      setError(result.error || 'Terjadi kesalahan saat menghapus resume');
      
      // Track failed resume delete
      trackEvent('Resume Delete Failed', {
        authenticated: true,
        error: result.error || 'Terjadi kesalahan saat menghapus resume'
      });
      
      setIsDeleting(false);
      return false;
    }
  };

  const handleResumeUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Track resume upload attempt
    trackEvent('Resume Upload Attempt', {
      authenticated: !!user,
      fileType: file.type,
      fileSize: file.size
    });

    const validation = validateResumeFile(file);
    if (!validation.valid) {
      setError(validation.error || 'File tidak valid');
      
      // Track validation error
      trackEvent('Resume Upload Validation Failed', {
        authenticated: !!user,
        fileType: file.type,
        fileSize: file.size,
        error: validation.error || 'File tidak valid'
      });
      return;
    }

    setError('');
    setIsUploading(true);

    // Handle unauthenticated upload (cache file only)
    if (!user) {
      const newResumeInfo = {
        fileName: file.name,
        publicUrl: URL.createObjectURL(file),
        uploadedAt: new Date().toISOString(),
        unauthenticatedResumeFile: file,
      }

      setExistingResume(newResumeInfo);
      setUploadSuccess(true);
      setToast(createToast('Resume siap digunakan', 'success'));

      resumeCache.data = newResumeInfo;
      resumeCache.hasBeenFetched = true;
      resumeCache.lastFetchTime = Date.now();

      // Track successful unauthenticated resume upload
      trackEvent('Resume Uploaded', {
        authenticated: false,
        success: true,
        fileType: file.type,
        fileSize: file.size,
        fileName: file.name
      });

      setIsUploading(false);
      // Reset all file inputs with resume-related IDs
      const fileInputs = ['resume', 'resume-existing', 'resume-new'];
      fileInputs.forEach(id => {
        const fileInput = document.getElementById(id) as HTMLInputElement;
        if (fileInput) {
          fileInput.value = '';
        }
      });
      return;
    }

    // Handle authenticated upload (original Supabase logic)
    const result = await uploadResume(file);
    
    if (result.success && result.data) {
      const newResumeInfo = {
        fileName: result.data.fileName,
        publicUrl: result.data.fileUrl,
        uploadedAt: result.data.uploadedAt,
      };
      
      setExistingResume(newResumeInfo);
      setUploadSuccess(true);
      setToast(createToast('Resume berhasil diunggah!', 'success'));
      
      // Update cache
      resumeCache.data = newResumeInfo;
      resumeCache.hasBeenFetched = true;
      resumeCache.lastFetchTime = Date.now();
      
      // Track successful authenticated resume upload
      trackEvent('Resume Uploaded', {
        authenticated: true,
        success: true,
        fileType: file.type,
        fileSize: file.size,
        fileName: file.name
      });
    } else {
      setError(result.error || 'Terjadi kesalahan saat mengunggah resume');
      setToast(createToast(result.error || 'Terjadi kesalahan saat mengunggah resume', 'error'));
      
      // Track failed resume upload
      trackEvent('Resume Upload Failed', {
        authenticated: true,
        error: result.error || 'Terjadi kesalahan saat mengunggah resume',
        fileType: file.type,
        fileSize: file.size,
        fileName: file.name
      });
    }
    
    setIsUploading(false);
    // Reset all file inputs with resume-related IDs
    const fileInputs = ['resume', 'resume-existing', 'resume-new'];
    fileInputs.forEach(id => {
      const fileInput = document.getElementById(id) as HTMLInputElement;
      if (fileInput) {
        fileInput.value = '';
      }
    });
  };

  return {
    isUploading,
    uploadSuccess,
    error,
    toast,
    existingResume,
    isLoading,
    isDeleting,
    isGettingResumeUrl,
    setToast,
    handleResumeUpload,
    handleViewResume,
    handleDeleteResume
  };
}
