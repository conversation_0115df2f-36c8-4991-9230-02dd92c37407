import { apiPostFormData } from "@/lib/apiFetch";
import { useState, useEffect, useRef } from 'react';
import { createClient } from '@/lib/supabase';
import type { RealtimePostgresChangesPayload, RealtimeChannel } from '@supabase/supabase-js';
import { convertStructuredDataToPlainText, StructuredLetterData } from '@/types/letter-structured';
import { fillLetterTemplate } from '@/utils/letter-template-engine';
import { getTemplateById } from '@/utils/letter-templates/applicationLetterTemplates';
import { callGenerateLetterEdgeFunctionFromFrontend } from '@/utils/letterEdgeFunction';

/**
 * Client-side wrapper around the letter generation API.
 * This intentionally keeps business logic on the server – the hook merely
 * starts a generation job and polls for its status.
 */
export type LetterGenerationStatus =
  | {
      status: "idle";
    }
  | {
      status: "processing";
      startedAt?: number;
    }
  | {
      status: "done";
      plainText: string;
      designHtml?: string;
      startedAt: number;
      structuredData?: any;
    }
  | {
      status: "error";
      error: string;
      startedAt?: number;
    };

// Types for the letter generation parameters
export interface LetterGenerationParams {
  // Job method and data
  jobDescription?: string;
  jobImage?: File;
  // Resume data for unauthenticated users
  unauthenticatedResumeFile?: File;
  unauthenticatedResumeFileName?: string;
  // Template selection
  templateId?: string;
}

/**
 * Kick off letter generation using the new 2-step flow:
 * 1. Call API route to get required data
 * 2. Call edge function directly from frontend
 * Returns the generation ID, which can be fed into `useGenerationStatus`.
 */
export async function startGeneration(
  params: LetterGenerationParams
): Promise<string> {
  const formData = new FormData();

  // Add job description if provided
  if (params.jobDescription) {
    formData.append('jobDescription', params.jobDescription);
  }

  // Add job image if provided
  if (params.jobImage) {
    formData.append('jobImage', params.jobImage);
  }

  // Add unauthenticated resume file if provided
  if (params.unauthenticatedResumeFile && params.unauthenticatedResumeFileName) {
    formData.append('unauthenticatedResumeFile', params.unauthenticatedResumeFile);
    formData.append('unauthenticatedResumeFileName', params.unauthenticatedResumeFileName);
  }

  // Add template ID
  if (params.templateId) {
    formData.append('templateId', params.templateId);
  }

  const res = await apiPostFormData("/api/generate-application-letter", formData);

  if (!res.ok) {
    const errorResponse = await res.json();
    const errorText = errorResponse.error;
    console.error('API route failed:', errorText);
    throw new Error(errorText || "Gagal memulai proses generate letter.");
  }

  const apiData = await res.json();
  console.log('API route response:', { hasId: !!apiData.id, hasEdgeFunctionData: !!apiData.edgeFunctionData });

  if (!apiData.id || !apiData.edgeFunctionData) {
    throw new Error('API route did not return required data');
  }

  const letterId = apiData.id;
  const edgeFunctionData = apiData.edgeFunctionData;

  callGenerateLetterEdgeFunctionFromFrontend(edgeFunctionData).catch(error => {
    console.error('Edge function call failed:', error);
    // Don't throw here - let the realtime subscription handle the error status
  });

  return letterId;
}

/**
 * Update letter with generated content and deduct tokens
 */
async function updateLetterWithGeneratedContent(
  letterId: string,
  plainText: string,
  designHtml: string | undefined,
  templateId: string,
  structuredData: StructuredLetterData | undefined,
): Promise<void> {
  try {
    const supabase = createClient();

    // Update the letter with generated content
    const { error: updateError } = await supabase
      .from('letters')
      .update({
        plain_text: plainText,
        design_html: designHtml,
        structured_data: structuredData,
        updated_at: new Date().toISOString()
      })
      .eq('id', letterId);

    if (updateError) {
      console.error('Error updating letter with generated content:', updateError);
      return;
    }

    // Deduct tokens after successful generation
    await deductTokensForTemplate(letterId, templateId);
  } catch (error) {
    console.error('Error in updateLetterWithGeneratedContent:', error);
  }
}

/**
 * Deduct tokens for template usage after successful generation
 */
async function deductTokensForTemplate(letterId: string, templateId: string): Promise<void> {
  try {
    const supabase = createClient();

    // Get the user ID from the letter
    const { data: letter, error: letterError } = await supabase
      .from('letters')
      .select('user_id')
      .eq('id', letterId)
      .single();

    if (letterError || !letter) {
      console.error('Error fetching letter for token deduction:', letterError);
      return;
    }

    const userId = letter.user_id;
    const template = getTemplateById(templateId);
    const tokenCost = template?.tokenCost || 0;

    if (tokenCost <= 0) {
      console.log('No tokens to deduct for this template');
      return;
    }

    // Get current token balance
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('tokens')
      .eq('id', userId)
      .single();

    if (profileError || !profile) {
      console.error('Error fetching user profile for token deduction:', profileError);
      return;
    }

    const currentTokens = (profile.tokens as number | null) ?? 0;
    const newTokens = Math.max(currentTokens - tokenCost, 0);

    // Update token balance
    const { error: updateError } = await supabase
      .from('profiles')
      .update({ tokens: newTokens })
      .eq('id', userId);

    if (updateError) {
      console.error('Error updating token balance:', updateError);
      return;
    }

    console.log(`Successfully deducted ${tokenCost} tokens from user ${userId}. New balance: ${newTokens}`);
  } catch (error) {
    console.error('Unexpected error during token deduction:', error);
  }
}

/**
 * React hook that subscribes to letter generation status updates via Supabase realtime.
 */
export function useGenerationStatus(id: string | null): LetterGenerationStatus & { reset: () => void } {
  const [status, setStatus] = useState<LetterGenerationStatus>({ status: "idle" });
  const subscriptionRef = useRef<RealtimeChannel | null>(null);

  const reset = () => {
    // Reset status to idle
    setStatus({ status: "idle" });
  };

  const transformLetterData = async (letter: any): Promise<LetterGenerationStatus> => {
    if (!letter) return { status: "processing" };

    switch (letter.status) {
      case 'done':
        let plainText = letter.plain_text || '';
        let designHtml = letter.design_html || undefined;
        let structuredData = letter.structured_data ? (typeof letter.structured_data === 'string' ? JSON.parse(letter.structured_data) : letter.structured_data) : undefined;

        // If we have structured data but missing plain_text or design_html, generate them
        if (structuredData && (!plainText || !designHtml)) {
          try {
            // Generate plain text if missing
            if (!plainText) {
              plainText = convertStructuredDataToPlainText(structuredData);
            }

            // Generate design HTML if missing
            if (!designHtml) {
              const template = getTemplateById(letter.template_id || 'plain-text');
              if (template) {
                designHtml = fillLetterTemplate(template, structuredData);
              }
            }

            // Update the database with generated content and deduct tokens
            if ((!letter.plain_text && plainText) || (!letter.design_html && designHtml)) {
              await updateLetterWithGeneratedContent(letter.id, plainText, designHtml, letter.template_id, structuredData);
            }
          } catch (error) {
            console.error('Error generating plain text or design HTML:', error);
            // Continue with whatever we have
          }
        }

        return {
          status: "done",
          plainText,
          designHtml,
          startedAt: letter.created_at ? new Date(letter.created_at).getTime() : Date.now(),
          structuredData
        };
      case 'error':
        return {
          status: "error",
          error: letter.error_message || 'Generation failed',
          startedAt: letter.created_at ? new Date(letter.created_at).getTime() : undefined
        };
      case 'processing':
        return {
          status: "processing",
          startedAt: letter.created_at ? new Date(letter.created_at).getTime() : undefined
        };
      default:
        return { status: "processing" };
    }
  };

  const fetchInitialStatus = async (letterId: string) => {
    try {
      const supabase = createClient();

      const { data: letter, error } = await supabase
        .from('letters')
        .select('*')
        .eq('id', letterId)
        .single();

      if (error) {
        console.error('Error fetching letter status:', error);
        setStatus({ status: "error", error: error.message });
        return;
      }

      const transformedStatus = await transformLetterData(letter);
      setStatus(transformedStatus);
    } catch (err) {
      console.error('Error fetching initial status:', err);
      setStatus({ status: "error", error: 'Failed to fetch status' });
    }
  };

  useEffect(() => {
    // Cleanup previous subscription if it exists
    if (subscriptionRef.current) {
      const supabase = createClient();
      supabase.removeChannel(subscriptionRef.current);
      subscriptionRef.current = null;
    }

    if (!id) {
      setStatus({ status: "idle" });
      return;
    }

    // Set initial processing state
    setStatus({ status: "processing" });

    // Fetch initial status
    fetchInitialStatus(id);

    // Set up realtime subscription
    const supabase = createClient();

    const subscription = supabase
      .channel(`letter_${id}_changes`)
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'letters',
          filter: `id=eq.${id}`
        },
        async (payload: RealtimePostgresChangesPayload<any>) => {
          console.log('Realtime letter status change:', payload);

          if (payload.eventType === 'UPDATE') {
            const updatedStatus = await transformLetterData(payload.new);
            setStatus(updatedStatus);
          }
        }
      )
      .subscribe();

    // Store subscription in ref
    subscriptionRef.current = subscription;

    // Cleanup subscription on unmount or id change
    return () => {
      if (subscriptionRef.current) {
        supabase.removeChannel(subscriptionRef.current);
        subscriptionRef.current = null;
      }
    };
  }, [id]);

  return { ...status, reset };
}