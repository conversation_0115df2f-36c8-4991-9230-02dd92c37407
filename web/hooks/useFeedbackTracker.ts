import { useState, useEffect } from 'react';

// Keys for localStorage
const FEATURE_USAGE_KEY = 'gigsta_feature_usage';
const FEEDBACK_SHOWN_KEY = 'gigsta_feedback_shown';

// Feature types
type FeatureType = 'email-application' | 'application-letter' | 'job-match';

/**
 * Hook to track feature usage and determine when to show feedback form
 * Shows feedback form after any feature has been used 3 times and only once
 */
export function useFeedbackTracker(featureType: FeatureType): { 
  shouldShowFeedback: boolean;
  markFeedbackShown: () => void;
  incrementFeatureUsage: () => void;
} {
  const [shouldShowFeedback, setShouldShowFeedback] = useState<boolean>(false);

  // Load feature usage data on mount
  useEffect(() => {
    // Check if feedback has already been shown
    const feedbackShown = localStorage.getItem(FEEDBACK_SHOWN_KEY) === 'true';
    
    // If feedback has already been shown, don't show it again
    if (feedbackShown) {
      return;
    }
    
    // Get current feature usage counts
    const usageData = getFeatureUsage();
    const totalUsage = Object.values(usageData).reduce((sum, count) => sum + count, 0);
    
    // Show feedback if total usage across all features is at least 3
    setShouldShowFeedback(totalUsage >= 3);
  }, []);

  // Mark that feedback has been shown to user
  const markFeedbackShown = () => {
    localStorage.setItem(FEEDBACK_SHOWN_KEY, 'true');
    setShouldShowFeedback(false);
  };

  // Increment usage count for the current feature
  const incrementFeatureUsage = () => {
    const usageData = getFeatureUsage();
    usageData[featureType] = (usageData[featureType] || 0) + 1;
    localStorage.setItem(FEATURE_USAGE_KEY, JSON.stringify(usageData));
    
    // Check if we've reached 3 uses and feedback hasn't been shown yet
    const totalUsage = Object.values(usageData).reduce((sum, count) => sum + count, 0);
    const feedbackShown = localStorage.getItem(FEEDBACK_SHOWN_KEY) === 'true';
    
    if (totalUsage >= 3 && !feedbackShown) {
      setShouldShowFeedback(true);
    }
  };

  // Helper to get current feature usage data from localStorage
  function getFeatureUsage(): Record<FeatureType, number> {
    try {
      const storedData = localStorage.getItem(FEATURE_USAGE_KEY);
      return storedData ? JSON.parse(storedData) : {
        'email-application': 0,
        'application-letter': 0,
        'job-match': 0
      };
    } catch (e) {
      // If there's an error parsing, return default values
      return {
        'email-application': 0,
        'application-letter': 0,
        'job-match': 0
      };
    }
  }

  return { shouldShowFeedback, markFeedbackShown, incrementFeatureUsage };
}
