import { useState, useEffect, useRef } from 'react';
import { createClient } from '@/lib/supabase';
import { useAuth } from '@/hooks/useAuth';
import type { RealtimePostgresChangesPayload, RealtimeChannel } from '@supabase/supabase-js';

export interface EmailHistoryItem {
  id: string;
  subject: string;
  body: string;
  createdAt: string;
}

export function useEmailHistory(auth: ReturnType<typeof useAuth>) {
  const { user, loading: authLoading } = auth;
  const [emails, setEmails] = useState<EmailHistoryItem[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const subscriptionRef = useRef<RealtimeChannel | null>(null);

  const formatEmail = (email: any): EmailHistoryItem => {
    return {
      id: email.id,
      subject: email.subject,
      body: email.body,
      createdAt: email.created_at
    };
  };

  const fetchEmails = async () => {
    if (!user) {
      setEmails([]);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const supabase = createClient();

      const { data: emailsData, error: fetchError } = await supabase
        .from('emails')
        .select('id, subject, body, created_at')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false });

      if (fetchError) {
        throw fetchError;
      }

      const formattedEmails = emailsData?.map(formatEmail) || [];
      setEmails(formattedEmails);
    } catch (err) {
      console.error('Error fetching emails:', err);
      setError('Failed to fetch emails');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    // Cleanup previous subscription if it exists
    if (subscriptionRef.current) {
      const supabase = createClient();
      supabase.removeChannel(subscriptionRef.current);
      subscriptionRef.current = null;
    }

    if (!user) {
      setEmails([]);
      return;
    }

    // Initial fetch
    fetchEmails();

    // Set up realtime subscription
    const supabase = createClient();

    const subscription = supabase
      .channel('emails_changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'emails',
          filter: `user_id=eq.${user.id}`
        },
        (payload: RealtimePostgresChangesPayload<any>) => {
          console.log('Realtime email change:', payload);

          if (payload.eventType === 'INSERT') {
            // Add new email to the beginning of the list
            const newEmail = formatEmail(payload.new);
            setEmails(prev => [newEmail, ...prev]);
          } else if (payload.eventType === 'UPDATE') {
            // Update existing email
            const updatedEmail = formatEmail(payload.new);
            setEmails(prev =>
              prev.map(email =>
                email.id === updatedEmail.id ? updatedEmail : email
              )
            );
          } else if (payload.eventType === 'DELETE') {
            // Remove deleted email
            setEmails(prev =>
              prev.filter(email => email.id !== payload.old.id)
            );
          }
        }
      )
      .subscribe();

    // Store subscription in ref
    subscriptionRef.current = subscription;

    // Cleanup subscription on unmount
    return () => {
      if (subscriptionRef.current) {
        supabase.removeChannel(subscriptionRef.current);
        subscriptionRef.current = null;
      }
    };
  }, [user]);

  return {
    emails,
    isLoading,
    error,
    refetch: fetchEmails
  };
}
