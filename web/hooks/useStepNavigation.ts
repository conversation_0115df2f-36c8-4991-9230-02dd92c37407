'use client';

import { useState, useEffect, useCallback } from 'react';

export interface StepNavigationOptions<T extends number> {
  /** Initial step number */
  initialStep: T;
  /** Total number of steps */
  totalSteps: number;
  /** URL parameter name for the step (default: 'step') */
  stepParam?: string;
  /** Whether to sync with URL parameters (default: true) */
  syncWithURL?: boolean;
  /** Custom validation function for each step */
  onStepValidation?: (currentStep: T, targetStep: T) => Promise<boolean> | boolean;
  /** Callback when step changes */
  onStepChange?: (newStep: T, prevStep: T) => void;
}

export interface StepNavigationReturn<T extends number> {
  /** Current step number */
  currentStep: T;
  /** Navigate to the next step */
  goNext: () => Promise<void>;
  /** Navigate to the previous step */
  goBack: () => void;
  /** Navigate to a specific step */
  goToStep: (step: T) => Promise<void>;
  /** Check if it's the first step */
  isFirstStep: boolean;
  /** Check if it's the last step */
  isLastStep: boolean;
  /** Check if a specific step is completed (before current step) */
  isStepCompleted: (step: number) => boolean;
  /** Check if a specific step is active */
  isStepActive: (step: number) => boolean;
}

/**
 * Custom hook for managing step-based navigation with browser history support
 * 
 * Features:
 * - Browser back/forward button support
 * - URL synchronization
 * - Custom step validation
 * - Step completion tracking
 * 
 * @example
 * ```tsx
 * const stepNav = useStepNavigation({
 *   initialStep: 1 as 1 | 2 | 3,
 *   totalSteps: 3,
 *   onStepValidation: async (current, target) => {
 *     if (current === 1 && target === 2) {
 *       return !!resumeUploaded;
 *     }
 *     return true;
 *   }
 * });
 * ```
 */
export function useStepNavigation<T extends number>({
  initialStep,
  totalSteps,
  stepParam = 'step',
  syncWithURL = true,
  onStepValidation,
  onStepChange,
}: StepNavigationOptions<T>): StepNavigationReturn<T> {
  // Always initialize with the initial step to ensure fresh start
  const [currentStep, setCurrentStep] = useState<T>(initialStep);

  // Always remove step parameter from URL on mount to ensure fresh start
  useEffect(() => {
    if (typeof window === 'undefined' || !syncWithURL) {
      return;
    }

    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.has(stepParam)) {
      // Remove the step parameter from URL
      urlParams.delete(stepParam);
      const newUrl = new URL(window.location.href);
      newUrl.search = urlParams.toString();

      // Replace the current URL without adding to history
      window.history.replaceState(
        null,
        '',
        newUrl.toString()
      );
    }
  }, []); // Empty dependency array - only run once on mount

  // Update URL when step changes
  const updateURL = useCallback((step: T) => {
    if (typeof window === 'undefined' || !syncWithURL) {
      return;
    }

    const url = new URL(window.location.href);
    url.searchParams.set(stepParam, step.toString());
    
    // Use pushState to add to browser history
    window.history.pushState(
      { step },
      '',
      url.toString()
    );
  }, [stepParam, syncWithURL]);

  // Handle browser back/forward navigation
  useEffect(() => {
    if (typeof window === 'undefined' || !syncWithURL) {
      return;
    }

    const handlePopState = () => {
      const urlParams = new URLSearchParams(window.location.search);
      const stepFromURL = urlParams.get(stepParam);
      
      if (stepFromURL) {
        const parsedStep = parseInt(stepFromURL, 10);
        if (parsedStep >= 1 && parsedStep <= totalSteps) {
          const newStep = parsedStep as T;
          const prevStep = currentStep;
          
          setCurrentStep(newStep);
          
          // Call onChange callback if provided
          if (onStepChange) {
            onStepChange(newStep, prevStep);
          }
        }
      } else {
        // No step in URL, go to initial step
        const prevStep = currentStep;
        setCurrentStep(initialStep);
        
        if (onStepChange) {
          onStepChange(initialStep, prevStep);
        }
      }
    };

    window.addEventListener('popstate', handlePopState);
    
    return () => {
      window.removeEventListener('popstate', handlePopState);
    };
  }, [currentStep, initialStep, totalSteps, stepParam, syncWithURL, onStepChange]);

  // Navigate to next step
  const goNext = useCallback(async () => {
    if (currentStep >= totalSteps) {
      return;
    }

    const nextStep = (currentStep + 1) as T;
    
    // Run validation if provided
    if (onStepValidation) {
      const isValid = await onStepValidation(currentStep, nextStep);
      if (!isValid) {
        return;
      }
    }

    const prevStep = currentStep;
    setCurrentStep(nextStep);
    updateURL(nextStep);
    
    // Call onChange callback if provided
    if (onStepChange) {
      onStepChange(nextStep, prevStep);
    }
  }, [currentStep, totalSteps, onStepValidation, updateURL, onStepChange]);

  // Navigate to previous step
  const goBack = useCallback(() => {
    if (currentStep <= 1) {
      return;
    }

    const prevStepNumber = (currentStep - 1) as T;
    const prevStep = currentStep;
    
    setCurrentStep(prevStepNumber);
    updateURL(prevStepNumber);
    
    // Call onChange callback if provided
    if (onStepChange) {
      onStepChange(prevStepNumber, prevStep);
    }
  }, [currentStep, updateURL, onStepChange]);

  // Navigate to specific step
  const goToStep = useCallback(async (step: T) => {
    if (step < 1 || step > totalSteps || step === currentStep) {
      return;
    }

    // Run validation if provided
    if (onStepValidation) {
      const isValid = await onStepValidation(currentStep, step);
      if (!isValid) {
        return;
      }
    }

    const prevStep = currentStep;
    setCurrentStep(step);
    updateURL(step);
    
    // Call onChange callback if provided
    if (onStepChange) {
      onStepChange(step, prevStep);
    }
  }, [currentStep, totalSteps, onStepValidation, updateURL, onStepChange]);

  // Helper functions
  const isFirstStep = currentStep === 1;
  const isLastStep = currentStep === totalSteps;
  const isStepCompleted = useCallback((step: number) => step < currentStep, [currentStep]);
  const isStepActive = useCallback((step: number) => step === currentStep, [currentStep]);

  return {
    currentStep,
    goNext,
    goBack,
    goToStep,
    isFirstStep,
    isLastStep,
    isStepCompleted,
    isStepActive,
  };
}

/**
 * Type helper for creating step types
 * 
 * @example
 * ```tsx
 * type EmailSteps = StepType<1, 3>; // 1 | 2 | 3
 * type LetterSteps = StepType<1, 4>; // 1 | 2 | 3 | 4
 * ```
 */
export type StepType<Min extends number, Max extends number> = Min extends Max
  ? Min
  : Min | StepType<Min extends number ? (Min extends 0 ? 1 : Min extends 1 ? 2 : Min extends 2 ? 3 : Min extends 3 ? 4 : Min extends 4 ? 5 : never) : never, Max>;

/**
 * Predefined step types for common use cases
 */
export type ThreeSteps = 1 | 2 | 3;
export type FourSteps = 1 | 2 | 3 | 4;
export type FiveSteps = 1 | 2 | 3 | 4 | 5;