'use client'

import { useState, useEffect, useRef } from 'react';
import { createClient as createBrowserClient } from '@/lib/supabase';
import { User, SupabaseClient, Provider, AuthWeakPasswordError, AuthApiError } from '@supabase/supabase-js';
import type { RealtimeChannel } from '@supabase/supabase-js';
import mixpanel from '@/lib/mixpanel';

export interface AuthState {
  user: User | null;
  loading: boolean;
  supabase: SupabaseClient;
}

export interface UserProfile {
  id: string;
  tokens: number | null;
  resume_file_name: string | null;
  real_file_name: string | null;
  resume_url: string | null;
  resume_uploaded_at: string | null;
}

type AuthResult = {
  error: Error | null;
  success: boolean;
  message?: string;
}

export function useAuth(): AuthState & {
  signOut: () => Promise<AuthResult>;
  signIn: (email: string, password: string) => Promise<AuthResult>;
  signUp: (email: string, password: string, redirectTo?: string) => Promise<AuthResult>;
  signInWithGoogle: (next?: string) => Promise<AuthResult>;
  signInWithFacebook: (next?: string) => Promise<AuthResult>;
  resetPassword: (email: string) => Promise<AuthResult>;
  updatePassword: (password: string) => Promise<AuthResult>;
  profile: UserProfile | null;
  profileLoading: boolean;
  refreshProfile: () => Promise<void>;
} {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [profileLoading, setProfileLoading] = useState(false);
  // Use ref to track previous user to avoid stale closure
  const previousUserRef = useRef<User | null>(null);
  // Ref to keep track of the realtime channel so we can clean it up properly
  const tokensChannelRef = useRef<RealtimeChannel | null>(null);
  // Browser client for state management and auth state
  const supabase = createBrowserClient();

  // Helper function for OAuth sign in using client-side Supabase SDK
  const signInWithOAuth = async (provider: Provider, next?: string) => {
    mixpanel.track('Social Login Attempt', { provider });

    let redirectTo = `${window.location.origin}/auth/callback`;
    if (next) {
      redirectTo = `${window.location.origin}/auth/callback?next=${next}`;
    }
    
    const { data, error } = await supabase.auth.signInWithOAuth({
      provider,
      options: {
        redirectTo,
        queryParams: {
          access_type: 'offline',
          prompt: 'consent',
        },
        skipBrowserRedirect: true
      },
    });
    if (error) {
      mixpanel.track('Social Login Failed', { provider, error: error.message });
      return { error, success: false, message: error.message };
    }
    // Supabase returns a URL to redirect for OAuth
    return { error: null, success: true, message: data?.url }; // TODO: add field for url instead of using message field
  };

  // Realtime listener for token balance changes
  useEffect(() => {
    // Unsubscribe from previous channel if any
    if (tokensChannelRef.current) {
      supabase.removeChannel(tokensChannelRef.current);
      tokensChannelRef.current = null;
    }

    // Subscribe to updates for current user
    if (user) {
      const channel = supabase.channel('tokens_updates')
        .on(
          'postgres_changes',
          {
            event: '*',
            schema: 'public',
            table: 'profiles',
            filter: `id=eq.${user.id}`
          },
          (payload) => {
            const newTokens = (payload.new as any).tokens as number | null;
            setProfile((prev) => (prev ? { ...prev, tokens: newTokens } : prev));
          }
        )
        .subscribe();

      tokensChannelRef.current = channel;
    }

    // Cleanup on unmount or when user changes
    return () => {
      if (tokensChannelRef.current) {
        supabase.removeChannel(tokensChannelRef.current);
        tokensChannelRef.current = null;
      }
    };
  }, [user]);

  async function fetchUserProfile(userId: string) {
    try {
      setProfileLoading(true);
      const { data, error } = await supabase
        .from('profiles')
        .select('id, tokens, resume_file_name, real_file_name, resume_url, resume_uploaded_at')
        .eq('id', userId)
        .single();

      if (error) {
        console.error('Error fetching user profile:', error);
        setProfile(null);
      } else {
        setProfile(data);
      }
    } catch (error) {
      console.error('Error fetching user profile:', error);
      setProfile(null);
    } finally {
      setProfileLoading(false);
    }
  }

  const refreshProfile = async () => {
    if (user) {
      await fetchUserProfile(user.id);
    }
  };

  useEffect(() => {
    previousUserRef.current = user;
  }, [user]);

  async function getUser() {
    const { data: { user } } = await supabase.auth.getUser();
    setUser(user);

    // Identify user in Mixpanel if they're logged in
    if (user) {
      // Track user in Mixpanel
      mixpanel.identify(user.id);
      mixpanel.people.set({
        $email: user.email,
        $name: user.user_metadata?.full_name || '',
        $last_login: new Date().toISOString()
      });

      // Fetch user profile data
      await fetchUserProfile(user.id);
    } else {
      setProfile(null);
    }

    setLoading(false);
  }

  useEffect(() => {
    // Fetch the user on initial load
    getUser();

    // Listen for auth state changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      (event, session) => {
        const previousUser = previousUserRef.current;
        const currentUser = session?.user || null;
        
        // Update the ref with current user before setting state
        previousUserRef.current = currentUser;
        setUser(currentUser);

        // Track auth state changes
        if (event) {
          if (event === 'SIGNED_IN' && currentUser && (!previousUser || previousUser.id !== currentUser.id)) {
            // Identify user in Mixpanel
            mixpanel.identify(currentUser.id);
            mixpanel.people.set({
              $email: currentUser.email,
              $name: currentUser.user_metadata?.full_name || '',
              $last_login: new Date().toISOString()
            });

            mixpanel.track('User Logged In', {
              method: 'oauth',
              provider: currentUser.app_metadata?.provider || 'unknown',
              user_id: currentUser.id,
              email: currentUser.email
            });

            setTimeout(async () => {
              await fetchUserProfile(currentUser.id);
            }, 0)
          }

          // Reset Mixpanel user and profile on sign out
          if (event === 'SIGNED_OUT') {
            mixpanel.reset();
            setProfile(null);
          }
        }
      }
    );

    // Cleanup the subscription
    return () => {
      subscription.unsubscribe();
    };
  }, []);

  const signOut = async () => {
    try {
      setLoading(true);
      const { error } = await supabase.auth.signOut();
      if (error) {
        return { error, success: false, message: error.message };
      }
      // Clear user state and profile
      setUser(null);
      setProfile(null);
      mixpanel.track('User Logged Out');
      mixpanel.reset();
      return { error: null, success: true };
    } finally {
      setLoading(false);
    }
  };

  const signIn = async (email: string, password: string) => {
    try {
      setLoading(true);
      const { data, error } = await supabase.auth.signInWithPassword({ email, password });
      if (error) {
        mixpanel.track('User Login Failed', { method: 'email', error: error.message });
        let message = 'Terjadi kesalahan saat login';
        if (error instanceof AuthApiError) {
          if (error.code === 'email_not_confirmed') {
            message = 'Email Anda belum dikonfirmasi. Silakan periksa email Anda dan klik link konfirmasi yang telah dikirim.';
          } else {
            message = 'Email atau kata sandi salah';
          }
        }
        return { error, success: false, message: message };
      }

      if (data.session && data.user) {
        setUser(data.user);
        mixpanel.identify(data.user.id);
        mixpanel.people.set({
          $email: data.user.email,
          $name: data.user.user_metadata?.full_name || '',
          $last_login: new Date().toISOString(),
        });
        mixpanel.track('User Logged In', { method: 'email', user_id: data.user.id, email: data.user.email });
      }

      return { error: null, success: true };
    } finally {
      setLoading(false);
    }
  };

  const signUp = async (email: string, password: string, redirectTo?: string) => {
    try {
      setLoading(true);
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: redirectTo ? { emailRedirectTo: redirectTo } : undefined,
      });

      if (error) {
        mixpanel.track('User Register Failed', { error: error.message });
        const message = error instanceof AuthWeakPasswordError ? 'Kata sandi terlalu lemah. Harus terdiri dari minimal 8 karakter, termasuk huruf besar, huruf kecil, angka, dan simbol' : 'Terjadi kesalahan saat mendaftar'
        return { error, success: false, message: message };
      }

      if (data.user) {
        mixpanel.identify(data.user.id);

        mixpanel.track('User Signed Up', {
          method: 'email',
          user_id: data.user.id,
          email: data.user.email
        })
        
        mixpanel.people.set({
          $email: data.user.email,
          $created: new Date().toISOString()
        })
      }

      return {
        error: null,
        success: true,
        message: data.session ? undefined : 'Pendaftaran berhasil, silakan verifikasi email Anda',
      };
    } finally {
      setLoading(false);
    }
  };

  const signInWithGoogle = (next?: string) => signInWithOAuth('google', next);
  const signInWithFacebook = (next?: string) => signInWithOAuth('facebook', next);

  const resetPassword = async (email: string) => {
    mixpanel.track('Password Reset Requested', { email });
    const { data, error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: `${window.location.origin}/reset-password`,
    });
    if (error) {
      return { error, success: false, message: error.message };
    }
    return { error: null, success: true, message: 'Link reset password telah dikirim' };
  };

  const updatePassword = async (password: string) => {
    const { data: { user }, error } = await supabase.auth.updateUser({ password });
    if (error) {
      const message = error instanceof AuthWeakPasswordError ? 'Kata sandi terlalu lemah. Harus terdiri dari minimal 8 karakter, termasuk huruf besar, huruf kecil, angka, dan simbol' : 'Terjadi kesalahan saat mengubah password';
      return { error, success: false, message: message };
    }
    if (user) {
      setUser(user);
    }
    return { error: null, success: true, message: 'Password berhasil diubah' };
  };

  return {
    user,
    loading,
    supabase,
    signOut,
    signIn,
    signUp,
    signInWithGoogle,
    signInWithFacebook,
    resetPassword,
    updatePassword,
    profile,
    profileLoading,
    refreshProfile
  };
}
